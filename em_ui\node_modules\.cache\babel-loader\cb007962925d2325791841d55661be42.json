{"ast": null, "code": "export var Success = function Success(that, msg) {\n  that.$notification['success']({\n    message: '消息',\n    description: msg\n  });\n};\nexport var info = function info(that, msg) {\n  that.$notification['info']({\n    message: '消息',\n    description: msg\n  });\n};\nexport var Warning = function Warning(that, msg) {\n  that.$notification['warning']({\n    message: '消息',\n    description: msg\n  });\n};\nexport var Error = function Error(that, msg) {\n  that.$notification['error']({\n    message: '消息',\n    description: msg\n  });\n};", "map": {"version": 3, "names": ["Success", "that", "msg", "$notification", "message", "description", "info", "Warning", "Error"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/util/message.js"], "sourcesContent": ["export let Success = (that, msg) => {\n  that.$notification['success']({\n    message: '消息',\n    description:\n      msg\n  })\n}\nexport let info = (that, msg) => {\n  that.$notification['info']({\n    message: '消息',\n    description:\n      msg\n  })\n}\nexport let Warning = (that, msg) => {\n  that.$notification['warning']({\n    message: '消息',\n    description:\n      msg\n  })\n}\nexport let Error = (that, msg) => {\n  that.$notification['error']({\n    message: '消息',\n    description:\n      msg\n  })\n}"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG,SAAVA,OAAOA,CAAIC,IAAI,EAAEC,GAAG,EAAK;EAClCD,IAAI,CAACE,aAAa,CAAC,SAAS,CAAC,CAAC;IAC5BC,OAAO,EAAE,IAAI;IACbC,WAAW,EACTH;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAII,IAAI,GAAG,SAAPA,IAAIA,CAAIL,IAAI,EAAEC,GAAG,EAAK;EAC/BD,IAAI,CAACE,aAAa,CAAC,MAAM,CAAC,CAAC;IACzBC,OAAO,EAAE,IAAI;IACbC,WAAW,EACTH;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIK,OAAO,GAAG,SAAVA,OAAOA,CAAIN,IAAI,EAAEC,GAAG,EAAK;EAClCD,IAAI,CAACE,aAAa,CAAC,SAAS,CAAC,CAAC;IAC5BC,OAAO,EAAE,IAAI;IACbC,WAAW,EACTH;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIM,KAAK,GAAG,SAARA,KAAKA,CAAIP,IAAI,EAAEC,GAAG,EAAK;EAChCD,IAAI,CAACE,aAAa,CAAC,OAAO,CAAC,CAAC;IAC1BC,OAAO,EAAE,IAAI;IACbC,WAAW,EACTH;EACJ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}