{"ast": null, "code": "import { getPayRecordOfMonth, payFess } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  name: 'user_pay',\n  data: function data() {\n    return {\n      rTime: rTime,\n      loading: false,\n      payRecord_data_list: []\n    };\n  },\n  created: function created() {\n    this.get_payRecordOfMonth();\n  },\n  methods: {\n    get_payRecordOfMonth: function get_payRecordOfMonth() {\n      var _this = this;\n      getPayRecordOfMonth().then(function (res) {\n        _this.payRecord_data_list = res.data;\n      });\n    },\n    pay_fess: function pay_fess(chargeTypeId) {\n      var _this2 = this;\n      var h = this.$createElement;\n      payFess(chargeTypeId).then(function (res) {\n        _this2.$success({\n          title: '支付物业费用回执',\n          // JSX support\n          content: h(\"div\", [h(\"p\", [\"\\u64CD\\u4F5C\\u6210\\u529F\\uFF01\"])])\n        });\n        _this2.get_payRecordOfMonth();\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAqBA;AACA;AACA;AAGA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QACAC;MACA;IACA;IACAC;MAAA;MAAA;MACAC;QACAC;UACAC;UACA;UACAC;QAKA;QACAF;MACA;IACA;EACA;AACA", "names": ["name", "data", "rTime", "loading", "payRecord_data_list", "created", "methods", "get_payRecordOfMonth", "getPayRecordOfMonth", "_this", "pay_fess", "payFess", "_this2", "title", "content"], "sourceRoot": "src/views/admin/user", "sources": ["user_pay.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-table :loading=\"loading\" :data-source=\"payRecord_data_list\">\n      <a-table-column key=\"chargeName\" title=\"费用类型\" data-index=\"chargeName\" />\n      <a-table-column key=\"chargeMoney\" title=\"所需费用\" data-index=\"chargeMoney\" />\n      <a-table-column key=\"createTime\" title=\"缴费创建时间\" data-index=\"createTime\" >\n        <template slot-scope=\"text, record\">\n          {{rTime(text)}}\n        </template>\n      </a-table-column>\n      <a-table-column key=\"isPayment\" title=\"本月是否缴纳\" data-index=\"isPayment\">\n        <template slot-scope=\"text, record\">\n          <a-tag v-if=\"text\" color=\"red\">已缴纳</a-tag>\n          <a-tag v-else color=\"blue\" @click=\"pay_fess(record.chargeTypeId)\">未缴纳</a-tag>\n        </template>\n      </a-table-column>\n    </a-table>\n  </page-main>\n</template>\n\n<script>\nimport { getPayRecordOfMonth, payFess } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\n\n\nexport default {\n  name: 'user_pay',\n  data () {\n    return {\n      rTime,\n      loading: false,\n      payRecord_data_list: [],\n    }\n  },\n  created () {\n    this.get_payRecordOfMonth()\n  },\n  methods: {\n    get_payRecordOfMonth () {\n      getPayRecordOfMonth().then(res => {\n        this.payRecord_data_list = res.data\n      })\n    },\n    pay_fess (chargeTypeId) {\n      payFess(chargeTypeId).then(res => {\n        this.$success({\n          title: '支付物业费用回执',\n          // JSX support\n          content: (\n            <div>\n              <p>操作成功！</p>\n            </div>\n          ),\n        });\n        this.get_payRecordOfMonth()\n      })\n    }\n  },\n}\n</script>\n"]}, "metadata": {}, "sourceType": "module"}