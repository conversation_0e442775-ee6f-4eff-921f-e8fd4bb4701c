{"ast": null, "code": "export default {\n  name: 'FixedActionBar',\n  data: function data() {\n    return {\n      isBottom: true\n    };\n  },\n  mounted: function mounted() {\n    this.onScroll();\n    window.addEventListener('scroll', this.onScroll);\n  },\n  destroyed: function destroyed() {\n    window.removeEventListener('scroll', this.onScroll);\n  },\n  methods: {\n    onScroll: function onScroll() {\n      // 变量scrollTop是滚动条滚动时，滚动条上端距离顶部的距离\n      var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n      // 变量windowHeight是可视区的高度\n      var windowHeight = document.documentElement.clientHeight || document.body.clientHeight;\n      // 变量scrollHeight是滚动条的总高度（当前可滚动的页面的总高度）\n      var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;\n      // 滚动条到底部\n      if (Math.ceil(scrollTop + windowHeight) >= scrollHeight) {\n        this.isBottom = true;\n      } else {\n        this.isBottom = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAWA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "isBottom", "mounted", "window", "destroyed", "methods", "onScroll"], "sourceRoot": "src/components/FixedActionBar", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div :class=\"{\n        'actionbar': true,\n        'shadow': !isBottom\n    }\"\n    >\n        <slot />\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'FixedActionBar',\n    data() {\n        return {\n            isBottom: true\n        }\n    },\n    mounted() {\n        this.onScroll()\n        window.addEventListener('scroll', this.onScroll)\n    },\n    destroyed() {\n        window.removeEventListener('scroll', this.onScroll)\n    },\n    methods: {\n        onScroll() {\n            // 变量scrollTop是滚动条滚动时，滚动条上端距离顶部的距离\n            var scrollTop = document.documentElement.scrollTop || document.body.scrollTop\n            // 变量windowHeight是可视区的高度\n            var windowHeight = document.documentElement.clientHeight || document.body.clientHeight\n            // 变量scrollHeight是滚动条的总高度（当前可滚动的页面的总高度）\n            var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight\n            // 滚动条到底部\n            if (Math.ceil(scrollTop + windowHeight) >= scrollHeight) {\n                this.isBottom = true\n            } else {\n                this.isBottom = false\n            }\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n[data-mode=mobile] {\n    .actionbar {\n        width: 100%;\n        transform: translateX(-50%);\n    }\n}\n.actionbar {\n    position: fixed;\n    z-index: 4;\n    right: 0;\n    bottom: 0;\n    left: 50%;\n    width: calc(100% - #{$g_sidebar_width});\n    transform: translateX(calc(-50% + #{$g_sidebar_width} * 0.5));\n    padding: 20px;\n    text-align: center;\n    background-color: #fff;\n    transition: all 0.3s, box-shadow 0.5s;\n    box-shadow: 0 0 1px 0 #ccc;\n    &.shadow {\n        box-shadow: 0 -10px 10px -10px #ccc;\n    }\n}\n::v-deep .el-form-item {\n    margin-bottom: 0;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}