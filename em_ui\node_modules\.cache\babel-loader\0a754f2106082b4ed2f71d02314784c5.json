{"ast": null, "code": "export default {\n  name: 'Page<PERSON>eader',\n  props: {\n    title: {\n      type: String,\n      required: true\n    },\n    content: {\n      type: String,\n      default: ''\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAeA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;EACA;AACA", "names": ["name", "props", "title", "type", "required", "content", "default"], "sourceRoot": "src/components/PageHeader", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"header\">\n        <div>\n            <div class=\"title\">{{ title }}</div>\n            <div class=\"content\">\n                <slot name=\"content\">{{ content }}</slot>\n            </div>\n        </div>\n        <div>\n            <slot />\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: '<PERSON>Header',\n    props: {\n        title: {\n            type: String,\n            required: true\n        },\n        content: {\n            type: String,\n            default: ''\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 20px;\n    padding: 16px 20px;\n    background: #fff;\n    border-bottom: 1px solid #e8eaec;\n    .title {\n        font-size: 22px;\n    }\n    .content {\n        color: #909399;\n        font-size: 14px;\n        margin-top: 10px;\n        &:empty {\n            display: none;\n        }\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}