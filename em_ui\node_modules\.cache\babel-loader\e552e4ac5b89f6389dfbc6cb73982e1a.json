{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _toConsumableArray from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _asyncToGenerator from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _objectSpread from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { deepClone } from '@/util';\nfunction hasPermission(permissions, route) {\n  var isAuth = false;\n  if (route.meta && route.meta.auth) {\n    isAuth = permissions.some(function (auth) {\n      if (typeof route.meta.auth == 'string') {\n        return route.meta.auth === auth;\n      } else {\n        return route.meta.auth.some(function (routeAuth) {\n          return routeAuth === auth;\n        });\n      }\n    });\n  } else {\n    isAuth = true;\n  }\n  return isAuth;\n}\nfunction filterAsyncRoutes(routes, permissions) {\n  var res = [];\n  routes.forEach(function (route) {\n    var tmp = _objectSpread({}, route);\n    if (hasPermission(permissions, tmp)) {\n      if (tmp.children) {\n        tmp.children = filterAsyncRoutes(tmp.children, permissions);\n        tmp.children.length && res.push(tmp);\n      } else {\n        res.push(tmp);\n      }\n    }\n  });\n  return res;\n}\n\n// 将多层嵌套路由处理成平级\nfunction flatAsyncRoutes(routes, breadcrumb) {\n  var baseUrl = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';\n  var res = [];\n  routes.forEach(function (route) {\n    var tmp = _objectSpread({}, route);\n    if (tmp.children) {\n      var childrenBaseUrl = '';\n      if (baseUrl == '') {\n        childrenBaseUrl = tmp.path;\n      } else if (tmp.path != '') {\n        childrenBaseUrl = \"\".concat(baseUrl, \"/\").concat(tmp.path);\n      }\n      var childrenBreadcrumb = deepClone(breadcrumb);\n      if (route.meta.breadcrumb !== false) {\n        childrenBreadcrumb.push({\n          path: childrenBaseUrl,\n          title: route.meta.title\n        });\n      }\n      var tmpRoute = deepClone(route);\n      tmpRoute.path = childrenBaseUrl;\n      tmpRoute.meta.breadcrumbNeste = childrenBreadcrumb;\n      delete tmpRoute.children;\n      res.push(tmpRoute);\n      var childrenRoutes = flatAsyncRoutes(tmp.children, childrenBreadcrumb, childrenBaseUrl);\n      childrenRoutes.map(function (item) {\n        // 如果 path 一样则覆盖，因为子路由的 path 可能设置为空，导致和父路由一样，直接注册会提示路由重复\n        if (res.some(function (v) {\n          return v.path == item.path;\n        })) {\n          res.forEach(function (v, i) {\n            if (v.path == item.path) {\n              res[i] = item;\n            }\n          });\n        } else {\n          res.push(item);\n        }\n      });\n    } else {\n      if (baseUrl != '') {\n        if (tmp.path != '') {\n          tmp.path = \"\".concat(baseUrl, \"/\").concat(tmp.path);\n        } else {\n          tmp.path = baseUrl;\n        }\n      }\n      // 处理面包屑导航\n      var tmpBreadcrumb = deepClone(breadcrumb);\n      if (tmp.meta.breadcrumb !== false) {\n        tmpBreadcrumb.push({\n          path: tmp.path,\n          title: tmp.meta.title\n        });\n      }\n      tmp.meta.breadcrumbNeste = tmpBreadcrumb;\n      res.push(tmp);\n    }\n  });\n  return res;\n}\nvar state = {\n  isGenerate: false,\n  routes: [],\n  headerActived: 0\n};\nvar getters = {\n  sidebarRoutes: function sidebarRoutes(state) {\n    return state.routes[state.headerActived].children;\n  }\n};\nvar actions = {\n  // 根据权限动态生成路由\n  generateRoutes: function generateRoutes(_ref, data) {\n    var rootState = _ref.rootState,\n      dispatch = _ref.dispatch,\n      commit = _ref.commit;\n    // eslint-disable-next-line no-async-promise-executor\n    return new Promise(/*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(resolve) {\n        var accessedRoutes, permissions, routes;\n        return _regeneratorRuntime().wrap(function (_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (!rootState.settings.openPermission) {\n                _context.next = 2;\n                break;\n              }\n              _context.next = 1;\n              return dispatch('user/getPermissions', null, {\n                root: true\n              });\n            case 1:\n              permissions = _context.sent;\n              accessedRoutes = filterAsyncRoutes(data.asyncRoutes, permissions);\n              _context.next = 3;\n              break;\n            case 2:\n              accessedRoutes = data.asyncRoutes;\n            case 3:\n              commit('setRoutes', accessedRoutes);\n              commit('setHeaderActived', data.currentPath);\n              routes = [];\n              accessedRoutes.map(function (item) {\n                routes.push.apply(routes, _toConsumableArray(item.children));\n              });\n              if (rootState.settings.enableFlatRoutes) {\n                routes.map(function (item) {\n                  if (item.children) {\n                    item.children = flatAsyncRoutes(item.children, [{\n                      path: item.path,\n                      title: item.meta.title\n                    }]);\n                  }\n                });\n              }\n              resolve(routes);\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n  }\n};\nvar mutations = {\n  invalidRoutes: function invalidRoutes(state) {\n    state.isGenerate = false;\n    state.headerActived = 0;\n  },\n  setRoutes: function setRoutes(state, routes) {\n    state.isGenerate = true;\n    var newRoutes = deepClone(routes);\n    state.routes = newRoutes.filter(function (item) {\n      return item.children.length != 0;\n    });\n  },\n  // 根据路由判断属于哪个头部导航\n  setHeaderActived: function setHeaderActived(state, path) {\n    state.routes.map(function (item, index) {\n      if (item.children.some(function (r) {\n        return path.indexOf(r.path + '/') === 0 || path == r.path;\n      })) {\n        state.headerActived = index;\n      }\n    });\n  },\n  // 切换头部导航\n  switchHeaderActived: function switchHeaderActived(state, index) {\n    state.headerActived = index;\n  }\n};\nexport default {\n  namespaced: true,\n  state: state,\n  actions: actions,\n  getters: getters,\n  mutations: mutations\n};", "map": {"version": 3, "names": ["deepClone", "hasPermission", "permissions", "route", "isAuth", "meta", "auth", "some", "routeAuth", "filterAsyncRoutes", "routes", "res", "for<PERSON>ach", "tmp", "_objectSpread", "children", "length", "push", "flatAsyncRoutes", "breadcrumb", "baseUrl", "arguments", "undefined", "childrenBaseUrl", "path", "concat", "childrenBreadcrumb", "title", "tmpRoute", "breadcrumbNeste", "childrenRoutes", "map", "item", "v", "i", "tmpBreadcrumb", "state", "isGenerate", "headerActived", "getters", "sidebarRoutes", "actions", "generateRoutes", "_ref", "data", "rootState", "dispatch", "commit", "Promise", "_ref2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "resolve", "accessedRoutes", "wrap", "_context", "prev", "next", "settings", "openPermission", "root", "sent", "asyncRoutes", "currentPath", "apply", "_toConsumableArray", "enableFlatRoutes", "stop", "_x", "mutations", "invalidRoutes", "setRoutes", "newRoutes", "filter", "setHeaderActived", "index", "r", "indexOf", "switchHeaderActived", "namespaced"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/store/modules/menu.js"], "sourcesContent": ["import { deepClone } from '@/util'\n\nfunction hasPermission(permissions, route) {\n    let isAuth = false\n    if (route.meta && route.meta.auth) {\n        isAuth = permissions.some(auth => {\n            if (typeof route.meta.auth == 'string') {\n                return route.meta.auth === auth\n            } else {\n                return route.meta.auth.some(routeAuth => {\n                    return routeAuth === auth\n                })\n            }\n        })\n    } else {\n        isAuth = true\n    }\n    return isAuth\n}\n\nfunction filterAsyncRoutes(routes, permissions) {\n    const res = []\n    routes.forEach(route => {\n        const tmp = { ...route }\n        if (hasPermission(permissions, tmp)) {\n            if (tmp.children) {\n                tmp.children = filterAsyncRoutes(tmp.children, permissions)\n                tmp.children.length && res.push(tmp)\n            } else {\n                res.push(tmp)\n            }\n        }\n    })\n    return res\n}\n\n// 将多层嵌套路由处理成平级\nfunction flatAsyncRoutes(routes, breadcrumb, baseUrl = '') {\n    let res = []\n    routes.forEach(route => {\n        const tmp = { ...route }\n        if (tmp.children) {\n            let childrenBaseUrl = ''\n            if (baseUrl == '') {\n                childrenBaseUrl = tmp.path\n            } else if (tmp.path != '') {\n                childrenBaseUrl = `${baseUrl}/${tmp.path}`\n            }\n            let childrenBreadcrumb = deepClone(breadcrumb)\n            if (route.meta.breadcrumb !== false) {\n                childrenBreadcrumb.push({\n                    path: childrenBaseUrl,\n                    title: route.meta.title\n                })\n            }\n            let tmpRoute = deepClone(route)\n            tmpRoute.path = childrenBaseUrl\n            tmpRoute.meta.breadcrumbNeste = childrenBreadcrumb\n            delete tmpRoute.children\n            res.push(tmpRoute)\n            let childrenRoutes = flatAsyncRoutes(tmp.children, childrenBreadcrumb, childrenBaseUrl)\n            childrenRoutes.map(item => {\n                // 如果 path 一样则覆盖，因为子路由的 path 可能设置为空，导致和父路由一样，直接注册会提示路由重复\n                if (res.some(v => v.path == item.path)) {\n                    res.forEach((v, i) => {\n                        if (v.path == item.path) {\n                            res[i] = item\n                        }\n                    })\n                } else {\n                    res.push(item)\n                }\n            })\n        } else {\n            if (baseUrl != '') {\n                if (tmp.path != '') {\n                    tmp.path = `${baseUrl}/${tmp.path}`\n                } else {\n                    tmp.path = baseUrl\n                }\n            }\n            // 处理面包屑导航\n            let tmpBreadcrumb = deepClone(breadcrumb)\n            if (tmp.meta.breadcrumb !== false) {\n                tmpBreadcrumb.push({\n                    path: tmp.path,\n                    title: tmp.meta.title\n                })\n            }\n            tmp.meta.breadcrumbNeste = tmpBreadcrumb\n            res.push(tmp)\n        }\n    })\n    return res\n}\n\nconst state = {\n    isGenerate: false,\n    routes: [],\n    headerActived: 0\n}\n\nconst getters = {\n    sidebarRoutes: state => {\n        return state.routes[state.headerActived].children\n    }\n}\n\nconst actions = {\n    // 根据权限动态生成路由\n    generateRoutes({rootState, dispatch, commit}, data) {\n        // eslint-disable-next-line no-async-promise-executor\n        return new Promise(async resolve => {\n            let accessedRoutes\n            // 判断权限功能是否开启\n            if (rootState.settings.openPermission) {\n                const permissions = await dispatch('user/getPermissions', null, { root: true })\n                accessedRoutes = filterAsyncRoutes(data.asyncRoutes, permissions)\n            } else {\n                accessedRoutes = data.asyncRoutes\n            }\n            commit('setRoutes', accessedRoutes)\n            commit('setHeaderActived', data.currentPath)\n            let routes = []\n            accessedRoutes.map(item => {\n                routes.push(...item.children)\n            })\n            if (rootState.settings.enableFlatRoutes) {\n                routes.map(item => {\n                    if (item.children) {\n                        item.children = flatAsyncRoutes(item.children, [{\n                            path: item.path,\n                            title: item.meta.title\n                        }])\n                    }\n                })\n            }\n            resolve(routes)\n        })\n    }\n}\n\nconst mutations = {\n    invalidRoutes(state) {\n        state.isGenerate = false\n        state.headerActived = 0\n    },\n    setRoutes(state, routes) {\n        state.isGenerate = true\n        let newRoutes = deepClone(routes)\n        state.routes = newRoutes.filter(item => {\n            return item.children.length != 0\n        })\n    },\n    // 根据路由判断属于哪个头部导航\n    setHeaderActived(state, path) {\n        state.routes.map((item, index) => {\n            if (\n                item.children.some(r => {\n                    return path.indexOf(r.path + '/') === 0 || path == r.path\n                })\n            ) {\n                state.headerActived = index\n            }\n        })\n    },\n    // 切换头部导航\n    switchHeaderActived(state, index) {\n        state.headerActived = index\n    }\n}\n\nexport default {\n    namespaced: true,\n    state,\n    actions,\n    getters,\n    mutations\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,QAAQ;AAElC,SAASC,aAAaA,CAACC,WAAW,EAAEC,KAAK,EAAE;EACvC,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAID,KAAK,CAACE,IAAI,IAAIF,KAAK,CAACE,IAAI,CAACC,IAAI,EAAE;IAC/BF,MAAM,GAAGF,WAAW,CAACK,IAAI,CAAC,UAAAD,IAAI,EAAI;MAC9B,IAAI,OAAOH,KAAK,CAACE,IAAI,CAACC,IAAI,IAAI,QAAQ,EAAE;QACpC,OAAOH,KAAK,CAACE,IAAI,CAACC,IAAI,KAAKA,IAAI;MACnC,CAAC,MAAM;QACH,OAAOH,KAAK,CAACE,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,UAAAC,SAAS,EAAI;UACrC,OAAOA,SAAS,KAAKF,IAAI;QAC7B,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN,CAAC,MAAM;IACHF,MAAM,GAAG,IAAI;EACjB;EACA,OAAOA,MAAM;AACjB;AAEA,SAASK,iBAAiBA,CAACC,MAAM,EAAER,WAAW,EAAE;EAC5C,IAAMS,GAAG,GAAG,EAAE;EACdD,MAAM,CAACE,OAAO,CAAC,UAAAT,KAAK,EAAI;IACpB,IAAMU,GAAG,GAAAC,aAAA,KAAQX,KAAK,CAAE;IACxB,IAAIF,aAAa,CAACC,WAAW,EAAEW,GAAG,CAAC,EAAE;MACjC,IAAIA,GAAG,CAACE,QAAQ,EAAE;QACdF,GAAG,CAACE,QAAQ,GAAGN,iBAAiB,CAACI,GAAG,CAACE,QAAQ,EAAEb,WAAW,CAAC;QAC3DW,GAAG,CAACE,QAAQ,CAACC,MAAM,IAAIL,GAAG,CAACM,IAAI,CAACJ,GAAG,CAAC;MACxC,CAAC,MAAM;QACHF,GAAG,CAACM,IAAI,CAACJ,GAAG,CAAC;MACjB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOF,GAAG;AACd;;AAEA;AACA,SAASO,eAAeA,CAACR,MAAM,EAAES,UAAU,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAL,MAAA,QAAAK,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;EACrD,IAAIV,GAAG,GAAG,EAAE;EACZD,MAAM,CAACE,OAAO,CAAC,UAAAT,KAAK,EAAI;IACpB,IAAMU,GAAG,GAAAC,aAAA,KAAQX,KAAK,CAAE;IACxB,IAAIU,GAAG,CAACE,QAAQ,EAAE;MACd,IAAIQ,eAAe,GAAG,EAAE;MACxB,IAAIH,OAAO,IAAI,EAAE,EAAE;QACfG,eAAe,GAAGV,GAAG,CAACW,IAAI;MAC9B,CAAC,MAAM,IAAIX,GAAG,CAACW,IAAI,IAAI,EAAE,EAAE;QACvBD,eAAe,MAAAE,MAAA,CAAML,OAAO,OAAAK,MAAA,CAAIZ,GAAG,CAACW,IAAI,CAAE;MAC9C;MACA,IAAIE,kBAAkB,GAAG1B,SAAS,CAACmB,UAAU,CAAC;MAC9C,IAAIhB,KAAK,CAACE,IAAI,CAACc,UAAU,KAAK,KAAK,EAAE;QACjCO,kBAAkB,CAACT,IAAI,CAAC;UACpBO,IAAI,EAAED,eAAe;UACrBI,KAAK,EAAExB,KAAK,CAACE,IAAI,CAACsB;QACtB,CAAC,CAAC;MACN;MACA,IAAIC,QAAQ,GAAG5B,SAAS,CAACG,KAAK,CAAC;MAC/ByB,QAAQ,CAACJ,IAAI,GAAGD,eAAe;MAC/BK,QAAQ,CAACvB,IAAI,CAACwB,eAAe,GAAGH,kBAAkB;MAClD,OAAOE,QAAQ,CAACb,QAAQ;MACxBJ,GAAG,CAACM,IAAI,CAACW,QAAQ,CAAC;MAClB,IAAIE,cAAc,GAAGZ,eAAe,CAACL,GAAG,CAACE,QAAQ,EAAEW,kBAAkB,EAAEH,eAAe,CAAC;MACvFO,cAAc,CAACC,GAAG,CAAC,UAAAC,IAAI,EAAI;QACvB;QACA,IAAIrB,GAAG,CAACJ,IAAI,CAAC,UAAA0B,CAAC;UAAA,OAAIA,CAAC,CAACT,IAAI,IAAIQ,IAAI,CAACR,IAAI;QAAA,EAAC,EAAE;UACpCb,GAAG,CAACC,OAAO,CAAC,UAACqB,CAAC,EAAEC,CAAC,EAAK;YAClB,IAAID,CAAC,CAACT,IAAI,IAAIQ,IAAI,CAACR,IAAI,EAAE;cACrBb,GAAG,CAACuB,CAAC,CAAC,GAAGF,IAAI;YACjB;UACJ,CAAC,CAAC;QACN,CAAC,MAAM;UACHrB,GAAG,CAACM,IAAI,CAACe,IAAI,CAAC;QAClB;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACH,IAAIZ,OAAO,IAAI,EAAE,EAAE;QACf,IAAIP,GAAG,CAACW,IAAI,IAAI,EAAE,EAAE;UAChBX,GAAG,CAACW,IAAI,MAAAC,MAAA,CAAML,OAAO,OAAAK,MAAA,CAAIZ,GAAG,CAACW,IAAI,CAAE;QACvC,CAAC,MAAM;UACHX,GAAG,CAACW,IAAI,GAAGJ,OAAO;QACtB;MACJ;MACA;MACA,IAAIe,aAAa,GAAGnC,SAAS,CAACmB,UAAU,CAAC;MACzC,IAAIN,GAAG,CAACR,IAAI,CAACc,UAAU,KAAK,KAAK,EAAE;QAC/BgB,aAAa,CAAClB,IAAI,CAAC;UACfO,IAAI,EAAEX,GAAG,CAACW,IAAI;UACdG,KAAK,EAAEd,GAAG,CAACR,IAAI,CAACsB;QACpB,CAAC,CAAC;MACN;MACAd,GAAG,CAACR,IAAI,CAACwB,eAAe,GAAGM,aAAa;MACxCxB,GAAG,CAACM,IAAI,CAACJ,GAAG,CAAC;IACjB;EACJ,CAAC,CAAC;EACF,OAAOF,GAAG;AACd;AAEA,IAAMyB,KAAK,GAAG;EACVC,UAAU,EAAE,KAAK;EACjB3B,MAAM,EAAE,EAAE;EACV4B,aAAa,EAAE;AACnB,CAAC;AAED,IAAMC,OAAO,GAAG;EACZC,aAAa,EAAE,SAAfA,aAAaA,CAAEJ,KAAK,EAAI;IACpB,OAAOA,KAAK,CAAC1B,MAAM,CAAC0B,KAAK,CAACE,aAAa,CAAC,CAACvB,QAAQ;EACrD;AACJ,CAAC;AAED,IAAM0B,OAAO,GAAG;EACZ;EACAC,cAAc,WAAdA,cAAcA,CAAAC,IAAA,EAAgCC,IAAI,EAAE;IAAA,IAApCC,SAAS,GAAAF,IAAA,CAATE,SAAS;MAAEC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;MAAEC,MAAM,GAAAJ,IAAA,CAANI,MAAM;IACvC;IACA,OAAO,IAAIC,OAAO;MAAA,IAAAC,KAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAC,SAAAC,QAAMC,OAAO;QAAA,IAAAC,cAAA,EAAArD,WAAA,EAAAQ,MAAA;QAAA,OAAAyC,mBAAA,GAAAK,IAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,KAGxBd,SAAS,CAACe,QAAQ,CAACC,cAAc;gBAAAJ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACPb,QAAQ,CAAC,qBAAqB,EAAE,IAAI,EAAE;gBAAEgB,IAAI,EAAE;cAAK,CAAC,CAAC;YAAA;cAAzE5D,WAAW,GAAAuD,QAAA,CAAAM,IAAA;cACjBR,cAAc,GAAG9C,iBAAiB,CAACmC,IAAI,CAACoB,WAAW,EAAE9D,WAAW,CAAC;cAAAuD,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEjEJ,cAAc,GAAGX,IAAI,CAACoB,WAAW;YAAA;cAErCjB,MAAM,CAAC,WAAW,EAAEQ,cAAc,CAAC;cACnCR,MAAM,CAAC,kBAAkB,EAAEH,IAAI,CAACqB,WAAW,CAAC;cACxCvD,MAAM,GAAG,EAAE;cACf6C,cAAc,CAACxB,GAAG,CAAC,UAAAC,IAAI,EAAI;gBACvBtB,MAAM,CAACO,IAAI,CAAAiD,KAAA,CAAXxD,MAAM,EAAAyD,kBAAA,CAASnC,IAAI,CAACjB,QAAQ,EAAC;cACjC,CAAC,CAAC;cACF,IAAI8B,SAAS,CAACe,QAAQ,CAACQ,gBAAgB,EAAE;gBACrC1D,MAAM,CAACqB,GAAG,CAAC,UAAAC,IAAI,EAAI;kBACf,IAAIA,IAAI,CAACjB,QAAQ,EAAE;oBACfiB,IAAI,CAACjB,QAAQ,GAAGG,eAAe,CAACc,IAAI,CAACjB,QAAQ,EAAE,CAAC;sBAC5CS,IAAI,EAAEQ,IAAI,CAACR,IAAI;sBACfG,KAAK,EAAEK,IAAI,CAAC3B,IAAI,CAACsB;oBACrB,CAAC,CAAC,CAAC;kBACP;gBACJ,CAAC,CAAC;cACN;cACA2B,OAAO,CAAC5C,MAAM,CAAC;YAAA;YAAA;cAAA,OAAA+C,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA,CAClB;MAAA,iBAAAiB,EAAA;QAAA,OAAArB,KAAA,CAAAiB,KAAA,OAAA7C,SAAA;MAAA;IAAA,IAAC;EACN;AACJ,CAAC;AAED,IAAMkD,SAAS,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAACpC,KAAK,EAAE;IACjBA,KAAK,CAACC,UAAU,GAAG,KAAK;IACxBD,KAAK,CAACE,aAAa,GAAG,CAAC;EAC3B,CAAC;EACDmC,SAAS,WAATA,SAASA,CAACrC,KAAK,EAAE1B,MAAM,EAAE;IACrB0B,KAAK,CAACC,UAAU,GAAG,IAAI;IACvB,IAAIqC,SAAS,GAAG1E,SAAS,CAACU,MAAM,CAAC;IACjC0B,KAAK,CAAC1B,MAAM,GAAGgE,SAAS,CAACC,MAAM,CAAC,UAAA3C,IAAI,EAAI;MACpC,OAAOA,IAAI,CAACjB,QAAQ,CAACC,MAAM,IAAI,CAAC;IACpC,CAAC,CAAC;EACN,CAAC;EACD;EACA4D,gBAAgB,WAAhBA,gBAAgBA,CAACxC,KAAK,EAAEZ,IAAI,EAAE;IAC1BY,KAAK,CAAC1B,MAAM,CAACqB,GAAG,CAAC,UAACC,IAAI,EAAE6C,KAAK,EAAK;MAC9B,IACI7C,IAAI,CAACjB,QAAQ,CAACR,IAAI,CAAC,UAAAuE,CAAC,EAAI;QACpB,OAAOtD,IAAI,CAACuD,OAAO,CAACD,CAAC,CAACtD,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAIA,IAAI,IAAIsD,CAAC,CAACtD,IAAI;MAC7D,CAAC,CAAC,EACJ;QACEY,KAAK,CAACE,aAAa,GAAGuC,KAAK;MAC/B;IACJ,CAAC,CAAC;EACN,CAAC;EACD;EACAG,mBAAmB,WAAnBA,mBAAmBA,CAAC5C,KAAK,EAAEyC,KAAK,EAAE;IAC9BzC,KAAK,CAACE,aAAa,GAAGuC,KAAK;EAC/B;AACJ,CAAC;AAED,eAAe;EACXI,UAAU,EAAE,IAAI;EAChB7C,KAAK,EAALA,KAAK;EACLK,OAAO,EAAPA,OAAO;EACPF,OAAO,EAAPA,OAAO;EACPgC,SAAS,EAATA;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}