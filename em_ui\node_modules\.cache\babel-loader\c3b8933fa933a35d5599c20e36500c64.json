{"ast": null, "code": "export default {\n  name: 'AbsoluteContainer',\n  props: {},\n  data: function data() {\n    return {};\n  },\n  mounted: function mounted() {},\n  methods: {}\n};", "map": {"version": 3, "mappings": "AAOA;EACAA;EACAC;EACAC;IACA;EACA;EACAC;EACAC;AACA", "names": ["name", "props", "data", "mounted", "methods"], "sourceRoot": "src/components/AbsoluteContainer", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"absolute-container\">\n        <slot />\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'AbsoluteContainer',\n    props: {},\n    data() {\n        return {}\n    },\n    mounted() {},\n    methods: {}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.absolute-container {\n    position: absolute;\n    width: 100%;\n    height: calc(100% - #{$g-topbar-height});\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}