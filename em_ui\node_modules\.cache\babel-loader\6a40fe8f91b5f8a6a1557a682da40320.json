{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { login, isAdmin } from '@/api/requests/rq-manage.js';\nexport default {\n  name: 'home',\n  data: function data() {\n    return {\n      login_img_src: 'http://localhost:8082/login/code',\n      login_form_data: {\n        userName: '',\n        passWord: '',\n        code: ''\n      },\n      loginLoading: false,\n      loginRules: {\n        userName: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        passWord: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }],\n        code: [{\n          required: true,\n          message: '请输入验证码',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    var _this = this;\n    if (localStorage.getItem(\"isLogin\")) {\n      this.$store.dispatch('user/login', this.form).then(function (res) {\n        _this.$router.push('/');\n      });\n    }\n  },\n  methods: {\n    home_login: function home_login() {\n      var _this2 = this;\n      var h = this.$createElement;\n      login(this.login_form_data).then(function (res) {\n        if (res.code == 200) {\n          _this2.$success({\n            title: '登录成功',\n            content: h(\"div\", [h(\"p\", [\"\\u63A5\\u4E0B\\u6765\\u5373\\u5C06\\u8DF3\\u8F6C\\u5230\\u540E\\u53F0\\u754C\\u9762...\"])])\n          });\n          localStorage.setItem(\"username\", res.data.userName);\n          _this2.$store.dispatch('user/login', _this2.form).then(function (res) {\n            setTimeout(function () {\n              window.location.href = '/';\n            }, 1000);\n          });\n        } else {\n          _this2.$error({\n            title: '\b登录失败',\n            content: res.msg\n          });\n        }\n      });\n    },\n    refreshCaptcha: function refreshCaptcha() {\n      this.login_img_src = 'http://localhost:8082/login/code?time=' + new Date().getTime();\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AA6FA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAJ,WACA;UAAAK;UAAAC;UAAAC;QAAA,EACA;QACAN,WACA;UAAAI;UAAAC;UAAAC;QAAA,EACA;QACAL,OACA;UAAAG;UAAAC;UAAAC;QAAA;MAEA;IACA;EACA;EACAC;IAAA;IACA;MAEA;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MAAA;MAAA;MACAC;QACA;UACAC;YACAC;YACAC;UAKA;UACAC;UACAH;YACAI;cACAC;YACA;UAEA;QACA;UACAL;YACAC;YACAC;UACA;QACA;MACA;IACA;IACAI;MACA;IACA;EACA;AACA", "names": ["name", "data", "login_img_src", "login_form_data", "userName", "passWord", "code", "loginLoading", "loginRules", "required", "message", "trigger", "created", "_this", "methods", "home_login", "login", "_this2", "title", "content", "localStorage", "setTimeout", "window", "refreshCaptcha"], "sourceRoot": "src/views", "sources": ["home.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-background\">\n      <div class=\"login-overlay\"></div>\n    </div>\n\n    <div class=\"login-content\">\n      <div class=\"login-card\">\n        <div class=\"login-header\">\n          <div class=\"logo-section\">\n            <div class=\"logo-icon\">\n              <png-icon name=\"building\" size=\"40\" />\n            </div>\n            <h1 class=\"system-title\">小区物业管理系统</h1>\n            <p class=\"system-subtitle\">Property Management System</p>\n          </div>\n        </div>\n\n        <div class=\"login-form\">\n          <el-form :model=\"login_form_data\" :rules=\"loginRules\" ref=\"loginForm\">\n            <el-form-item prop=\"userName\">\n              <el-input\n                v-model=\"login_form_data.userName\"\n                placeholder=\"请输入用户名\"\n                prefix-icon=\"el-icon-user\"\n                size=\"large\"\n                class=\"login-input\">\n              </el-input>\n            </el-form-item>\n\n            <el-form-item prop=\"passWord\">\n              <el-input\n                v-model=\"login_form_data.passWord\"\n                type=\"password\"\n                placeholder=\"请输入密码\"\n                prefix-icon=\"el-icon-lock\"\n                size=\"large\"\n                class=\"login-input\"\n                show-password>\n              </el-input>\n            </el-form-item>\n\n            <el-form-item prop=\"code\">\n              <el-row :gutter=\"10\">\n                <el-col :span=\"14\">\n                  <el-input\n                    v-model=\"login_form_data.code\"\n                    placeholder=\"请输入验证码\"\n                    prefix-icon=\"el-icon-key\"\n                    size=\"large\"\n                    class=\"login-input\">\n                  </el-input>\n                </el-col>\n                <el-col :span=\"10\">\n                  <img\n                    @click=\"refreshCaptcha\"\n                    :src=\"login_img_src\"\n                    class=\"captcha-img\"\n                    title=\"点击刷新验证码\"\n                  />\n                </el-col>\n              </el-row>\n            </el-form-item>\n\n            <el-form-item>\n              <el-button\n                type=\"primary\"\n                size=\"large\"\n                class=\"login-button\"\n                :loading=\"loginLoading\"\n                @click=\"home_login()\">\n                {{ loginLoading ? '登录中...' : '登录' }}\n              </el-button>\n            </el-form-item>\n          </el-form>\n\n          <div class=\"login-tips\">\n            <p>默认账号密码：</p>\n            <p>管理员：admin / 123456</p>\n            <p>普通用户：test / 123456</p>\n          </div>\n\n          <div class=\"register-link\">\n            <p>还没有账户？<router-link to=\"/register\">立即注册</router-link></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n</template>\n\n<script>\nimport { login, isAdmin } from '@/api/requests/rq-manage.js'\n\nexport default {\n  name: 'home',\n  data () {\n    return {\n      login_img_src: 'http://localhost:8082/login/code',\n      login_form_data: {\n        userName: '',\n        passWord: '',\n        code: ''\n      },\n      loginLoading: false,\n      loginRules: {\n        userName: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        passWord: [\n          { required: true, message: '请输入密码', trigger: 'blur' }\n        ],\n        code: [\n          { required: true, message: '请输入验证码', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  created () {\n    if (localStorage.getItem(\"isLogin\")) {\n\n      this.$store.dispatch('user/login', this.form).then(res => {\n        this.$router.push('/')\n      })\n\n    }\n  },\n  methods: {\n    home_login () {\n      login(this.login_form_data).then(res => {\n        if (res.code == 200) {\n          this.$success({\n            title: '登录成功',\n            content: (\n              <div>\n                <p>接下来即将跳转到后台界面...</p>\n              </div>\n            ),\n          });\n          localStorage.setItem(\"username\", res.data.userName)\n          this.$store.dispatch('user/login', this.form).then(res => {\n              setTimeout(() => {\n            window.location.href = '/'\n          }, 1000)\n\n          })\n        } else {\n          this.$error({\n            title: '\b登录失败',\n            content: res.msg,\n          });\n        }\n      })\n    },\n    refreshCaptcha() {\n      this.login_img_src = 'http://localhost:8082/login/code?time=' + new Date().getTime()\n    }\n  },\n}\n\n\n\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n}\n\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n    opacity: 0.3;\n  }\n}\n\n.login-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.login-content {\n  position: relative;\n  z-index: 10;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  padding: 20px;\n}\n\n.login-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 450px;\n  animation: slideUp 0.6s ease-out;\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.logo-section {\n  .logo-icon {\n    width: 80px;\n    height: 80px;\n    margin: 0 auto 20px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    i {\n      font-size: 40px;\n      color: white;\n    }\n\n    .png-icon {\n      filter: brightness(0) invert(1);\n    }\n  }\n\n  .system-title {\n    font-size: 28px;\n    font-weight: bold;\n    color: #2c3e50;\n    margin: 0 0 10px 0;\n  }\n\n  .system-subtitle {\n    font-size: 14px;\n    color: #7f8c8d;\n    margin: 0;\n  }\n}\n\n.login-form {\n  .login-input {\n    margin-bottom: 20px;\n\n    ::v-deep .el-input__inner {\n      height: 50px;\n      border-radius: 10px;\n      border: 2px solid #e9ecef;\n      font-size: 16px;\n      transition: all 0.3s ease;\n\n      &:focus {\n        border-color: #667eea;\n        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n      }\n    }\n\n    ::v-deep .el-input__prefix {\n      left: 15px;\n      color: #667eea;\n    }\n  }\n\n  .captcha-img {\n    height: 50px;\n    width: 100%;\n    border-radius: 10px;\n    border: 2px solid #e9ecef;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #667eea;\n    }\n  }\n\n  .login-button {\n    width: 100%;\n    height: 50px;\n    border-radius: 10px;\n    font-size: 16px;\n    font-weight: bold;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border: none;\n    transition: all 0.3s ease;\n\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\n    }\n  }\n}\n\n.login-tips {\n  margin-top: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 10px;\n  text-align: center;\n\n  p {\n    margin: 5px 0;\n    font-size: 14px;\n    color: #6c757d;\n\n    &:first-child {\n      font-weight: bold;\n      color: #495057;\n    }\n  }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}