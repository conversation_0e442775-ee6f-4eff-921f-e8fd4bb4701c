{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport axios from 'axios';\n// import Qs from 'qs'\nimport router from '@/router/index';\nimport store from '@/store/index';\nvar api = axios.create({\n  baseURL: process.env.VUE_APP_API_ROOT,\n  timeout: 10000,\n  responseType: 'blob'\n  // withCredentials: true\n});\napi.interceptors.request.use(function (request) {\n  request.headers;\n  request.headers.Authorization = store.state.user.token;\n  return request;\n});\napi.interceptors.response.use(function (response) {\n  // 如果接口请求时发现 token 失效，则立马跳转到登录页\n  if (response.data.code == 401) {\n    toLogin();\n    return false;\n  }\n  return Promise.resolve(response);\n}, function (error) {\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "router", "store", "api", "create", "baseURL", "process", "env", "VUE_APP_API_ROOT", "timeout", "responseType", "interceptors", "request", "use", "headers", "Authorization", "state", "user", "token", "response", "data", "code", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "error", "reject"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/api/down.js"], "sourcesContent": ["import axios from 'axios'\n// import Qs from 'qs'\nimport router from '@/router/index'\nimport store from '@/store/index'\n\n\nconst api = axios.create({\n  baseURL: process.env.VUE_APP_API_ROOT,\n  timeout: 10000,\n  responseType: 'blob',\n  // withCredentials: true\n})\n\napi.interceptors.request.use(\n  request => {\n    request.headers\n    request.headers.Authorization = store.state.user.token\n    return request\n  }\n)\n\napi.interceptors.response.use(\n  response => {\n    // 如果接口请求时发现 token 失效，则立马跳转到登录页\n    if (response.data.code == 401) {\n      toLogin()\n      return false\n    }\n    return Promise.resolve(response)\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\nexport default api\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,KAAK,MAAM,eAAe;AAGjC,IAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrCC,OAAO,EAAE,KAAK;EACdC,YAAY,EAAE;EACd;AACF,CAAC,CAAC;AAEFP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1B,UAAAD,OAAO,EAAI;EACTA,OAAO,CAACE,OAAO;EACfF,OAAO,CAACE,OAAO,CAACC,aAAa,GAAGb,KAAK,CAACc,KAAK,CAACC,IAAI,CAACC,KAAK;EACtD,OAAON,OAAO;AAChB,CACF,CAAC;AAEDT,GAAG,CAACQ,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAC3B,UAAAM,QAAQ,EAAI;EACV;EACA,IAAIA,QAAQ,CAACC,IAAI,CAACC,IAAI,IAAI,GAAG,EAAE;IAC7BC,OAAO,CAAC,CAAC;IACT,OAAO,KAAK;EACd;EACA,OAAOC,OAAO,CAACC,OAAO,CAACL,QAAQ,CAAC;AAClC,CAAC,EACD,UAAAM,KAAK,EAAI;EACP,OAAOF,OAAO,CAACG,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAetB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}