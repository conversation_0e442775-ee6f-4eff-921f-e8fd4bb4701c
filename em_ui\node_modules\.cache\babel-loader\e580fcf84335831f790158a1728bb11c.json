{"ast": null, "code": "import \"F:\\\\\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF\\\\em_ui\\\\node_modules\\\\core-js\\\\modules\\\\es.array.iterator.js\";\nimport \"F:\\\\\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF\\\\em_ui\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.js\";\nimport \"F:\\\\\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF\\\\em_ui\\\\node_modules\\\\core-js\\\\modules\\\\es.object.assign.js\";\nimport \"F:\\\\\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF\\\\em_ui\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.finally.js\";\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router/index';\nimport store from './store/index';\nimport api from './api';\nVue.prototype.$api = api;\nimport dayjs from 'dayjs';\nVue.prototype.$dayjs = dayjs;\nimport auth from './util/auth';\nVue.use(auth);\nimport cookies from 'vue-cookies';\nVue.use(cookies);\nimport VueMeta from 'vue-meta';\nVue.use(VueMeta);\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nVue.use(ElementUI);\nimport AntDesignVue from 'ant-design-vue';\nimport 'ant-design-vue/dist/antd.css';\nVue.use(AntDesignVue);\nimport hotkeys from 'hotkeys-js';\nVue.prototype.$hotkeys = hotkeys;\n\n// 全局组件自动注册\nimport './components/autoRegister';\n\n// 注释掉SVG自动加载，现在使用PNG图标\n// const req = require.context('./assets/icons', false, /\\.svg$/)\n// const requireAll = requireContext => requireContext.keys().map(requireContext)\n// requireAll(req)\n\nimport './assets/styles/reset.scss';\n\n// import './mock'\n\nVue.config.productionTip = false;\nVue.prototype.$eventBus = new Vue({\n  router: router,\n  store: store,\n  render: function render(h) {\n    return h(App);\n  }\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "api", "prototype", "$api", "dayjs", "$dayjs", "auth", "use", "cookies", "VueMeta", "ElementUI", "AntDesignVue", "hotkeys", "$hotkeys", "config", "productionTip", "$eventBus", "render", "h", "$mount"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router/index'\nimport store from './store/index'\n\nimport api from './api'\nVue.prototype.$api = api\n\nimport dayjs from 'dayjs'\nVue.prototype.$dayjs = dayjs\n\nimport auth from './util/auth'\nVue.use(auth)\n\nimport cookies from 'vue-cookies'\nVue.use(cookies)\n\nimport VueMeta from 'vue-meta'\nVue.use(VueMeta)\n\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\nVue.use(ElementUI)\n\nimport AntDesignVue from 'ant-design-vue'\nimport 'ant-design-vue/dist/antd.css';\nVue.use(AntDesignVue)\n\nimport hotkeys from 'hotkeys-js'\nVue.prototype.$hotkeys = hotkeys\n\n// 全局组件自动注册\nimport './components/autoRegister'\n\n// 注释掉SVG自动加载，现在使用PNG图标\n// const req = require.context('./assets/icons', false, /\\.svg$/)\n// const requireAll = requireContext => requireContext.keys().map(requireContext)\n// requireAll(req)\n\nimport './assets/styles/reset.scss'\n\n// import './mock'\n\nVue.config.productionTip = false\n\nVue.prototype.$eventBus = new Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": ";;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,KAAK,MAAM,eAAe;AAEjC,OAAOC,GAAG,MAAM,OAAO;AACvBJ,GAAG,CAACK,SAAS,CAACC,IAAI,GAAGF,GAAG;AAExB,OAAOG,KAAK,MAAM,OAAO;AACzBP,GAAG,CAACK,SAAS,CAACG,MAAM,GAAGD,KAAK;AAE5B,OAAOE,IAAI,MAAM,aAAa;AAC9BT,GAAG,CAACU,GAAG,CAACD,IAAI,CAAC;AAEb,OAAOE,OAAO,MAAM,aAAa;AACjCX,GAAG,CAACU,GAAG,CAACC,OAAO,CAAC;AAEhB,OAAOC,OAAO,MAAM,UAAU;AAC9BZ,GAAG,CAACU,GAAG,CAACE,OAAO,CAAC;AAEhB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7Cb,GAAG,CAACU,GAAG,CAACG,SAAS,CAAC;AAElB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAO,8BAA8B;AACrCd,GAAG,CAACU,GAAG,CAACI,YAAY,CAAC;AAErB,OAAOC,OAAO,MAAM,YAAY;AAChCf,GAAG,CAACK,SAAS,CAACW,QAAQ,GAAGD,OAAO;;AAEhC;AACA,OAAO,2BAA2B;;AAElC;AACA;AACA;AACA;;AAEA,OAAO,4BAA4B;;AAEnC;;AAEAf,GAAG,CAACiB,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhClB,GAAG,CAACK,SAAS,CAACc,SAAS,GAAG,IAAInB,GAAG,CAAC;EAChCE,MAAM,EAANA,MAAM;EACNC,KAAK,EAALA,KAAK;EACLiB,MAAM,EAAE,SAARA,MAAMA,CAAEC,CAAC;IAAA,OAAIA,CAAC,CAACpB,GAAG,CAAC;EAAA;AACrB,CAAC,CAAC,CAACqB,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}