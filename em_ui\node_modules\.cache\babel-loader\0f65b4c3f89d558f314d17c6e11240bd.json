{"ast": null, "code": "import tinymce from 'tinymce/tinymce';\nimport TinymceEditor from '@tinymce/tinymce-vue';\nimport 'tinymce/themes/silver/theme';\nimport 'tinymce/icons/default/icons';\nimport 'tinymce/plugins/autolink';\nimport 'tinymce/plugins/autoresize';\nimport 'tinymce/plugins/colorpicker';\nimport 'tinymce/plugins/contextmenu';\nimport 'tinymce/plugins/fullscreen';\nimport 'tinymce/plugins/hr';\nimport 'tinymce/plugins/image';\nimport 'tinymce/plugins/imagetools';\nimport 'tinymce/plugins/insertdatetime';\nimport 'tinymce/plugins/link';\nimport 'tinymce/plugins/lists';\nimport 'tinymce/plugins/media';\nimport 'tinymce/plugins/preview';\nimport 'tinymce/plugins/table';\nimport 'tinymce/plugins/textcolor';\nimport 'tinymce/plugins/wordcount';\nimport 'tinymce/plugins/code';\nimport 'tinymce/plugins/searchreplace';\nexport default {\n  name: 'Editor',\n  components: {\n    TinymceEditor: TinymceEditor\n  },\n  props: {\n    value: {\n      type: String,\n      default: ''\n    },\n    setting: {\n      type: Object,\n      default: function _default() {}\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      defaultSetting: {\n        language_url: 'tinymce/langs/zh_CN.js',\n        language: 'zh_CN',\n        skin_url: 'tinymce/skins/ui/oxide',\n        min_height: 250,\n        max_height: 600,\n        selector: 'textarea',\n        plugins: 'autolink autoresize contextmenu fullscreen hr image imagetools insertdatetime link lists media preview table textcolor wordcount code searchreplace',\n        toolbar: 'undo redo | formatselect | bold italic strikethrough forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | hr link image media table insertdatetime searchreplace removeformat | preview code fullscreen',\n        branding: false,\n        menubar: false,\n        toolbar_mode: 'sliding',\n        insertdatetime_formats: ['%Y年%m月%d日', '%H点%M分%S秒', '%Y-%m-%d', '%H:%M:%S'],\n        images_upload_handler: function images_upload_handler(blobInfo, success) {\n          var img = 'data:image/jpeg;base64,' + blobInfo.base64();\n          success(img);\n        }\n      },\n      myValue: this.value\n    };\n  },\n  computed: {\n    completeSetting: function completeSetting() {\n      return Object.assign(this.defaultSetting, this.setting);\n    }\n  },\n  watch: {\n    myValue: function myValue(newValue) {\n      this.$emit('input', newValue);\n    },\n    value: function value(newValue) {\n      this.myValue = newValue;\n    }\n  },\n  mounted: function mounted() {\n    tinymce.init({});\n  }\n};", "map": {"version": 3, "mappings": "AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC,yBACA,aACA,aACA,YACA,WACA;QACAC;UACA;UACAC;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAH;MACA;IACA;IACArB;MACA;IACA;EACA;EACAyB;IACAC;EACA;AACA", "names": ["name", "components", "TinymceEditor", "props", "value", "type", "default", "setting", "disabled", "data", "defaultSetting", "language_url", "language", "skin_url", "min_height", "max_height", "selector", "plugins", "toolbar", "branding", "menubar", "toolbar_mode", "insertdatetime_formats", "images_upload_handler", "success", "myValue", "computed", "completeSetting", "watch", "mounted", "<PERSON><PERSON><PERSON>"], "sourceRoot": "src/components/Editor", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"editor\">\n        <TinymceEditor v-model=\"myValue\" :init=\"completeSetting\" :disabled=\"disabled\" />\n    </div>\n</template>\n\n<script>\nimport tinymce from 'tinymce/tinymce'\nimport TinymceEditor from '@tinymce/tinymce-vue'\nimport 'tinymce/themes/silver/theme'\nimport 'tinymce/icons/default/icons'\nimport 'tinymce/plugins/autolink'\nimport 'tinymce/plugins/autoresize'\nimport 'tinymce/plugins/colorpicker'\nimport 'tinymce/plugins/contextmenu'\nimport 'tinymce/plugins/fullscreen'\nimport 'tinymce/plugins/hr'\nimport 'tinymce/plugins/image'\nimport 'tinymce/plugins/imagetools'\nimport 'tinymce/plugins/insertdatetime'\nimport 'tinymce/plugins/link'\nimport 'tinymce/plugins/lists'\nimport 'tinymce/plugins/media'\nimport 'tinymce/plugins/preview'\nimport 'tinymce/plugins/table'\nimport 'tinymce/plugins/textcolor'\nimport 'tinymce/plugins/wordcount'\nimport 'tinymce/plugins/code'\nimport 'tinymce/plugins/searchreplace'\n\nexport default {\n    name: 'Editor',\n    components: {\n        TinymceEditor\n    },\n    props: {\n        value: {\n            type: String,\n            default: ''\n        },\n        setting: {\n            type: Object,\n            default: () => {}\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        }\n    },\n    data() {\n        return {\n            defaultSetting: {\n                language_url: 'tinymce/langs/zh_CN.js',\n                language: 'zh_CN',\n                skin_url: 'tinymce/skins/ui/oxide',\n                min_height: 250,\n                max_height: 600,\n                selector: 'textarea',\n                plugins: 'autolink autoresize contextmenu fullscreen hr image imagetools insertdatetime link lists media preview table textcolor wordcount code searchreplace',\n                toolbar: 'undo redo | formatselect | bold italic strikethrough forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | hr link image media table insertdatetime searchreplace removeformat | preview code fullscreen',\n                branding: false,\n                menubar: false,\n                toolbar_mode: 'sliding',\n                insertdatetime_formats: [\n                    '%Y年%m月%d日',\n                    '%H点%M分%S秒',\n                    '%Y-%m-%d',\n                    '%H:%M:%S'\n                ],\n                images_upload_handler: (blobInfo, success) => {\n                    const img = 'data:image/jpeg;base64,' + blobInfo.base64()\n                    success(img)\n                }\n            },\n            myValue: this.value\n        }\n    },\n    computed: {\n        completeSetting() {\n            return Object.assign(this.defaultSetting, this.setting)\n        }\n    },\n    watch: {\n        myValue(newValue) {\n            this.$emit('input', newValue)\n        },\n        value(newValue) {\n            this.myValue = newValue\n        }\n    },\n    mounted() {\n        tinymce.init({})\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .tox-tinymce {\n    border: 1px solid #dcdfe6;\n    border-radius: 4px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}