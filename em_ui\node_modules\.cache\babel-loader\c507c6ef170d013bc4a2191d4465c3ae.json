{"ast": null, "code": "export default {\n  name: '<PERSON><PERSON><PERSON>',\n  props: {\n    title: {\n      type: String,\n      default: ''\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAQA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;AACA", "names": ["name", "props", "title", "type", "default"], "sourceRoot": "src/components/PageMain", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"page-main\">\n        <div v-if=\"title\" class=\"title-container\">{{ title }}</div>\n        <slot />\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'PageMain',\n    props: {\n        title: {\n            type: String,\n            default: ''\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-main {\n    position: relative;\n    margin: 20px;\n    padding: 20px;\n    background-color: #fff;\n    .title-container {\n        width: calc(100% + 40px);\n        padding: 14px 20px;\n        margin-left: -20px;\n        margin-top: -20px;\n        margin-bottom: 20px;\n        border-bottom: 1px solid #eee;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}