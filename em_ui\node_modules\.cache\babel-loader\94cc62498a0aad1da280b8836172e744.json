{"ast": null, "code": "export default {\n  name: 'Copyright',\n  methods: {\n    linkProps: function linkProps() {\n      if (this.$store.state.settings.copyrightWebsite) {\n        return {\n          is: 'a',\n          href: this.$store.state.settings.copyrightWebsite,\n          target: '_blank',\n          rel: 'noopener'\n        };\n      } else {\n        return {\n          is: 'span'\n        };\n      }\n    }\n  }\n};", "map": {"version": 3, "mappings": "AASA;EACAA;EACAC;IACAC;MACA;QACA;UACAC;UACAC;UACAC;UACAC;QACA;MACA;QACA;UACAH;QACA;MACA;IACA;EACA;AACA", "names": ["name", "methods", "linkProps", "is", "href", "target", "rel"], "sourceRoot": "src/components/Copyright", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <footer class=\"copyright\">\n        Copyright © {{ $store.state.settings.copyrightDates }}\n        <!-- eslint-disable-next-line vue/require-component-is -->\n        <component v-bind=\"linkProps()\">{{ $store.state.settings.copyrightCompany }}</component>\n    </footer>\n</template>\n\n<script>\nexport default {\n    name: 'Copyright',\n    methods: {\n        linkProps() {\n            if (this.$store.state.settings.copyrightWebsite) {\n                return {\n                    is: 'a',\n                    href: this.$store.state.settings.copyrightWebsite,\n                    target: '_blank',\n                    rel: 'noopener'\n                }\n            } else {\n                return {\n                    is: 'span'\n                }\n            }\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\nfooter {\n    margin: 40px 0 20px 0;\n    line-height: 20px;\n    text-align: center;\n    color: #808695;\n    font-size: 14px;\n    a {\n        text-decoration: none;\n        color: #808695;\n        &:hover {\n            color: #3d4047;\n        }\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}