{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getTask, updateTask, deleteTask, addTask } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      rTime: rTime,\n      loading: false,\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 5\n      },\n      table_selectedRowKeys: [],\n      task_query_type: 'name',\n      task_query_buttonTitle: '搜索',\n      task_query_text: '',\n      task_save_title: '新增定时任务',\n      task_save_modalVisible: false,\n      task_save_current_text: '',\n      task_form_data: {},\n      task_data_list: [],\n      rules: {\n        id: [{\n          required: true,\n          message: '',\n          trigger: 'blur'\n        }],\n        name: [{\n          required: true,\n          message: '',\n          trigger: 'blur'\n        }],\n        status: [{\n          required: true,\n          message: '',\n          trigger: 'blur'\n        }],\n        groupName: [{\n          required: true,\n          message: '',\n          trigger: 'blur'\n        }],\n        cron: [{\n          required: true,\n          message: '',\n          trigger: 'blur'\n        }],\n        classPath: [{\n          required: true,\n          message: '',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.Get_taskDataList();\n  },\n  watch: {\n    task_save_modalVisible: function task_save_modalVisible(val) {\n      if (!val) {\n        this.task_form_data = {};\n      } else {\n        //saveModal被显示\n        this.task_save_current_text = this.task_form_data.text;\n      }\n    }\n  },\n  methods: {\n    Get_taskDataList: function Get_taskDataList() {\n      var _this = this;\n      getTask().then(function (res) {\n        _this.task_query_buttonTitle = '搜索';\n        _this.task_data_list = res.data;\n        _this.task_save_title = '新建定时任务';\n      });\n    },\n    Query_taskDataList: function Query_taskDataList() {\n      var _this2 = this;\n      var text = this.task_query_text;\n      var temp_list = [];\n      this.task_data_list.forEach(function (item) {\n        if (item[_this2.task_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.task_query_buttonTitle = '返回';\n      this.task_data_list = temp_list;\n    },\n    Edit_taskData: function Edit_taskData(form) {\n      this.task_save_title = '编辑定时任务';\n      this.task_form_data = JSON.parse(JSON.stringify(form));\n      this.task_save_modalVisible = true;\n    },\n    Del_taskData: function Del_taskData(data) {\n      var _this3 = this;\n      deleteTask(data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_taskDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_taskData({\n          'name': _this4.task_data_list[i].name,\n          'groupName': _this4.task_data_list[i].groupName\n        });\n      });\n    },\n    Save_taskData: function Save_taskData() {\n      var _this5 = this;\n      var form = this.task_form_data;\n      var req = this.task_save_title == '编辑定时任务' ? updateTask(form) : addTask(form);\n      req.then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.task_save_modalVisible = false;\n        _this5.Get_taskDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_date_changeHandler: function Form_date_changeHandler(date) {\n      this.task_form_data.date = date._d;\n    },\n    Change_taskStatus: function Change_taskStatus(r) {\n      r.status = r.status == 0 ? 1 : 0;\n      this.task_form_data = r;\n      this.task_save_title = '编辑定时任务';\n      this.Save_taskData();\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;;AA4HA;AACA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;UAAAC;UAAAC;UAAAC;QAAA;QACAC;UAAAH;UAAAC;UAAAC;QAAA;QACAE;UAAAJ;UAAAC;UAAAC;QAAA;QACAG;UAAAL;UAAAC;UAAAC;QAAA;QACAI;UAAAN;UAAAC;UAAAC;QAAA;QACAK;UAAAP;UAAAC;UAAAC;QAAA;MACA;IACA;EACA;EACAM;IACA;EACA;EACAC;IACAf;MACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAgB;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;MACA;MACA;MACA;IAEA;EACA;AACA", "names": ["data", "rTime", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "task_query_type", "task_query_buttonTitle", "task_query_text", "task_save_title", "task_save_modalVisible", "task_save_current_text", "task_form_data", "task_data_list", "rules", "id", "required", "message", "trigger", "name", "status", "groupName", "cron", "classPath", "created", "watch", "methods", "Get_taskDataList", "getTask", "_this", "Query_taskDataList", "temp_list", "Edit_taskData", "Del_taskData", "deleteTask", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_taskData", "req", "_this5", "Table_selectChange", "Form_date_changeHandler", "Change_taskStatus", "r"], "sourceRoot": "src/views/admin/system", "sources": ["automatic_task.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"定时任务管理\">\n      <div class=\"head\">\n        <span>搜索类型</span>\n        <a-select size=\"large\" default-value=\"公告编号\" v-model=\"task_query_type\" style=\"width: 200px;\">\n          <a-select-option value=\"name\">任务名称</a-select-option>\n          <a-select-option value=\"id\">任务编号</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"task_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"task_query_buttonTitle\"\n          size=\"large\"\n          @search=\"task_query_buttonTitle == '搜索' ? Query_taskDataList() : Get_taskDataList()\"\n        />\n        <a-button type=\"primary\" style=\"height: 38px;\" @click=\"task_save_modalVisible = true\">添加定时任务</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 40px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「定时任务」</a-button>\n      </div>\n      <a-table :data-source=\"task_data_list\">\n        <a-table-column key=\"id\" title=\"任务编号\" data-index=\"id\" />\n        <a-table-column key=\"name\" title=\"任务名称\" data-index=\"name\" />\n        <a-table-column key=\"groupName\" title=\"任务组名\" data-index=\"groupName\" />\n        <a-table-column key=\"classPath\" title=\"调用目标类\" data-index=\"classPath\" />\n        <a-table-column key=\"cron\" title=\"cron表达式\" data-index=\"cron\" />\n        <a-table-column key=\"status\" title=\"当前状态\" data-index=\"status\">\n          <template slot-scope=\"text, record\">\n            <a-switch v-model=\"record.status == 0\" @change=\"Change_taskStatus(record)\" />\n          </template>\n        </a-table-column>\n\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_taskData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_taskData(record)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"task_save_modalVisible\"\n      :title=\"task_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      @ok=\"Save_taskData\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      layout=\"horizontal\"\n      style=\"width: 54vw;\"\n    >\n      <a-form-model\n        :model=\"task_form_data\"\n        :label-col=\"labelCol\"\n        :wrapper-col=\"wrapperCol\"\n        :rules=\"rules\"\n      >\n        <el-row :gutter=\"20\" style=\"margin-top: 10px;\">\n          <el-col :span=\"10\">\n            <a-form-model-item label=\"任务名称\" prop=\"name\">\n              <a-input v-model=\"task_form_data.name\" style=\"width: 200px;\" placeholder=\"请输入任务名称\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <a-form-model-item label=\"任务组别\" prop=\"groupName\">\n              <a-select\n                default-value=\"系统任务\"\n                v-model=\"task_form_data.groupName\"\n                style=\"width: 200px;\"\n                placeholder=\"请选择\"\n              >\n                <a-select-option value=\"系统任务\">系统任务</a-select-option>\n                <a-select-option value=\"自定任务\">自定任务</a-select-option>\n              </a-select>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"10\">\n            <a-form-model-item label=\"调用类\" prop=\"classPath\">\n              <a-input\n                v-model=\"task_form_data.classPath\"\n                style=\"width: 481px;\"\n                placeholder=\"请输入任务调用的目标类\"\n              >\n                <a-tooltip slot=\"suffix\" title=\"Extra information\">\n                  <a-icon type=\"info-circle\" style=\"color: rgba(0, 0, 0, 0.45);\" />\n                </a-tooltip>\n              </a-input>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"10\">\n            <a-form-model-item label=\"cron表达式\" prop=\"cron\">\n              <a-input\n                v-model=\"task_form_data.cron\"\n                style=\"width: 200px;\"\n                placeholder=\"请输入cron表达式\"\n              />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <a-form-model-item label=\"当前策略\" placeholder=\"请选择\" prop=\"status\">\n              <a-select default-value=\"0\" v-model=\"task_form_data.status\" style=\"width: 200px;\">\n                <a-select-option value=\"0\">立即执行</a-select-option>\n                <a-select-option value=\"1\">暂停任务</a-select-option>\n              </a-select>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getTask, updateTask, deleteTask, addTask } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\n\nexport default {\n  data () {\n    return {\n      rTime,\n      loading: false,\n      labelCol: { span: 8 },\n      wrapperCol: { span: 5 },\n      table_selectedRowKeys: [],\n      task_query_type: 'name',\n      task_query_buttonTitle: '搜索',\n      task_query_text: '',\n      task_save_title: '新增定时任务',\n      task_save_modalVisible: false,\n      task_save_current_text: '',\n      task_form_data: {},\n      task_data_list: [],\n      rules: {\n        id: [{ required: true, message: '', trigger: 'blur' }],\n        name: [{ required: true, message: '', trigger: 'blur' }],\n        status: [{ required: true, message: '', trigger: 'blur' }],\n        groupName: [{ required: true, message: '', trigger: 'blur' }],\n        cron: [{ required: true, message: '', trigger: 'blur' }],\n        classPath: [{ required: true, message: '', trigger: 'blur' }],\n      },\n    }\n  },\n  created () {\n    this.Get_taskDataList()\n  },\n  watch: {\n    task_save_modalVisible (val) {\n      if (!val) {\n        this.task_form_data = {}\n      } else {\n        //saveModal被显示\n        this.task_save_current_text = this.task_form_data.text\n      }\n    },\n  },\n  methods: {\n    Get_taskDataList () {\n      getTask().then(res => {\n        this.task_query_buttonTitle = '搜索'\n        this.task_data_list = res.data\n        this.task_save_title = '新建定时任务'\n      })\n    },\n    Query_taskDataList () {\n      let text = this.task_query_text\n      let temp_list = []\n      this.task_data_list.forEach(item => {\n        if (item[this.task_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.task_query_buttonTitle = '返回'\n      this.task_data_list = temp_list\n    },\n    Edit_taskData (form) {\n      this.task_save_title = '编辑定时任务'\n      this.task_form_data = JSON.parse(JSON.stringify(form))\n      this.task_save_modalVisible = true\n    },\n    Del_taskData (data) {\n      deleteTask(data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_taskDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_taskData({\n          'name': this.task_data_list[i].name,\n          'groupName': this.task_data_list[i].groupName\n        })\n      })\n    },\n    Save_taskData () {\n      let form = this.task_form_data\n      let req = this.task_save_title == '编辑定时任务' ? updateTask(form) : addTask(form)\n      req.then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.task_save_modalVisible = false\n        this.Get_taskDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_date_changeHandler (date) {\n      this.task_form_data.date = date._d\n    },\n    Change_taskStatus (r) {\n      r.status = r.status == 0 ? 1 : 0\n      this.task_form_data = r\n      this.task_save_title = '编辑定时任务'\n      this.Save_taskData()\n\n    }\n  },\n}\n</script>\n    \n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n</style>\n\n<style lang=\"scss\" >\n.ant-modal-content {\n    width: 60vw;\n}\n</style>"]}, "metadata": {}, "sourceType": "module"}