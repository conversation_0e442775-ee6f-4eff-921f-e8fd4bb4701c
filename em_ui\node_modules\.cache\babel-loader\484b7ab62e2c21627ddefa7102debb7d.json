{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.symbol.iterator.js\");\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.array.splice.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.string.iterator.js\");\nrequire(\"core-js/modules/web.dom-collections.for-each.js\");\nrequire(\"core-js/modules/web.dom-collections.iterator.js\");\nexports.__esModule = true;\nexports.removeResizeListener = exports.addResizeListener = undefined;\nvar _resizeObserverPolyfill = require('resize-observer-polyfill');\nvar _resizeObserverPolyfill2 = _interopRequireDefault(_resizeObserverPolyfill);\nvar _throttleDebounce = require('throttle-debounce');\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar isServer = typeof window === 'undefined';\n\n/* istanbul ignore next */\nvar resizeHandler = function resizeHandler(entries) {\n  for (var _iterator = entries, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {\n    var _ref;\n    if (_isArray) {\n      if (_i >= _iterator.length) break;\n      _ref = _iterator[_i++];\n    } else {\n      _i = _iterator.next();\n      if (_i.done) break;\n      _ref = _i.value;\n    }\n    var entry = _ref;\n    var listeners = entry.target.__resizeListeners__ || [];\n    if (listeners.length) {\n      listeners.forEach(function (fn) {\n        fn();\n      });\n    }\n  }\n};\n\n/* istanbul ignore next */\nvar addResizeListener = exports.addResizeListener = function addResizeListener(element, fn) {\n  if (isServer) return;\n  if (!element.__resizeListeners__) {\n    element.__resizeListeners__ = [];\n    element.__ro__ = new _resizeObserverPolyfill2.default((0, _throttleDebounce.debounce)(16, resizeHandler));\n    element.__ro__.observe(element);\n  }\n  element.__resizeListeners__.push(fn);\n};\n\n/* istanbul ignore next */\nvar removeResizeListener = exports.removeResizeListener = function removeResizeListener(element, fn) {\n  if (!element || !element.__resizeListeners__) return;\n  element.__resizeListeners__.splice(element.__resizeListeners__.indexOf(fn), 1);\n  if (!element.__resizeListeners__.length) {\n    element.__ro__.disconnect();\n  }\n};", "map": {"version": 3, "names": ["require", "exports", "__esModule", "removeResizeListener", "addResizeListener", "undefined", "_resizeObserverPolyfill", "_resizeObserverPolyfill2", "_interopRequireDefault", "_throttleDebounce", "obj", "default", "isServer", "window", "resize<PERSON><PERSON>ler", "entries", "_iterator", "_isArray", "Array", "isArray", "_i", "Symbol", "iterator", "_ref", "length", "next", "done", "value", "entry", "listeners", "target", "__resizeListeners__", "for<PERSON>ach", "fn", "element", "__ro__", "debounce", "observe", "push", "splice", "indexOf", "disconnect"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/lib/utils/resize-event.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\nexports.removeResizeListener = exports.addResizeListener = undefined;\n\nvar _resizeObserverPolyfill = require('resize-observer-polyfill');\n\nvar _resizeObserverPolyfill2 = _interopRequireDefault(_resizeObserverPolyfill);\n\nvar _throttleDebounce = require('throttle-debounce');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar isServer = typeof window === 'undefined';\n\n/* istanbul ignore next */\nvar resizeHandler = function resizeHandler(entries) {\n  for (var _iterator = entries, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {\n    var _ref;\n\n    if (_isArray) {\n      if (_i >= _iterator.length) break;\n      _ref = _iterator[_i++];\n    } else {\n      _i = _iterator.next();\n      if (_i.done) break;\n      _ref = _i.value;\n    }\n\n    var entry = _ref;\n\n    var listeners = entry.target.__resizeListeners__ || [];\n    if (listeners.length) {\n      listeners.forEach(function (fn) {\n        fn();\n      });\n    }\n  }\n};\n\n/* istanbul ignore next */\nvar addResizeListener = exports.addResizeListener = function addResizeListener(element, fn) {\n  if (isServer) return;\n  if (!element.__resizeListeners__) {\n    element.__resizeListeners__ = [];\n    element.__ro__ = new _resizeObserverPolyfill2.default((0, _throttleDebounce.debounce)(16, resizeHandler));\n    element.__ro__.observe(element);\n  }\n  element.__resizeListeners__.push(fn);\n};\n\n/* istanbul ignore next */\nvar removeResizeListener = exports.removeResizeListener = function removeResizeListener(element, fn) {\n  if (!element || !element.__resizeListeners__) return;\n  element.__resizeListeners__.splice(element.__resizeListeners__.indexOf(fn), 1);\n  if (!element.__resizeListeners__.length) {\n    element.__ro__.disconnect();\n  }\n};"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,oBAAoB,GAAGF,OAAO,CAACG,iBAAiB,GAAGC,SAAS;AAEpE,IAAIC,uBAAuB,GAAGN,OAAO,CAAC,0BAA0B,CAAC;AAEjE,IAAIO,wBAAwB,GAAGC,sBAAsB,CAACF,uBAAuB,CAAC;AAE9E,IAAIG,iBAAiB,GAAGT,OAAO,CAAC,mBAAmB,CAAC;AAEpD,SAASQ,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACR,UAAU,GAAGQ,GAAG,GAAG;IAAEC,OAAO,EAAED;EAAI,CAAC;AAAE;AAE9F,IAAIE,QAAQ,GAAG,OAAOC,MAAM,KAAK,WAAW;;AAE5C;AACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,OAAO,EAAE;EAClD,KAAK,IAAIC,SAAS,GAAGD,OAAO,EAAEE,QAAQ,GAAGC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAEI,EAAE,GAAG,CAAC,EAAEJ,SAAS,GAAGC,QAAQ,GAAGD,SAAS,GAAGA,SAAS,CAACK,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,IAAI;IAC5I,IAAIC,IAAI;IAER,IAAIN,QAAQ,EAAE;MACZ,IAAIG,EAAE,IAAIJ,SAAS,CAACQ,MAAM,EAAE;MAC5BD,IAAI,GAAGP,SAAS,CAACI,EAAE,EAAE,CAAC;IACxB,CAAC,MAAM;MACLA,EAAE,GAAGJ,SAAS,CAACS,IAAI,CAAC,CAAC;MACrB,IAAIL,EAAE,CAACM,IAAI,EAAE;MACbH,IAAI,GAAGH,EAAE,CAACO,KAAK;IACjB;IAEA,IAAIC,KAAK,GAAGL,IAAI;IAEhB,IAAIM,SAAS,GAAGD,KAAK,CAACE,MAAM,CAACC,mBAAmB,IAAI,EAAE;IACtD,IAAIF,SAAS,CAACL,MAAM,EAAE;MACpBK,SAAS,CAACG,OAAO,CAAC,UAAUC,EAAE,EAAE;QAC9BA,EAAE,CAAC,CAAC;MACN,CAAC,CAAC;IACJ;EACF;AACF,CAAC;;AAED;AACA,IAAI7B,iBAAiB,GAAGH,OAAO,CAACG,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC8B,OAAO,EAAED,EAAE,EAAE;EAC1F,IAAIrB,QAAQ,EAAE;EACd,IAAI,CAACsB,OAAO,CAACH,mBAAmB,EAAE;IAChCG,OAAO,CAACH,mBAAmB,GAAG,EAAE;IAChCG,OAAO,CAACC,MAAM,GAAG,IAAI5B,wBAAwB,CAACI,OAAO,CAAC,CAAC,CAAC,EAAEF,iBAAiB,CAAC2B,QAAQ,EAAE,EAAE,EAAEtB,aAAa,CAAC,CAAC;IACzGoB,OAAO,CAACC,MAAM,CAACE,OAAO,CAACH,OAAO,CAAC;EACjC;EACAA,OAAO,CAACH,mBAAmB,CAACO,IAAI,CAACL,EAAE,CAAC;AACtC,CAAC;;AAED;AACA,IAAI9B,oBAAoB,GAAGF,OAAO,CAACE,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC+B,OAAO,EAAED,EAAE,EAAE;EACnG,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACH,mBAAmB,EAAE;EAC9CG,OAAO,CAACH,mBAAmB,CAACQ,MAAM,CAACL,OAAO,CAACH,mBAAmB,CAACS,OAAO,CAACP,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9E,IAAI,CAACC,OAAO,CAACH,mBAAmB,CAACP,MAAM,EAAE;IACvCU,OAAO,CAACC,MAAM,CAACM,UAAU,CAAC,CAAC;EAC7B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}