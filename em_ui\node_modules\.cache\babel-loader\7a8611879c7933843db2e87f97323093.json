{"ast": null, "code": "import \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\nvar state = {\n  list: []\n};\nvar getters = {};\nvar actions = {};\nvar mutations = {\n  add: function add(state, name) {\n    !state.list.includes(name) && state.list.push(name);\n  },\n  remove: function remove(state, name) {\n    state.list = state.list.filter(function (v) {\n      return v != name;\n    });\n  },\n  clean: function clean(state) {\n    state.list = [];\n  }\n};\nexport default {\n  namespaced: true,\n  state: state,\n  actions: actions,\n  getters: getters,\n  mutations: mutations\n};", "map": {"version": 3, "names": ["state", "list", "getters", "actions", "mutations", "add", "name", "includes", "push", "remove", "filter", "v", "clean", "namespaced"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/store/modules/keepAlive.js"], "sourcesContent": ["const state = {\n    list: []\n}\n\nconst getters = {}\n\nconst actions = {}\n\nconst mutations = {\n    add(state, name) {\n        !state.list.includes(name) && state.list.push(name)\n    },\n    remove(state, name) {\n        state.list = state.list.filter(v => {\n            return v != name\n        })\n    },\n    clean(state) {\n        state.list = []\n    }\n}\n\nexport default {\n    namespaced: true,\n    state,\n    actions,\n    getters,\n    mutations\n}\n"], "mappings": ";;;;;;;AAAA,IAAMA,KAAK,GAAG;EACVC,IAAI,EAAE;AACV,CAAC;AAED,IAAMC,OAAO,GAAG,CAAC,CAAC;AAElB,IAAMC,OAAO,GAAG,CAAC,CAAC;AAElB,IAAMC,SAAS,GAAG;EACdC,GAAG,WAAHA,GAAGA,CAACL,KAAK,EAAEM,IAAI,EAAE;IACb,CAACN,KAAK,CAACC,IAAI,CAACM,QAAQ,CAACD,IAAI,CAAC,IAAIN,KAAK,CAACC,IAAI,CAACO,IAAI,CAACF,IAAI,CAAC;EACvD,CAAC;EACDG,MAAM,WAANA,MAAMA,CAACT,KAAK,EAAEM,IAAI,EAAE;IAChBN,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,CAACS,MAAM,CAAC,UAAAC,CAAC,EAAI;MAChC,OAAOA,CAAC,IAAIL,IAAI;IACpB,CAAC,CAAC;EACN,CAAC;EACDM,KAAK,WAALA,KAAKA,CAACZ,KAAK,EAAE;IACTA,KAAK,CAACC,IAAI,GAAG,EAAE;EACnB;AACJ,CAAC;AAED,eAAe;EACXY,UAAU,EAAE,IAAI;EAChBb,KAAK,EAALA,KAAK;EACLG,OAAO,EAAPA,OAAO;EACPD,OAAO,EAAPA,OAAO;EACPE,SAAS,EAATA;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}