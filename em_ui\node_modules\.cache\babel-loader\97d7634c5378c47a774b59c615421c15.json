{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nexport default {\n  data: function data() {\n    return {};\n  },\n  watch: {\n    '$store.state.keepAlive.list': function $storeStateKeepAliveList(val) {\n      process.env.NODE_ENV == 'development' && console.log(\"[ keepAliveList ] \".concat(val));\n    },\n    '$store.state.settings.mode': {\n      handler: function handler() {\n        if (this.$store.state.settings.mode == 'pc') {\n          this.$store.commit('settings/updateThemeSetting', {\n            'sidebarCollapse': this.$store.state.settings.sidebarCollapseLastStatus\n          });\n        } else if (this.$store.state.settings.mode == 'mobile') {\n          this.$store.commit('settings/updateThemeSetting', {\n            'sidebarCollapse': true\n          });\n        }\n        document.body.setAttribute('data-mode', this.$store.state.settings.mode);\n      },\n      immediate: true\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n    window.onresize = function () {\n      _this.$store.commit('settings/setMode', document.body.clientWidth);\n    };\n    window.onresize();\n  },\n  methods: {},\n  metaInfo: function metaInfo() {\n    return {\n      title: this.$store.state.settings.enableDynamicTitle && this.$store.state.settings.title,\n      titleTemplate: function titleTemplate(title) {\n        return title ? \"\".concat(title, \" - \").concat(process.env.VUE_APP_TITLE) : process.env.VUE_APP_TITLE;\n      }\n    };\n  }\n};", "map": {"version": 3, "mappings": ";AAOA;EACAA;IACA;EACA;EACAC;IACA;MACAC;IACA;IACA;MACAC;QACA;UACA;YACA;UACA;QACA;UACA;YACA;UACA;QACA;QACAC;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACAC;MACAC;IACA;IACAD;EACA;EACAE;EACAC;IACA;MACAC;MACAC;QACA;MACA;IACA;EACA;AACA", "names": ["data", "watch", "process", "handler", "document", "immediate", "mounted", "window", "_this", "methods", "metaInfo", "title", "titleTemplate"], "sourceRoot": "src", "sources": ["App.vue"], "sourcesContent": ["<template>\n    <div id=\"app\">\n        <RouterView />\n    </div>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {}\n    },\n    watch: {\n        '$store.state.keepAlive.list'(val) {\n            process.env.NODE_ENV == 'development' && console.log(`[ keepAliveList ] ${val}`)\n        },\n        '$store.state.settings.mode': {\n            handler() {\n                if (this.$store.state.settings.mode == 'pc') {\n                    this.$store.commit('settings/updateThemeSetting', {\n                        'sidebarCollapse': this.$store.state.settings.sidebarCollapseLastStatus\n                    })\n                } else if (this.$store.state.settings.mode == 'mobile') {\n                    this.$store.commit('settings/updateThemeSetting', {\n                        'sidebarCollapse': true\n                    })\n                }\n                document.body.setAttribute('data-mode', this.$store.state.settings.mode)\n            },\n            immediate: true\n        }\n    },\n    mounted() {\n        window.onresize = () => {\n            this.$store.commit('settings/setMode', document.body.clientWidth)\n        }\n        window.onresize()\n    },\n    methods: {},\n    metaInfo() {\n        return {\n            title: this.$store.state.settings.enableDynamicTitle && this.$store.state.settings.title,\n            titleTemplate: title => {\n                return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\n            }\n        }\n    }\n}\n</script>\n\n<style scoped>\n#app {\n    height: 100%;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}