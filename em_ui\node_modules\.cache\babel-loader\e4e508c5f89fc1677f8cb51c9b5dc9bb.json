{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport path from 'path';\nexport default {\n  name: 'SidebarItem',\n  props: {\n    item: {\n      type: Object,\n      required: true\n    },\n    basePath: {\n      type: String,\n      default: ''\n    }\n  },\n  data: function data() {\n    return {};\n  },\n  computed: {\n    hasChildren: function hasChildren() {\n      var flag = true;\n      if (this.item.children) {\n        if (this.item.children.every(function (item) {\n          return item.meta.sidebar === false;\n        })) {\n          flag = false;\n        }\n      } else {\n        flag = false;\n      }\n      return flag;\n    }\n  },\n  created: function created() {},\n  mounted: function mounted() {},\n  methods: {\n    isExternal: function isExternal(path) {\n      return /^(https?:|mailto:|tel:)/.test(path);\n    },\n    resolvePath: function resolvePath(routePath) {\n      if (this.isExternal(routePath)) {\n        return routePath;\n      }\n      if (this.isExternal(this.basePath)) {\n        return this.basePath;\n      }\n      return path.resolve(this.basePath, routePath);\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;AAqBA;AAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;UAAA;QAAA;UACAC;QACA;MACA;QACAA;MACA;MACA;IACA;EACA;EACAC;EACAC;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "item", "type", "required", "basePath", "default", "data", "computed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flag", "created", "mounted", "methods", "isExternal", "<PERSON><PERSON><PERSON>"], "sourceRoot": "src/layout/components/SidebarItem", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div v-if=\"item.meta.sidebar !== false\">\n        <router-link v-if=\"!hasChildren\" v-slot=\"{ href, navigate, isActive, isExactActive }\" custom :to=\"resolvePath(item.path)\">\n            <a :href=\"isExternal(resolvePath(item.path)) ? resolvePath(item.path) : href\" :class=\"[isActive && 'router-link-active', isExactActive && 'router-link-exact-active']\" :target=\"isExternal(resolvePath(item.path)) ? '_blank' : '_self'\" @click=\"navigate\">\n                <el-menu-item :title=\"item.meta.title\" :index=\"resolvePath(item.path)\">\n                    <svg-icon v-if=\"item.meta.icon\" :name=\"item.meta.icon\" />\n                    <span>{{ item.meta.title }}</span>\n                </el-menu-item>\n            </a>\n        </router-link>\n        <el-submenu v-else :title=\"item.meta.title\" :index=\"resolvePath(item.path)\">\n            <template slot=\"title\">\n                <svg-icon v-if=\"item.meta.icon\" :name=\"item.meta.icon\" />\n                <span>{{ item.meta.title }}</span>\n            </template>\n            <SidebarItem v-for=\"route in item.children\" :key=\"route.path\" :item=\"route\" :base-path=\"resolvePath(item.path)\" />\n        </el-submenu>\n    </div>\n</template>\n\n<script>\nimport path from 'path'\n\nexport default {\n    name: 'SidebarItem',\n    props: {\n        item: {\n            type: Object,\n            required: true\n        },\n        basePath: {\n            type: String,\n            default: ''\n        }\n    },\n    data() {\n        return {}\n    },\n    computed: {\n        hasChildren() {\n            let flag = true\n            if (this.item.children) {\n                if (this.item.children.every(item => item.meta.sidebar === false)) {\n                    flag = false\n                }\n            } else {\n                flag = false\n            }\n            return flag\n        }\n    },\n    created() {},\n    mounted() {},\n    methods: {\n        isExternal(path) {\n            return /^(https?:|mailto:|tel:)/.test(path)\n        },\n        resolvePath(routePath) {\n            if (this.isExternal(routePath)) {\n                return routePath\n            }\n            if (this.isExternal(this.basePath)) {\n                return this.basePath\n            }\n            return path.resolve(this.basePath, routePath)\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-menu-item,\n::v-deep .el-menu-item span,\n::v-deep .el-submenu__title,\n::v-deep .el-submenu__title span {\n    vertical-align: inherit;\n    @include text-overflow;\n}\n::v-deep .el-submenu {\n    .el-menu--inline {\n        .el-submenu > .el-submenu__title,\n        .el-menu-item {\n            background-color: darken($g-sub-sidebar-bg, 7) !important;\n            &:hover {\n                background-color: darken($g-sub-sidebar-bg, 10) !important;\n            }\n        }\n    }\n}\n::v-deep .el-menu-item.is-active,\n::v-deep .el-submenu .el-menu--inline .el-menu-item.is-active {\n    background-color: $g-sub-sidebar-menu-active-bg !important;\n}\n::v-deep .el-submenu,\n::v-deep .el-menu-item {\n    .svg-icon {\n        font-size: 20px;\n        margin-right: 10px;\n        vertical-align: -0.25em;\n        transition: all 0.3s;\n    }\n    &:hover > .svg-icon,\n    .el-submenu__title:hover > .svg-icon {\n        transform: scale(1.2);\n    }\n}\na {\n    cursor: pointer;\n    color: inherit;\n    text-decoration: none;\n}\n</style>\n\n<style lang=\"scss\">\n.el-menu--collapse .el-submenu.is-active > .el-submenu__title {\n    color: $g-sub-sidebar-menu-active-color !important;\n    background-color: $g-sub-sidebar-menu-active-bg !important;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}