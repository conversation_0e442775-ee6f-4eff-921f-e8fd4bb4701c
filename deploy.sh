#!/bin/bash

# 小区物业管理系统部署脚本
# 作者: System
# 日期: 2024-01-15

set -e

echo "=== 小区物业管理系统部署脚本 ==="
echo "开始部署..."

# 检查 Docker 和 Docker Compose 是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 停止现有容器
echo "停止现有容器..."
docker-compose down

# 清理旧镜像（可选）
read -p "是否清理旧镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "清理旧镜像..."
    docker system prune -f
fi

# 构建后端
echo "构建后端应用..."
cd em_server
if [ ! -f "target/em_server-0.0.1-SNAPSHOT.jar" ]; then
    echo "构建 Maven 项目..."
    mvn clean package -DskipTests
fi
cd ..

# 构建前端
echo "构建前端应用..."
cd em_ui
if [ ! -d "dist" ]; then
    echo "安装前端依赖..."
    npm install
    echo "构建前端项目..."
    npm run build
fi
cd ..

# 启动服务
echo "启动服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "=== 部署完成 ==="
echo "前端访问地址: http://localhost"
echo "后端API地址: http://localhost:8082"
echo "数据库地址: localhost:3306"
echo ""
echo "默认账号:"
echo "管理员: admin / 123456"
echo "普通用户: test / 123456"
echo ""
echo "查看日志: docker-compose logs -f [service_name]"
echo "停止服务: docker-compose down"
echo "重启服务: docker-compose restart"
