{"ast": null, "code": "import \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport Vue from 'vue';\nimport Vuex from 'vuex';\nVue.use(Vuex);\nvar modules = {};\nvar require_module = require.context('./modules', false, /.js$/);\nrequire_module.keys().forEach(function (file_name) {\n  modules[file_name.slice(2, -3)] = require_module(file_name).default;\n});\nexport default new Vuex.Store({\n  modules: modules,\n  strict: process.env.NODE_ENV !== 'production'\n});", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "use", "modules", "require_module", "require", "context", "keys", "for<PERSON>ach", "file_name", "slice", "default", "Store", "strict", "process", "env", "NODE_ENV"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\n\nVue.use(Vuex)\n\nconst modules = {}\nconst require_module = require.context('./modules', false, /.js$/)\nrequire_module.keys().forEach(file_name => {\n    modules[file_name.slice(2, -3)] = require_module(file_name).default\n})\n\nexport default new Vuex.Store({\n    modules: modules,\n    strict: process.env.NODE_ENV !== 'production'\n})\n"], "mappings": ";;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvBD,GAAG,CAACE,GAAG,CAACD,IAAI,CAAC;AAEb,IAAME,OAAO,GAAG,CAAC,CAAC;AAClB,IAAMC,cAAc,GAAGC,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC;AAClEF,cAAc,CAACG,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,UAAAC,SAAS,EAAI;EACvCN,OAAO,CAACM,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGN,cAAc,CAACK,SAAS,CAAC,CAACE,OAAO;AACvE,CAAC,CAAC;AAEF,eAAe,IAAIV,IAAI,CAACW,KAAK,CAAC;EAC1BT,OAAO,EAAEA,OAAO;EAChBU,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK;AACrC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}