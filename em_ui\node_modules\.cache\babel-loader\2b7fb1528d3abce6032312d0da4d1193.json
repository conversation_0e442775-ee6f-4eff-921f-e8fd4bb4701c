{"ast": null, "code": "export default {\n  name: 'EmptyLayout',\n  props: {},\n  data: function data() {\n    return {};\n  },\n  created: function created() {},\n  mounted: function mounted() {},\n  methods: {}\n};", "map": {"version": 3, "mappings": "AAKA;EACAA;EACAC;EACAC;IACA;EACA;EACAC;EACAC;EACAC;AACA", "names": ["name", "props", "data", "created", "mounted", "methods"], "sourceRoot": "src/layout", "sources": ["empty.vue"], "sourcesContent": ["<template>\n    <RouterView />\n</template>\n\n<script>\nexport default {\n    name: 'EmptyLayout',\n    props: {},\n    data() {\n        return {}\n    },\n    created() {},\n    mounted() {},\n    methods: {}\n}\n</script>\n"]}, "metadata": {}, "sourceType": "module"}