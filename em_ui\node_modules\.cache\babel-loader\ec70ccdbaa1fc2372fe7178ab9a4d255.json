{"ast": null, "code": "import \"core-js/modules/es.object.keys.js\";\nimport { getRqInfo, saveRqInfo } from '@/api/requests/rq-manage.js';\nexport default {\n  data: function data() {\n    return {\n      loading: false,\n      info_form: {},\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      }\n    };\n  },\n  created: function created() {\n    this.Get_RqInfo();\n  },\n  methods: {\n    Get_RqInfo: function Get_RqInfo() {\n      var _this = this;\n      getRqInfo().then(function (res) {\n        console.log(res);\n        _this.info_form = JSON.parse(res.data.text);\n      });\n    },\n    Save_RqInfo: function Save_RqInfo() {\n      saveRqInfo(this.info_form).then(function (res) {\n        console.log(res);\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAqFA;AACA;EACAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QACAC;QACAC;MACA;IACA;IACAC;MACAC;QACAH;MACA;IACA;EACA;AACA", "names": ["data", "loading", "info_form", "labelCol", "span", "wrapperCol", "created", "methods", "Get_RqInfo", "getRqInfo", "console", "_this", "Save_RqInfo", "saveRqInfo"], "sourceRoot": "src/views/admin/rq", "sources": ["rq_info.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"社区简介\">\n      <a-form-model\n        :model=\"info_form\"\n        style=\"width: 80vh;\"\n        :label-col=\"labelCol\"\n        :wrapper-col=\"wrapperCol\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"社区名称\">\n              <a-input v-model=\"info_form.name\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"社区负责人\">\n              <a-input v-model=\"info_form.person\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"建成日期\">\n              <a-input v-model=\"info_form.date\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"楼宇数量\">\n              <a-input v-model=\"info_form.buildingsNumber\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"社区地址\">\n              <a-input v-model=\"info_form.address\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"联系电话\">\n              <a-input v-model=\"info_form.phone\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"建筑面积(亩)\">\n              <a-input v-model=\"info_form.builtUpArea\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"绿化面积(亩)\">\n              <a-input v-model=\"info_form.afforestedArea\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"道路面积(亩)\">\n              <a-input v-model=\"info_form.roadArea\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"停车场面积(亩)\">\n              <a-input v-model=\"info_form.parkingArea\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n\n        <a-form-model-item label=\"社区简介\" :labelCol=\" { span: 4 }\">\n          <a-input v-model=\"info_form.desc\" type=\"textarea\" />\n        </a-form-model-item>\n\n        <a-form-model-item style=\"margin-left: 16.6%;\">\n          <a-button type=\"primary\" @click=\"Save_RqInfo\">保存</a-button>\n          <a-button style=\"margin-left: 10px;\" @click=\"Get_RqInfo\">重置</a-button>\n        </a-form-model-item>\n      </a-form-model>\n    </a-card>\n  </page-main>\n</template>\n<script>\nimport { getRqInfo, saveRqInfo } from '@/api/requests/rq-manage.js'\nexport default {\n  data () {\n    return {\n      loading: false,\n      info_form: {},\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n    }\n  },\n  created () {\n    this.Get_RqInfo()\n  },\n  methods: {\n    Get_RqInfo () {\n      getRqInfo().then(res => {\n        console.log(res);\n        this.info_form = JSON.parse(res.data.text)\n      })\n    },\n    Save_RqInfo () {\n      saveRqInfo(this.info_form).then(res => {\n        console.log(res)\n      })\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.ant-form-item {\n    margin-bottom: 15px;\n}\nlabel {\n    width: 200px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}