{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nexports.default = function () {\n  if (_vue2.default.prototype.$isServer) return 0;\n  if (scrollBarWidth !== undefined) return scrollBarWidth;\n  var outer = document.createElement('div');\n  outer.className = 'el-scrollbar__wrap';\n  outer.style.visibility = 'hidden';\n  outer.style.width = '100px';\n  outer.style.position = 'absolute';\n  outer.style.top = '-9999px';\n  document.body.appendChild(outer);\n  var widthNoScroll = outer.offsetWidth;\n  outer.style.overflow = 'scroll';\n  var inner = document.createElement('div');\n  inner.style.width = '100%';\n  outer.appendChild(inner);\n  var widthWithScroll = inner.offsetWidth;\n  outer.parentNode.removeChild(outer);\n  scrollBarWidth = widthNoScroll - widthWithScroll;\n  return scrollBarWidth;\n};\nvar _vue = require('vue');\nvar _vue2 = _interopRequireDefault(_vue);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar scrollBarWidth = void 0;\n;", "map": {"version": 3, "names": ["exports", "__esModule", "default", "_vue2", "prototype", "$isServer", "scrollBarWidth", "undefined", "outer", "document", "createElement", "className", "style", "visibility", "width", "position", "top", "body", "append<PERSON><PERSON><PERSON>", "widthNoScroll", "offsetWidth", "overflow", "inner", "widthWithScroll", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_vue", "require", "_interopRequireDefault", "obj"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/lib/utils/scrollbar-width.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nexports.default = function () {\n  if (_vue2.default.prototype.$isServer) return 0;\n  if (scrollBarWidth !== undefined) return scrollBarWidth;\n\n  var outer = document.createElement('div');\n  outer.className = 'el-scrollbar__wrap';\n  outer.style.visibility = 'hidden';\n  outer.style.width = '100px';\n  outer.style.position = 'absolute';\n  outer.style.top = '-9999px';\n  document.body.appendChild(outer);\n\n  var widthNoScroll = outer.offsetWidth;\n  outer.style.overflow = 'scroll';\n\n  var inner = document.createElement('div');\n  inner.style.width = '100%';\n  outer.appendChild(inner);\n\n  var widthWithScroll = inner.offsetWidth;\n  outer.parentNode.removeChild(outer);\n  scrollBarWidth = widthNoScroll - widthWithScroll;\n\n  return scrollBarWidth;\n};\n\nvar _vue = require('vue');\n\nvar _vue2 = _interopRequireDefault(_vue);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar scrollBarWidth = void 0;\n\n;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzBD,OAAO,CAACE,OAAO,GAAG,YAAY;EAC5B,IAAIC,KAAK,CAACD,OAAO,CAACE,SAAS,CAACC,SAAS,EAAE,OAAO,CAAC;EAC/C,IAAIC,cAAc,KAAKC,SAAS,EAAE,OAAOD,cAAc;EAEvD,IAAIE,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCF,KAAK,CAACG,SAAS,GAAG,oBAAoB;EACtCH,KAAK,CAACI,KAAK,CAACC,UAAU,GAAG,QAAQ;EACjCL,KAAK,CAACI,KAAK,CAACE,KAAK,GAAG,OAAO;EAC3BN,KAAK,CAACI,KAAK,CAACG,QAAQ,GAAG,UAAU;EACjCP,KAAK,CAACI,KAAK,CAACI,GAAG,GAAG,SAAS;EAC3BP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,KAAK,CAAC;EAEhC,IAAIW,aAAa,GAAGX,KAAK,CAACY,WAAW;EACrCZ,KAAK,CAACI,KAAK,CAACS,QAAQ,GAAG,QAAQ;EAE/B,IAAIC,KAAK,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCY,KAAK,CAACV,KAAK,CAACE,KAAK,GAAG,MAAM;EAC1BN,KAAK,CAACU,WAAW,CAACI,KAAK,CAAC;EAExB,IAAIC,eAAe,GAAGD,KAAK,CAACF,WAAW;EACvCZ,KAAK,CAACgB,UAAU,CAACC,WAAW,CAACjB,KAAK,CAAC;EACnCF,cAAc,GAAGa,aAAa,GAAGI,eAAe;EAEhD,OAAOjB,cAAc;AACvB,CAAC;AAED,IAAIoB,IAAI,GAAGC,OAAO,CAAC,KAAK,CAAC;AAEzB,IAAIxB,KAAK,GAAGyB,sBAAsB,CAACF,IAAI,CAAC;AAExC,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAAC5B,UAAU,GAAG4B,GAAG,GAAG;IAAE3B,OAAO,EAAE2B;EAAI,CAAC;AAAE;AAE9F,IAAIvB,cAAc,GAAG,KAAK,CAAC;AAE3B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}