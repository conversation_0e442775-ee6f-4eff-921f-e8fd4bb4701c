{"ast": null, "code": "import api from '../index';\n\n/**\n * 获取系统统计概览数据\n */\nexport var getOverview = function getOverview() {\n  return api.get('/system/statistics/overview');\n};\n\n/**\n * 获取最新报修记录\n */\nexport var getRecentRepairs = function getRecentRepairs() {\n  return api.get('/system/statistics/recent/repairs');\n};\n\n/**\n * 获取最新投诉记录\n */\nexport var getRecentComplaints = function getRecentComplaints() {\n  return api.get('/system/statistics/recent/complaints');\n};\n\n/**\n * 获取月度统计数据\n */\nexport var getMonthlyStats = function getMonthlyStats() {\n  return api.get('/system/statistics/monthly');\n};\n\n/**\n * 获取楼宇列表\n */\nexport var getBuildingList = function getBuildingList() {\n  return api.get('/system/building/list');\n};\n\n/**\n * 获取房间列表\n */\nexport var getRoomList = function getRoomList() {\n  return api.get('/system/room/list');\n};\n\n/**\n * 获取用户列表\n */\nexport var getUserList = function getUserList() {\n  return api.get('/system/user/list');\n};\n\n/**\n * 获取报修列表\n */\nexport var getRepairList = function getRepairList() {\n  return api.get('/system/repair/list');\n};\n\n/**\n * 获取投诉列表\n */\nexport var getComplaintList = function getComplaintList() {\n  return api.get('/system/complaint/list');\n};", "map": {"version": 3, "names": ["api", "getOverview", "get", "getRecentRepairs", "getRecentComplaints", "getMonthlyStats", "getBuildingList", "getRoomList", "getUserList", "getRepairList", "getComplaintList"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/api/requests/statistics.js"], "sourcesContent": ["import api from '../index'\n\n/**\n * 获取系统统计概览数据\n */\nexport const getOverview = () => {\n  return api.get('/system/statistics/overview')\n}\n\n/**\n * 获取最新报修记录\n */\nexport const getRecentRepairs = () => {\n  return api.get('/system/statistics/recent/repairs')\n}\n\n/**\n * 获取最新投诉记录\n */\nexport const getRecentComplaints = () => {\n  return api.get('/system/statistics/recent/complaints')\n}\n\n/**\n * 获取月度统计数据\n */\nexport const getMonthlyStats = () => {\n  return api.get('/system/statistics/monthly')\n}\n\n/**\n * 获取楼宇列表\n */\nexport const getBuildingList = () => {\n  return api.get('/system/building/list')\n}\n\n/**\n * 获取房间列表\n */\nexport const getRoomList = () => {\n  return api.get('/system/room/list')\n}\n\n/**\n * 获取用户列表\n */\nexport const getUserList = () => {\n  return api.get('/system/user/list')\n}\n\n/**\n * 获取报修列表\n */\nexport const getRepairList = () => {\n  return api.get('/system/repair/list')\n}\n\n/**\n * 获取投诉列表\n */\nexport const getComplaintList = () => {\n  return api.get('/system/complaint/list')\n}\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;;AAE1B;AACA;AACA;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EAC/B,OAAOD,GAAG,CAACE,GAAG,CAAC,6BAA6B,CAAC;AAC/C,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EACpC,OAAOH,GAAG,CAACE,GAAG,CAAC,mCAAmC,CAAC;AACrD,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAME,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;EACvC,OAAOJ,GAAG,CAACE,GAAG,CAAC,sCAAsC,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EACnC,OAAOL,GAAG,CAACE,GAAG,CAAC,4BAA4B,CAAC;AAC9C,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMI,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EACnC,OAAON,GAAG,CAACE,GAAG,CAAC,uBAAuB,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMK,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EAC/B,OAAOP,GAAG,CAACE,GAAG,CAAC,mBAAmB,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMM,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EAC/B,OAAOR,GAAG,CAACE,GAAG,CAAC,mBAAmB,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMO,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EACjC,OAAOT,GAAG,CAACE,GAAG,CAAC,qBAAqB,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMQ,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;EACpC,OAAOV,GAAG,CAACE,GAAG,CAAC,wBAAwB,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}