{"ast": null, "code": "import \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.string.search.js\";\nimport { deepClone } from '@/util';\nexport default {\n  name: 'Search',\n  props: {},\n  data: function data() {\n    return {\n      isShow: false,\n      search: '',\n      sourceList: []\n    };\n  },\n  computed: {\n    resultList: function resultList() {\n      var _this = this;\n      var result = [];\n      result = this.sourceList.filter(function (item) {\n        var flag = false;\n        if (item.title.indexOf(_this.search) >= 0) {\n          flag = true;\n        }\n        if (item.path.indexOf(_this.search) >= 0) {\n          flag = true;\n        }\n        if (item.breadcrumb.some(function (b) {\n          return b.indexOf(_this.search) >= 0;\n        })) {\n          flag = true;\n        }\n        return flag;\n      });\n      return result;\n    }\n  },\n  watch: {\n    isShow: function isShow(val) {\n      var _this2 = this;\n      if (val) {\n        document.querySelector('body').classList.add('hidden');\n        this.$refs.search.scrollTop = 0;\n        setTimeout(function () {\n          _this2.$refs.input.$el.children[0].focus();\n        }, 100);\n      } else {\n        document.querySelector('body').classList.remove('hidden');\n        setTimeout(function () {\n          _this2.search = '';\n        }, 500);\n      }\n    }\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    var _this3 = this;\n    this.$eventBus.$on('global-search-toggle', function () {\n      _this3.isShow = !_this3.isShow;\n    });\n    this.$store.state.menu.routes.map(function (item) {\n      _this3.getSourceList(item.children);\n    });\n  },\n  methods: {\n    isExternal: function isExternal(path) {\n      return /^(https?:|mailto:|tel:)/.test(path);\n    },\n    hasChildren: function hasChildren(item) {\n      var flag = true;\n      if (item.children) {\n        if (item.children.every(function (i) {\n          return i.meta.sidebar === false;\n        })) {\n          flag = false;\n        }\n      } else {\n        flag = false;\n      }\n      return flag;\n    },\n    getSourceList: function getSourceList(arr) {\n      var _this4 = this;\n      arr.map(function (item) {\n        if (item.meta.sidebar !== false) {\n          if (_this4.hasChildren(item)) {\n            var baseBreadcrumb = item.meta.baseBreadcrumb ? deepClone(item.meta.baseBreadcrumb) : [];\n            baseBreadcrumb.push(item.meta.title);\n            var child = deepClone(item.children);\n            child.map(function (c) {\n              c.meta.baseIcon = item.meta.icon || item.meta.baseIcon;\n              c.meta.baseBreadcrumb = baseBreadcrumb;\n              c.meta.basePath = item.meta.basePath ? [item.meta.basePath, item.path].join('/') : item.path;\n            });\n            _this4.getSourceList(child);\n          } else {\n            var breadcrumb = [];\n            if (item.meta.baseBreadcrumb) {\n              breadcrumb = deepClone(item.meta.baseBreadcrumb);\n            }\n            breadcrumb.push(item.meta.title);\n            var path = '';\n            if (_this4.isExternal(item.path)) {\n              path = item.path;\n            } else {\n              path = item.meta.basePath ? [item.meta.basePath, item.path].join('/') : item.path;\n            }\n            _this4.sourceList.push({\n              icon: item.meta.icon || item.meta.baseIcon,\n              title: item.meta.title,\n              breadcrumb: breadcrumb,\n              path: path,\n              isExternal: _this4.isExternal(item.path)\n            });\n          }\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;AAkCA;AAEA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACAC;QACA;QACA;UACAC;QACA;QACA;UACAA;QACA;QACA;UAAA;QAAA;UACAA;QACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACAP;MAAA;MACA;QACAQ;QACA;QACAC;UACAC;QACA;MACA;QACAF;QACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;EACAC;IAAA;IACA;MACAC;IACA;IACA;MACAA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;UAAA;QAAA;UACAV;QACA;MACA;QACAA;MACA;MACA;IACA;IACAW;MAAA;MACAC;QACA;UACA;YACA;YACAC;YACA;YACAC;cACAC;cACAA;cACAA;YACA;YACAC;UACA;YACA;YACA;cACAC;YACA;YACAA;YACA;YACA;cACAC;YACA;cACAA;YACA;YACAF;cACAG;cACAC;cACAH;cACAC;cACAT;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "data", "isShow", "search", "sourceList", "computed", "resultList", "result", "flag", "watch", "document", "setTimeout", "_this2", "created", "mounted", "_this3", "methods", "isExternal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getSourceList", "arr", "baseBreadcrumb", "child", "c", "_this4", "breadcrumb", "path", "icon", "title"], "sourceRoot": "src/layout/components/Search", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div id=\"search\" :class=\"{'searching': isShow}\" @click=\"isShow && $eventBus.$emit('global-search-toggle')\">\n        <div class=\"container\">\n            <div class=\"search-box\" @click.stop>\n                <el-input ref=\"input\" v-model=\"search\" prefix-icon=\"el-icon-search\" placeholder=\"搜索页面\" clearable @keydown.esc.native=\"$eventBus.$emit('global-search-toggle')\" />\n                <div class=\"tips\">你可以使用快捷键<span>alt</span>+<span>s</span>唤醒搜索面板，按<span>esc</span>退出</div>\n            </div>\n            <div ref=\"search\" class=\"result\">\n                <router-link v-for=\"item in resultList\" :key=\"item.path\" v-slot=\"{ href, navigate }\" custom :to=\"isShow ? item.path : ''\">\n                    <a :href=\"isExternal(item.path) ? item.path : href\" class=\"item\" :target=\"isExternal(item.path) ? '_blank' : '_self'\" @click=\"navigate\">\n                        <div class=\"icon\">\n                            <svg-icon v-if=\"item.icon\" :name=\"item.icon\" />\n                        </div>\n                        <div class=\"info\">\n                            <div class=\"title\">\n                                {{ item.title }}\n                                <svg-icon v-if=\"item.isExternal\" name=\"external-link\" />\n                            </div>\n                            <div class=\"breadcrumb\">\n                                <span v-for=\"(bc, index) in item.breadcrumb\" :key=\"index\">\n                                    {{ bc }}\n                                    <i class=\"el-icon-arrow-right\" />\n                                </span>\n                            </div>\n                            <div class=\"path\">{{ item.path }}</div>\n                        </div>\n                    </a>\n                </router-link>\n            </div>\n        </div>\n    </div>\n</template>\n\n<script>\nimport { deepClone } from '@/util'\n\nexport default {\n    name: 'Search',\n    props: {},\n    data() {\n        return {\n            isShow: false,\n            search: '',\n            sourceList: []\n        }\n    },\n    computed: {\n        resultList() {\n            let result = []\n            result = this.sourceList.filter(item => {\n                let flag = false\n                if (item.title.indexOf(this.search) >= 0) {\n                    flag = true\n                }\n                if (item.path.indexOf(this.search) >= 0) {\n                    flag = true\n                }\n                if (item.breadcrumb.some(b => b.indexOf(this.search) >= 0)) {\n                    flag = true\n                }\n                return flag\n            })\n            return result\n        }\n    },\n    watch: {\n        isShow(val) {\n            if (val) {\n                document.querySelector('body').classList.add('hidden')\n                this.$refs.search.scrollTop = 0\n                setTimeout(() => {\n                    this.$refs.input.$el.children[0].focus()\n                }, 100)\n            } else {\n                document.querySelector('body').classList.remove('hidden')\n                setTimeout(() => {\n                    this.search = ''\n                }, 500)\n            }\n        }\n    },\n    created() {},\n    mounted() {\n        this.$eventBus.$on('global-search-toggle', () => {\n            this.isShow = !this.isShow\n        })\n        this.$store.state.menu.routes.map(item => {\n            this.getSourceList(item.children)\n        })\n    },\n    methods: {\n        isExternal(path) {\n            return /^(https?:|mailto:|tel:)/.test(path)\n        },\n        hasChildren(item) {\n            let flag = true\n            if (item.children) {\n                if (item.children.every(i => i.meta.sidebar === false)) {\n                    flag = false\n                }\n            } else {\n                flag = false\n            }\n            return flag\n        },\n        getSourceList(arr) {\n            arr.map(item => {\n                if (item.meta.sidebar !== false) {\n                    if (this.hasChildren(item)) {\n                        let baseBreadcrumb = item.meta.baseBreadcrumb ? deepClone(item.meta.baseBreadcrumb) : []\n                        baseBreadcrumb.push(item.meta.title)\n                        let child = deepClone(item.children)\n                        child.map(c => {\n                            c.meta.baseIcon = item.meta.icon || item.meta.baseIcon\n                            c.meta.baseBreadcrumb = baseBreadcrumb\n                            c.meta.basePath = item.meta.basePath ? [item.meta.basePath, item.path].join('/') : item.path\n                        })\n                        this.getSourceList(child)\n                    } else {\n                        let breadcrumb = []\n                        if (item.meta.baseBreadcrumb) {\n                            breadcrumb = deepClone(item.meta.baseBreadcrumb)\n                        }\n                        breadcrumb.push(item.meta.title)\n                        let path = ''\n                        if (this.isExternal(item.path)) {\n                            path = item.path\n                        } else {\n                            path = item.meta.basePath ? [item.meta.basePath, item.path].join('/') : item.path\n                        }\n                        this.sourceList.push({\n                            icon: item.meta.icon || item.meta.baseIcon,\n                            title: item.meta.title,\n                            breadcrumb: breadcrumb,\n                            path: path,\n                            isExternal: this.isExternal(item.path)\n                        })\n                    }\n                }\n            })\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n#search {\n    position: fixed;\n    z-index: 2000;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba($color: #000, $alpha: 0.5);\n    backdrop-filter: blur(10px);\n    transition: all 0.2s;\n    transform: translateZ(0);\n    opacity: 0;\n    visibility: hidden;\n    &.searching {\n        opacity: 1;\n        visibility: visible;\n        .container {\n            transform: initial;\n            filter: initial;\n        }\n    }\n    .container {\n        display: flex;\n        flex-direction: column;\n        max-width: 800px;\n        height: 100%;\n        margin: 0 auto;\n        transition: all 0.2s;\n        transform: scale(1.1);\n        filter: blur(10px);\n        .search-box {\n            margin: 50px 20px 0;\n            ::v-deep .el-input__inner {\n                height: 52px;\n                line-height: 52px;\n            }\n            .tips {\n                margin: 20px 0 40px;\n                line-height: 24px;\n                font-size: 14px;\n                text-align: center;\n                color: #fff;\n                span {\n                    margin: 0 5px;\n                    padding: 1px 5px 2px;\n                    border-radius: 5px;\n                    font-weight: bold;\n                    background-color: rgba($color: #000, $alpha: 0.5);\n                }\n            }\n        }\n        .result {\n            margin: 0 20px;\n            max-height: calc(100% - 250px);\n            border-radius: 5px;\n            overflow: auto;\n            background-color: #fff;\n            .item {\n                display: flex;\n                align-items: center;\n                text-decoration: none;\n                cursor: pointer;\n                &:hover {\n                    transition: all 0.3s;\n                    background-color: #f5f7fa;\n                    .icon .svg-icon {\n                        color: #409eff;\n                        transform: scale(1.2);\n                    }\n                    .info {\n                        .title {\n                            color: #333;\n                        }\n                        .breadcrumb,\n                        .path {\n                            color: #606266;\n                        }\n                    }\n                }\n                .icon {\n                    flex: 0 0 66px;\n                    text-align: center;\n                    .svg-icon {\n                        color: #999;\n                        font-size: 20px;\n                        transition: all 0.3s;\n                    }\n                }\n                .info {\n                    flex: 1;\n                    height: 70px;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: space-around;\n                    border-left: 1px solid #ebeef5;\n                    padding: 5px 10px 7px;\n                    @include text-overflow(1, true);\n                    .title {\n                        font-size: 18px;\n                        font-weight: bold;\n                        color: #666;\n                        @include text-overflow(1, true);\n                        .svg-icon {\n                            font-size: 14px;\n                        }\n                    }\n                    .breadcrumb,\n                    .path {\n                        font-size: 12px;\n                        color: #c0c4cc;\n                        transition: all 0.3s;\n                        @include text-overflow(1, true);\n                    }\n                    .breadcrumb {\n                        span:last-child i {\n                            display: none;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}