{"ast": null, "code": "import \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nexport default {\n  name: 'ImageUpload',\n  props: {\n    action: {\n      type: String,\n      required: true\n    },\n    headers: {\n      type: Object,\n      default: function _default() {}\n    },\n    data: {\n      type: Object,\n      default: function _default() {}\n    },\n    name: {\n      type: String,\n      default: 'file'\n    },\n    url: {\n      type: String,\n      default: ''\n    },\n    size: {\n      type: Number,\n      default: 2\n    },\n    width: {\n      type: Number,\n      default: 150\n    },\n    height: {\n      type: Number,\n      default: 150\n    },\n    placeholder: {\n      type: String,\n      default: ''\n    },\n    notip: {\n      type: Boolean,\n      default: false\n    },\n    ext: {\n      type: Array,\n      default: function _default() {\n        return ['jpg', 'png', 'gif', 'bmp'];\n      }\n    }\n  },\n  data: function data() {\n    return {\n      dialogVisible: false,\n      progress: {\n        preview: '',\n        percent: 0\n      }\n    };\n  },\n  methods: {\n    // 移除\n    remove: function remove() {\n      this.$emit('update:url', '');\n    },\n    beforeUpload: function beforeUpload(file) {\n      var fileName = file.name.split('.');\n      var fileExt = fileName[fileName.length - 1];\n      var isTypeOk = this.ext.indexOf(fileExt) >= 0;\n      var isSizeOk = file.size / 1024 / 1024 < this.size;\n      if (!isTypeOk) {\n        this.$message.error(\"\\u4E0A\\u4F20\\u56FE\\u7247\\u53EA\\u652F\\u6301 \".concat(this.ext.join(' / '), \" \\u683C\\u5F0F\\uFF01\"));\n      }\n      if (!isSizeOk) {\n        this.$message.error(\"\\u4E0A\\u4F20\\u56FE\\u7247\\u5927\\u5C0F\\u4E0D\\u80FD\\u8D85\\u8FC7 \".concat(this.size, \"MB\\uFF01\"));\n      }\n      if (isTypeOk && isSizeOk) {\n        this.progress.preview = URL.createObjectURL(file);\n      }\n      return isTypeOk && isSizeOk;\n    },\n    onProgress: function onProgress(file) {\n      var _this = this;\n      this.progress.percent = ~~file.percent;\n      if (this.progress.percent == 100) {\n        setTimeout(function () {\n          _this.progress.preview = '';\n          _this.progress.percent = 0;\n        }, 1000);\n      }\n    },\n    onSuccess: function onSuccess(res) {\n      this.$emit('on-success', res);\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;;;;;;AAgDA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;IACA;IACAN;MACAG;MACAG;IACA;IACAE;MACAL;MACAG;IACA;IACAG;MACAN;MACAG;IACA;IACAI;MACAP;MACAG;IACA;IACAK;MACAR;MACAG;IACA;IACAM;MACAT;MACAG;IACA;IACAO;MACAV;MACAG;IACA;IACAQ;MACAX;MACAG;QAAA;MAAA;IACA;EACA;EACAC;IACA;MACAQ;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;UACAC;UACAA;QACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "props", "action", "type", "required", "headers", "default", "data", "url", "size", "width", "height", "placeholder", "notip", "ext", "dialogVisible", "progress", "preview", "percent", "methods", "remove", "beforeUpload", "onProgress", "setTimeout", "_this", "onSuccess"], "sourceRoot": "src/components/ImageUpload", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"upload-container\">\n        <el-upload\n            :show-file-list=\"false\"\n            :headers=\"headers\"\n            :action=\"action\"\n            :data=\"data\"\n            :name=\"name\"\n            :before-upload=\"beforeUpload\"\n            :on-progress=\"onProgress\"\n            :on-success=\"onSuccess\"\n            drag\n        >\n            <el-image v-if=\"url === ''\" :src=\"url === '' ? placeholder : url\" :style=\"`width:${width}px;height:${height}px;`\" fit=\"fill\">\n                <div slot=\"error\" class=\"image-slot\">\n                    <i class=\"el-icon-plus\" />\n                </div>\n            </el-image>\n            <div v-else class=\"image\">\n                <el-image :src=\"url\" :style=\"`width:${width}px;height:${height}px;`\" fit=\"fill\" />\n                <div class=\"mask\">\n                    <div class=\"actions\">\n                        <span title=\"预览\" @click.stop=\"dialogVisible = true\">\n                            <i class=\"el-icon-zoom-in\" />\n                        </span>\n                        <span title=\"移除\" @click.stop=\"remove\">\n                            <i class=\"el-icon-delete\" />\n                        </span>\n                    </div>\n                </div>\n            </div>\n            <div v-show=\"progress.percent\" class=\"progress\" :style=\"`width:${width}px;height:${height}px;`\">\n                <el-image :src=\"progress.preview\" :style=\"`width:${width}px;height:${height}px;`\" fit=\"fill\" />\n                <el-progress type=\"circle\" :width=\"Math.min(width, height) * 0.8\" :percentage=\"progress.percent\" />\n            </div>\n        </el-upload>\n        <div v-if=\"!notip\" class=\"el-upload__tip\">\n            <div style=\"display: inline-block;\">\n                <el-alert :title=\"`上传图片支持 ${ ext.join(' / ') } 格式，且图片大小不超过 ${ size }MB，建议图片尺寸为 ${width}*${height}`\" type=\"info\" show-icon :closable=\"false\" />\n            </div>\n        </div>\n        <el-dialog :visible.sync=\"dialogVisible\" title=\"预览\" width=\"800px\">\n            <img :src=\"url\" style=\"display: block; max-width: 100%; margin: 0 auto;\">\n        </el-dialog>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'ImageUpload',\n    props: {\n        action: {\n            type: String,\n            required: true\n        },\n        headers: {\n            type: Object,\n            default: () => {}\n        },\n        data: {\n            type: Object,\n            default: () => {}\n        },\n        name: {\n            type: String,\n            default: 'file'\n        },\n        url: {\n            type: String,\n            default: ''\n        },\n        size: {\n            type: Number,\n            default: 2\n        },\n        width: {\n            type: Number,\n            default: 150\n        },\n        height: {\n            type: Number,\n            default: 150\n        },\n        placeholder: {\n            type: String,\n            default: ''\n        },\n        notip: {\n            type: Boolean,\n            default: false\n        },\n        ext: {\n            type: Array,\n            default: () => ['jpg', 'png', 'gif', 'bmp']\n        }\n    },\n    data() {\n        return {\n            dialogVisible: false,\n            progress: {\n                preview: '',\n                percent: 0\n            }\n        }\n    },\n    methods: {\n        // 移除\n        remove() {\n            this.$emit('update:url', '')\n        },\n        beforeUpload(file) {\n            const fileName = file.name.split('.')\n            const fileExt = fileName[fileName.length - 1]\n            const isTypeOk = this.ext.indexOf(fileExt) >= 0\n            const isSizeOk = file.size / 1024 / 1024 < this.size\n            if (!isTypeOk) {\n                this.$message.error(`上传图片只支持 ${ this.ext.join(' / ') } 格式！`)\n            }\n            if (!isSizeOk) {\n                this.$message.error(`上传图片大小不能超过 ${this.size}MB！`)\n            }\n            if (isTypeOk && isSizeOk) {\n                this.progress.preview = URL.createObjectURL(file)\n            }\n            return isTypeOk && isSizeOk\n        },\n        onProgress(file) {\n            this.progress.percent = ~~file.percent\n            if (this.progress.percent == 100) {\n                setTimeout(() => {\n                    this.progress.preview = ''\n                    this.progress.percent = 0\n                }, 1000)\n            }\n        },\n        onSuccess(res) {\n            this.$emit('on-success', res)\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.upload-container {\n    line-height: initial;\n}\n.image {\n    position: relative;\n    .mask {\n        opacity: 0;\n        position: absolute;\n        top: 0;\n        width: 100%;\n        height: 100%;\n        font-size: 24px;\n        background-color: rgba(0, 0, 0, 0.5);\n        transition: all 0.3s;\n        .actions {\n            width: 100px;\n            height: 100px;\n            display: flex;\n            flex-wrap: wrap;\n            align-items: center;\n            justify-content: center;\n            @include position-center(xy);\n            span {\n                width: 50%;\n                text-align: center;\n                color: #fff;\n                cursor: pointer;\n                transition: all 0.1s;\n                &.disabled {\n                    color: #999;\n                    cursor: not-allowed;\n                }\n                &:hover:not(.disabled) {\n                    transform: scale(1.5);\n                }\n            }\n        }\n    }\n    &:hover .mask {\n        opacity: 1;\n    }\n}\n::v-deep .el-upload {\n    .el-upload-dragger {\n        width: auto;\n        height: auto;\n        &.is-dragover {\n            border-width: 1px;\n        }\n        .el-image {\n            display: block;\n            .image-slot {\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                width: 100%;\n                height: 100%;\n                color: #909399;\n                font-size: 30px;\n                background-color: transparent;\n            }\n        }\n        .progress {\n            position: absolute;\n            top: 0;\n            &::after {\n                content: '';\n                position: absolute;\n                width: 100%;\n                height: 100%;\n                left: 0;\n                top: 0;\n                background-color: rgba(0, 0, 0, 0.2);\n            }\n            .el-progress {\n                z-index: 1;\n                @include position-center(xy);\n                .el-progress__text {\n                    color: #fff;\n                }\n            }\n        }\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}