# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/colors@^3.1.0":
  version "3.2.2"
  resolved "https://registry.yarnpkg.com/@ant-design/colors/-/colors-3.2.2.tgz#5ad43d619e911f3488ebac303d606e66a8423903"
  integrity sha512-YKgNbG2dlzqMhA9NtI3/pbY16m3Yl/EeWBRa+lB1X1YaYxHrxNexiQYCLTWO/uDvAjLFMEDU+zR901waBtMtjQ==
  dependencies:
    tinycolor2 "^1.4.1"

"@ant-design/icons-vue@^2.0.0":
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/@ant-design/icons-vue/-/icons-vue-2.0.0.tgz#0357f5010a404e9f34a87a4b41b2a08df691dbce"
  integrity sha512-2c0QQE5hL4N48k5NkPG5sdpMl9YnvyNhf0U7YkdZYDlLnspoRU7vIA0UK9eHBs6OpFLcJB6o8eJrIl2ajBskPg==
  dependencies:
    "@ant-design/colors" "^3.1.0"
    babel-runtime "^6.26.0"

"@ant-design/icons@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@ant-design/icons/-/icons-2.1.1.tgz#7b9c08dffd4f5d41db667d9dbe5e0107d0bd9a4a"
  integrity sha512-jCH+k2Vjlno4YWl6g535nHR09PwCEmTBKAG6VqF+rhkrSPRLfgpU2maagwbZPLjaHuU5Jd1DFQ2KJpQuI6uG8w==

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.12.11":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz#f4ad435aa263db935b8f10f2c552d23fb716a63f"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.12.5", "@babel/compat-data@^7.12.7":
  version "7.12.7"
  resolved "https://registry.npm.taobao.org/@babel/compat-data/download/@babel/compat-data-7.12.7.tgz?cache=0&sync_timestamp=1605904546282&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcompat-data%2Fdownload%2F%40babel%2Fcompat-data-7.12.7.tgz#9329b4782a7d6bbd7eef57e11addf91ee3ef1e41"
  integrity sha1-kym0eCp9a71+71fhGt35HuPvHkE=

"@babel/core@>=7.9.0", "@babel/core@^7.11.0":
  version "7.12.10"
  resolved "https://registry.npm.taobao.org/@babel/core/download/@babel/core-7.12.10.tgz#b79a2e1b9f70ed3d84bbfb6d8c4ef825f606bccd"
  integrity sha1-t5ouG59w7T2Eu/ttjE74JfYGvM0=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.12.10"
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helpers" "^7.12.5"
    "@babel/parser" "^7.12.10"
    "@babel/template" "^7.12.7"
    "@babel/traverse" "^7.12.10"
    "@babel/types" "^7.12.10"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.1"
    json5 "^2.1.2"
    lodash "^4.17.19"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/generator@^7.12.10", "@babel/generator@^7.12.11":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/generator/download/@babel/generator-7.12.11.tgz?cache=0&sync_timestamp=1608075197769&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fgenerator%2Fdownload%2F%40babel%2Fgenerator-7.12.11.tgz#98a7df7b8c358c9a37ab07a24056853016aba3af"
  integrity sha1-mKffe4w1jJo3qweiQFaFMBaro68=
  dependencies:
    "@babel/types" "^7.12.11"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.10.4":
  version "7.12.10"
  resolved "https://registry.npm.taobao.org/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.12.10.tgz?cache=0&sync_timestamp=1607583999574&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-annotate-as-pure%2Fdownload%2F%40babel%2Fhelper-annotate-as-pure-7.12.10.tgz#54ab9b000e60a93644ce17b3f37d313aaf1d115d"
  integrity sha1-VKubAA5gqTZEzhez830xOq8dEV0=
  dependencies:
    "@babel/types" "^7.12.10"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npm.taobao.org/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.10.4.tgz#bb0b75f31bf98cbf9ff143c1ae578b87274ae1a3"
  integrity sha1-uwt18xv5jL+f8UPBrleLhydK4aM=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helper-compilation-targets@^7.12.5", "@babel/helper-compilation-targets@^7.9.6":
  version "7.12.5"
  resolved "https://registry.npm.taobao.org/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.12.5.tgz?cache=0&sync_timestamp=1604441460288&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-compilation-targets%2Fdownload%2F%40babel%2Fhelper-compilation-targets-7.12.5.tgz#cb470c76198db6a24e9dbc8987275631e5d29831"
  integrity sha1-y0cMdhmNtqJOnbyJhydWMeXSmDE=
  dependencies:
    "@babel/compat-data" "^7.12.5"
    "@babel/helper-validator-option" "^7.12.1"
    browserslist "^4.14.5"
    semver "^5.5.0"

"@babel/helper-create-class-features-plugin@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.12.1.tgz?cache=0&sync_timestamp=1602802212738&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-create-class-features-plugin%2Fdownload%2F%40babel%2Fhelper-create-class-features-plugin-7.12.1.tgz#3c45998f431edd4a9214c5f1d3ad1448a6137f6e"
  integrity sha1-PEWZj0Me3UqSFMXx060USKYTf24=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-member-expression-to-functions" "^7.12.1"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-replace-supers" "^7.12.1"
    "@babel/helper-split-export-declaration" "^7.10.4"

"@babel/helper-create-regexp-features-plugin@^7.12.1":
  version "7.12.7"
  resolved "https://registry.npm.taobao.org/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.12.7.tgz?cache=0&sync_timestamp=1605904548973&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-create-regexp-features-plugin%2Fdownload%2F%40babel%2Fhelper-create-regexp-features-plugin-7.12.7.tgz#2084172e95443fa0a09214ba1bb328f9aea1278f"
  integrity sha1-IIQXLpVEP6CgkhS6G7Mo+a6hJ48=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    regexpu-core "^4.7.1"

"@babel/helper-define-map@^7.10.4":
  version "7.10.5"
  resolved "https://registry.npm.taobao.org/@babel/helper-define-map/download/@babel/helper-define-map-7.10.5.tgz#b53c10db78a640800152692b13393147acb9bb30"
  integrity sha1-tTwQ23imQIABUmkrEzkxR6y5uzA=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/types" "^7.10.5"
    lodash "^4.17.19"

"@babel/helper-explode-assignable-expression@^7.10.4":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.12.1.tgz?cache=0&sync_timestamp=1602800394981&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-explode-assignable-expression%2Fdownload%2F%40babel%2Fhelper-explode-assignable-expression-7.12.1.tgz#8006a466695c4ad86a2a5f2fb15b5f2c31ad5633"
  integrity sha1-gAakZmlcSthqKl8vsVtfLDGtVjM=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-function-name@^7.10.4", "@babel/helper-function-name@^7.12.11":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/helper-function-name/download/@babel/helper-function-name-7.12.11.tgz?cache=0&sync_timestamp=1608075198536&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-function-name%2Fdownload%2F%40babel%2Fhelper-function-name-7.12.11.tgz#1fd7738aee5dcf53c3ecff24f1da9c511ec47b42"
  integrity sha1-H9dziu5dz1PD7P8k8dqcUR7Ee0I=
  dependencies:
    "@babel/helper-get-function-arity" "^7.12.10"
    "@babel/template" "^7.12.7"
    "@babel/types" "^7.12.11"

"@babel/helper-get-function-arity@^7.12.10":
  version "7.12.10"
  resolved "https://registry.npm.taobao.org/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.12.10.tgz?cache=0&sync_timestamp=1607584476842&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-get-function-arity%2Fdownload%2F%40babel%2Fhelper-get-function-arity-7.12.10.tgz#b158817a3165b5faa2047825dfa61970ddcc16cf"
  integrity sha1-sViBejFltfqiBHgl36YZcN3MFs8=
  dependencies:
    "@babel/types" "^7.12.10"

"@babel/helper-hoist-variables@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npm.taobao.org/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.10.4.tgz#d49b001d1d5a68ca5e6604dda01a6297f7c9381e"
  integrity sha1-1JsAHR1aaMpeZgTdoBpil/fJOB4=
  dependencies:
    "@babel/types" "^7.10.4"

"@babel/helper-member-expression-to-functions@^7.12.1", "@babel/helper-member-expression-to-functions@^7.12.7":
  version "7.12.7"
  resolved "https://registry.npm.taobao.org/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.12.7.tgz?cache=0&sync_timestamp=1605904793577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-member-expression-to-functions%2Fdownload%2F%40babel%2Fhelper-member-expression-to-functions-7.12.7.tgz#aa77bd0396ec8114e5e30787efa78599d874a855"
  integrity sha1-qne9A5bsgRTl4weH76eFmdh0qFU=
  dependencies:
    "@babel/types" "^7.12.7"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.1", "@babel/helper-module-imports@^7.12.5", "@babel/helper-module-imports@^7.8.3":
  version "7.12.5"
  resolved "https://registry.npm.taobao.org/@babel/helper-module-imports/download/@babel/helper-module-imports-7.12.5.tgz?cache=0&sync_timestamp=1604443061838&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-module-imports%2Fdownload%2F%40babel%2Fhelper-module-imports-7.12.5.tgz#1bfc0229f794988f76ed0a4d4e90860850b54dfb"
  integrity sha1-G/wCKfeUmI927QpNTpCGCFC1Tfs=
  dependencies:
    "@babel/types" "^7.12.5"

"@babel/helper-module-transforms@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.12.1.tgz?cache=0&sync_timestamp=1602802212083&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-module-transforms%2Fdownload%2F%40babel%2Fhelper-module-transforms-7.12.1.tgz#7954fec71f5b32c48e4b303b437c34453fd7247c"
  integrity sha1-eVT+xx9bMsSOSzA7Q3w0RT/XJHw=
  dependencies:
    "@babel/helper-module-imports" "^7.12.1"
    "@babel/helper-replace-supers" "^7.12.1"
    "@babel/helper-simple-access" "^7.12.1"
    "@babel/helper-split-export-declaration" "^7.11.0"
    "@babel/helper-validator-identifier" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.12.1"
    "@babel/types" "^7.12.1"
    lodash "^4.17.19"

"@babel/helper-optimise-call-expression@^7.10.4", "@babel/helper-optimise-call-expression@^7.12.10":
  version "7.12.10"
  resolved "https://registry.npm.taobao.org/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.12.10.tgz?cache=0&sync_timestamp=1607576973797&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-optimise-call-expression%2Fdownload%2F%40babel%2Fhelper-optimise-call-expression-7.12.10.tgz#94ca4e306ee11a7dd6e9f42823e2ac6b49881e2d"
  integrity sha1-lMpOMG7hGn3W6fQoI+Ksa0mIHi0=
  dependencies:
    "@babel/types" "^7.12.10"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npm.taobao.org/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.10.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-plugin-utils%2Fdownload%2F%40babel%2Fhelper-plugin-utils-7.10.4.tgz#2f75a831269d4f677de49986dff59927533cf375"
  integrity sha1-L3WoMSadT2d95JmG3/WZJ1M883U=

"@babel/helper-remap-async-to-generator@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.12.1.tgz#8c4dbbf916314f6047dc05e6a2217074238347fd"
  integrity sha1-jE27+RYxT2BH3AXmoiFwdCODR/0=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-wrap-function" "^7.10.4"
    "@babel/types" "^7.12.1"

"@babel/helper-replace-supers@^7.12.1":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.12.11.tgz#ea511658fc66c7908f923106dd88e08d1997d60d"
  integrity sha1-6lEWWPxmx5CPkjEG3YjgjRmX1g0=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.12.7"
    "@babel/helper-optimise-call-expression" "^7.12.10"
    "@babel/traverse" "^7.12.10"
    "@babel/types" "^7.12.11"

"@babel/helper-simple-access@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/helper-simple-access/download/@babel/helper-simple-access-7.12.1.tgz#32427e5aa61547d38eb1e6eaf5fd1426fdad9136"
  integrity sha1-MkJ+WqYVR9OOsebq9f0UJv2tkTY=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.12.1.tgz#462dc63a7e435ade8468385c63d2b84cce4b3cbf"
  integrity sha1-Ri3GOn5DWt6EaDhcY9K4TM5LPL8=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-split-export-declaration@^7.10.4", "@babel/helper-split-export-declaration@^7.11.0", "@babel/helper-split-export-declaration@^7.12.11":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.12.11.tgz?cache=0&sync_timestamp=1608075197877&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-split-export-declaration%2Fdownload%2F%40babel%2Fhelper-split-export-declaration-7.12.11.tgz#1b4cc424458643c47d37022223da33d76ea4603a"
  integrity sha1-G0zEJEWGQ8R9NwIiI9oz126kYDo=
  dependencies:
    "@babel/types" "^7.12.11"

"@babel/helper-validator-identifier@^7.10.4", "@babel/helper-validator-identifier@^7.12.11":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.12.11.tgz#c9a1f021917dcb5ccf0d4e453e399022981fc9ed"
  integrity sha1-yaHwIZF9y1zPDU5FPjmQIpgfye0=

"@babel/helper-validator-option@^7.12.1", "@babel/helper-validator-option@^7.12.11":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/helper-validator-option/download/@babel/helper-validator-option-7.12.11.tgz?cache=0&sync_timestamp=1608076830898&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-validator-option%2Fdownload%2F%40babel%2Fhelper-validator-option-7.12.11.tgz#d66cb8b7a3e7fe4c6962b32020a131ecf0847f4f"
  integrity sha1-1my4t6Pn/kxpYrMgIKEx7PCEf08=

"@babel/helper-wrap-function@^7.10.4":
  version "7.12.3"
  resolved "https://registry.npm.taobao.org/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.12.3.tgz#3332339fc4d1fbbf1c27d7958c27d34708e990d9"
  integrity sha1-MzIzn8TR+78cJ9eVjCfTRwjpkNk=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.10.4"
    "@babel/types" "^7.10.4"

"@babel/helpers@^7.12.5":
  version "7.12.5"
  resolved "https://registry.npm.taobao.org/@babel/helpers/download/@babel/helpers-7.12.5.tgz?cache=0&sync_timestamp=1604441458350&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelpers%2Fdownload%2F%40babel%2Fhelpers-7.12.5.tgz#1a1ba4a768d9b58310eda516c449913fe647116e"
  integrity sha1-Ghukp2jZtYMQ7aUWxEmRP+ZHEW4=
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.12.5"
    "@babel/types" "^7.12.5"

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npm.taobao.org/@babel/highlight/download/@babel/highlight-7.10.4.tgz?cache=0&sync_timestamp=1593521251318&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhighlight%2Fdownload%2F%40babel%2Fhighlight-7.10.4.tgz#7d1bdfd65753538fabe6c38596cdb76d9ac60143"
  integrity sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.12.10", "@babel/parser@^7.12.11", "@babel/parser@^7.12.7", "@babel/parser@^7.7.0":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/parser/download/@babel/parser-7.12.11.tgz?cache=0&sync_timestamp=1608076906959&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fparser%2Fdownload%2F%40babel%2Fparser-7.12.11.tgz#9ce3595bcd74bc5c466905e86c535b8b25011e79"
  integrity sha1-nONZW810vFxGaQXobFNbiyUBHnk=

"@babel/plugin-proposal-async-generator-functions@^7.12.1":
  version "7.12.12"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.12.12.tgz?cache=0&sync_timestamp=1608730478113&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-async-generator-functions%2Fdownload%2F%40babel%2Fplugin-proposal-async-generator-functions-7.12.12.tgz#04b8f24fd4532008ab4e79f788468fd5a8476566"
  integrity sha1-BLjyT9RTIAirTnn3iEaP1ahHZWY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.12.1"
    "@babel/plugin-syntax-async-generators" "^7.8.0"

"@babel/plugin-proposal-class-properties@^7.12.1", "@babel/plugin-proposal-class-properties@^7.8.3":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.12.1.tgz?cache=0&sync_timestamp=1602802214147&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-class-properties%2Fdownload%2F%40babel%2Fplugin-proposal-class-properties-7.12.1.tgz#a082ff541f2a29a4821065b8add9346c0c16e5de"
  integrity sha1-oIL/VB8qKaSCEGW4rdk0bAwW5d4=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-decorators@^7.8.3":
  version "7.12.12"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.12.12.tgz?cache=0&sync_timestamp=1608731089426&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-decorators%2Fdownload%2F%40babel%2Fplugin-proposal-decorators-7.12.12.tgz#067a6d3d6ca86d54cf56bb183239199c20daeafe"
  integrity sha1-BnptPWyobVTPVrsYMjkZnCDa6v4=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-decorators" "^7.12.1"

"@babel/plugin-proposal-dynamic-import@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.12.1.tgz?cache=0&sync_timestamp=1602802196029&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-dynamic-import%2Fdownload%2F%40babel%2Fplugin-proposal-dynamic-import-7.12.1.tgz#43eb5c2a3487ecd98c5c8ea8b5fdb69a2749b2dc"
  integrity sha1-Q+tcKjSH7NmMXI6otf22midJstw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-dynamic-import" "^7.8.0"

"@babel/plugin-proposal-export-namespace-from@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.12.1.tgz?cache=0&sync_timestamp=1602802194581&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-export-namespace-from%2Fdownload%2F%40babel%2Fplugin-proposal-export-namespace-from-7.12.1.tgz#8b9b8f376b2d88f5dd774e4d24a5cc2e3679b6d4"
  integrity sha1-i5uPN2stiPXdd05NJKXMLjZ5ttQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.12.1.tgz?cache=0&sync_timestamp=1602799824802&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-json-strings%2Fdownload%2F%40babel%2Fplugin-proposal-json-strings-7.12.1.tgz#d45423b517714eedd5621a9dfdc03fa9f4eb241c"
  integrity sha1-1FQjtRdxTu3VYhqd/cA/qfTrJBw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.0"

"@babel/plugin-proposal-logical-assignment-operators@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-logical-assignment-operators/download/@babel/plugin-proposal-logical-assignment-operators-7.12.1.tgz?cache=0&sync_timestamp=1602802195229&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-logical-assignment-operators%2Fdownload%2F%40babel%2Fplugin-proposal-logical-assignment-operators-7.12.1.tgz#f2c490d36e1b3c9659241034a5d2cd50263a2751"
  integrity sha1-8sSQ024bPJZZJBA0pdLNUCY6J1E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.12.1.tgz?cache=0&sync_timestamp=1602802194969&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator%2Fdownload%2F%40babel%2Fplugin-proposal-nullish-coalescing-operator-7.12.1.tgz#3ed4fff31c015e7f3f1467f190dbe545cd7b046c"
  integrity sha1-PtT/8xwBXn8/FGfxkNvlRc17BGw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.0"

"@babel/plugin-proposal-numeric-separator@^7.12.7":
  version "7.12.7"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.12.7.tgz?cache=0&sync_timestamp=1605904544979&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-numeric-separator%2Fdownload%2F%40babel%2Fplugin-proposal-numeric-separator-7.12.7.tgz#8bf253de8139099fea193b297d23a9d406ef056b"
  integrity sha1-i/JT3oE5CZ/qGTspfSOp1AbvBWs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.12.1.tgz#def9bd03cea0f9b72283dac0ec22d289c7691069"
  integrity sha1-3vm9A86g+bcig9rA7CLSicdpEGk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-transform-parameters" "^7.12.1"

"@babel/plugin-proposal-optional-catch-binding@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.12.1.tgz?cache=0&sync_timestamp=1602802196469&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-optional-catch-binding%2Fdownload%2F%40babel%2Fplugin-proposal-optional-catch-binding-7.12.1.tgz#ccc2421af64d3aae50b558a71cede929a5ab2942"
  integrity sha1-zMJCGvZNOq5QtVinHO3pKaWrKUI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"

"@babel/plugin-proposal-optional-chaining@^7.12.7":
  version "7.12.7"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.12.7.tgz?cache=0&sync_timestamp=1605904545552&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-optional-chaining%2Fdownload%2F%40babel%2Fplugin-proposal-optional-chaining-7.12.7.tgz#e02f0ea1b5dc59d401ec16fb824679f683d3303c"
  integrity sha1-4C8OobXcWdQB7Bb7gkZ59oPTMDw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.12.1"
    "@babel/plugin-syntax-optional-chaining" "^7.8.0"

"@babel/plugin-proposal-private-methods@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.12.1.tgz?cache=0&sync_timestamp=1602802212950&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-private-methods%2Fdownload%2F%40babel%2Fplugin-proposal-private-methods-7.12.1.tgz#86814f6e7a21374c980c10d38b4493e703f4a389"
  integrity sha1-hoFPbnohN0yYDBDTi0ST5wP0o4k=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-proposal-unicode-property-regex@^7.12.1", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.12.1.tgz?cache=0&sync_timestamp=1602802215129&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-proposal-unicode-property-regex%2Fdownload%2F%40babel%2Fplugin-proposal-unicode-property-regex-7.12.1.tgz#2a183958d417765b9eae334f47758e5d6a82e072"
  integrity sha1-Khg5WNQXdluerjNPR3WOXWqC4HI=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-async-generators@^7.8.0":
  version "7.8.4"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.1.tgz?cache=0&sync_timestamp=1602799825010&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-class-properties%2Fdownload%2F%40babel%2Fplugin-syntax-class-properties-7.12.1.tgz#bcb297c5366e79bebadef509549cd93b04f19978"
  integrity sha1-vLKXxTZueb663vUJVJzZOwTxmXg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-decorators@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.12.1.tgz?cache=0&sync_timestamp=1602799825118&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-decorators%2Fdownload%2F%40babel%2Fplugin-syntax-decorators-7.12.1.tgz#81a8b535b284476c41be6de06853a8802b98c5dd"
  integrity sha1-gai1NbKER2xBvm3gaFOogCuYxd0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-dynamic-import@^7.8.0", "@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz#028964a9ba80dbc094c915c487ad7c4e7a66465a"
  integrity sha1-AolkqbqA28CUyRXEh618TnpmRlo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-json-strings@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.8.3":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.12.1.tgz#9d9d357cc818aa7ae7935917c1257f67677a0926"
  integrity sha1-nZ01fMgYqnrnk1kXwSV/Z2d6CSY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz?cache=0&sync_timestamp=1593525072473&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-logical-assignment-operators%2Fdownload%2F%40babel%2Fplugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.0":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.12.1.tgz?cache=0&sync_timestamp=1602800388781&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-syntax-top-level-await%2Fdownload%2F%40babel%2Fplugin-syntax-top-level-await-7.12.1.tgz#dd6c0b357ac1bb142d98537450a319625d13d2a0"
  integrity sha1-3WwLNXrBuxQtmFN0UKMZYl0T0qA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-arrow-functions@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.12.1.tgz?cache=0&sync_timestamp=1602800389200&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-arrow-functions%2Fdownload%2F%40babel%2Fplugin-transform-arrow-functions-7.12.1.tgz#8083ffc86ac8e777fbe24b5967c4b2521f3cb2b3"
  integrity sha1-gIP/yGrI53f74ktZZ8SyUh88srM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-async-to-generator@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.12.1.tgz?cache=0&sync_timestamp=1602802209455&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-async-to-generator%2Fdownload%2F%40babel%2Fplugin-transform-async-to-generator-7.12.1.tgz#3849a49cc2a22e9743cbd6b52926d30337229af1"
  integrity sha1-OEmknMKiLpdDy9a1KSbTAzcimvE=
  dependencies:
    "@babel/helper-module-imports" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-remap-async-to-generator" "^7.12.1"

"@babel/plugin-transform-block-scoped-functions@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.12.1.tgz?cache=0&sync_timestamp=1602800389453&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-block-scoped-functions%2Fdownload%2F%40babel%2Fplugin-transform-block-scoped-functions-7.12.1.tgz#f2a1a365bde2b7112e0a6ded9067fdd7c07905d9"
  integrity sha1-8qGjZb3itxEuCm3tkGf918B5Bdk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-block-scoping@^7.12.11":
  version "7.12.12"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.12.12.tgz?cache=0&sync_timestamp=1608730445937&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-block-scoping%2Fdownload%2F%40babel%2Fplugin-transform-block-scoping-7.12.12.tgz#d93a567a152c22aea3b1929bb118d1d0a175cdca"
  integrity sha1-2TpWehUsIq6jsZKbsRjR0KF1zco=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-classes@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.12.1.tgz#65e650fcaddd3d88ddce67c0f834a3d436a32db6"
  integrity sha1-ZeZQ/K3dPYjdzmfA+DSj1DajLbY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.10.4"
    "@babel/helper-define-map" "^7.10.4"
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-optimise-call-expression" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.12.1"
    "@babel/helper-split-export-declaration" "^7.10.4"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.12.1.tgz#d68cf6c9b7f838a8a4144badbe97541ea0904852"
  integrity sha1-1oz2ybf4OKikFEutvpdUHqCQSFI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-destructuring@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.12.1.tgz?cache=0&sync_timestamp=1602799826715&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-destructuring%2Fdownload%2F%40babel%2Fplugin-transform-destructuring-7.12.1.tgz#b9a570fe0d0a8d460116413cb4f97e8e08b2f847"
  integrity sha1-uaVw/g0KjUYBFkE8tPl+jgiy+Ec=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-dotall-regex@^7.12.1", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.12.1.tgz?cache=0&sync_timestamp=1602802215403&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-dotall-regex%2Fdownload%2F%40babel%2Fplugin-transform-dotall-regex-7.12.1.tgz#a1d16c14862817b6409c0a678d6f9373ca9cd975"
  integrity sha1-odFsFIYoF7ZAnApnjW+Tc8qc2XU=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-duplicate-keys@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.12.1.tgz?cache=0&sync_timestamp=1602800391058&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-duplicate-keys%2Fdownload%2F%40babel%2Fplugin-transform-duplicate-keys-7.12.1.tgz#745661baba295ac06e686822797a69fbaa2ca228"
  integrity sha1-dFZhuropWsBuaGgieXpp+6osoig=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-exponentiation-operator@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.12.1.tgz#b0f2ed356ba1be1428ecaf128ff8a24f02830ae0"
  integrity sha1-sPLtNWuhvhQo7K8Sj/iiTwKDCuA=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-for-of@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.12.1.tgz?cache=0&sync_timestamp=1602800389679&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-for-of%2Fdownload%2F%40babel%2Fplugin-transform-for-of-7.12.1.tgz#07640f28867ed16f9511c99c888291f560921cfa"
  integrity sha1-B2QPKIZ+0W+VEcmciIKR9WCSHPo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-function-name@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.12.1.tgz?cache=0&sync_timestamp=1602800389784&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-function-name%2Fdownload%2F%40babel%2Fplugin-transform-function-name-7.12.1.tgz#2ec76258c70fe08c6d7da154003a480620eba667"
  integrity sha1-LsdiWMcP4IxtfaFUADpIBiDrpmc=
  dependencies:
    "@babel/helper-function-name" "^7.10.4"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-literals@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.12.1.tgz?cache=0&sync_timestamp=1602800391264&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-literals%2Fdownload%2F%40babel%2Fplugin-transform-literals-7.12.1.tgz#d73b803a26b37017ddf9d3bb8f4dc58bfb806f57"
  integrity sha1-1zuAOiazcBfd+dO7j03Fi/uAb1c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.12.1.tgz?cache=0&sync_timestamp=1602799819477&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-member-expression-literals%2Fdownload%2F%40babel%2Fplugin-transform-member-expression-literals-7.12.1.tgz#496038602daf1514a64d43d8e17cbb2755e0c3ad"
  integrity sha1-SWA4YC2vFRSmTUPY4Xy7J1Xgw60=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-modules-amd@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.12.1.tgz#3154300b026185666eebb0c0ed7f8415fefcf6f9"
  integrity sha1-MVQwCwJhhWZu67DA7X+EFf789vk=
  dependencies:
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.12.1.tgz#fa403124542636c786cf9b460a0ffbb48a86e648"
  integrity sha1-+kAxJFQmNseGz5tGCg/7tIqG5kg=
  dependencies:
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-simple-access" "^7.12.1"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.12.1.tgz#663fea620d593c93f214a464cd399bf6dc683086"
  integrity sha1-Zj/qYg1ZPJPyFKRkzTmb9txoMIY=
  dependencies:
    "@babel/helper-hoist-variables" "^7.10.4"
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-validator-identifier" "^7.10.4"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.12.1.tgz?cache=0&sync_timestamp=1602802215813&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-modules-umd%2Fdownload%2F%40babel%2Fplugin-transform-modules-umd-7.12.1.tgz#eb5a218d6b1c68f3d6217b8fa2cc82fec6547902"
  integrity sha1-61ohjWscaPPWIXuPosyC/sZUeQI=
  dependencies:
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-named-capturing-groups-regex@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.12.1.tgz#b407f5c96be0d9f5f88467497fa82b30ac3e8753"
  integrity sha1-tAf1yWvg2fX4hGdJf6grMKw+h1M=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.1"

"@babel/plugin-transform-new-target@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.12.1.tgz#80073f02ee1bb2d365c3416490e085c95759dec0"
  integrity sha1-gAc/Au4bstNlw0FkkOCFyVdZ3sA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-object-super@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.12.1.tgz?cache=0&sync_timestamp=1602802211487&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-object-super%2Fdownload%2F%40babel%2Fplugin-transform-object-super-7.12.1.tgz#4ea08696b8d2e65841d0c7706482b048bed1066e"
  integrity sha1-TqCGlrjS5lhB0MdwZIKwSL7RBm4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-replace-supers" "^7.12.1"

"@babel/plugin-transform-parameters@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.12.1.tgz?cache=0&sync_timestamp=1602799819857&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-parameters%2Fdownload%2F%40babel%2Fplugin-transform-parameters-7.12.1.tgz#d2e963b038771650c922eff593799c96d853255d"
  integrity sha1-0uljsDh3FlDJIu/1k3mclthTJV0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-property-literals@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.12.1.tgz#41bc81200d730abb4456ab8b3fbd5537b59adecd"
  integrity sha1-QbyBIA1zCrtEVquLP71VN7Wa3s0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-regenerator@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.12.1.tgz?cache=0&sync_timestamp=1602800392288&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-regenerator%2Fdownload%2F%40babel%2Fplugin-transform-regenerator-7.12.1.tgz#5f0a28d842f6462281f06a964e88ba8d7ab49753"
  integrity sha1-Xwoo2EL2RiKB8GqWToi6jXq0l1M=
  dependencies:
    regenerator-transform "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.12.1.tgz?cache=0&sync_timestamp=1602801643725&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-reserved-words%2Fdownload%2F%40babel%2Fplugin-transform-reserved-words-7.12.1.tgz#6fdfc8cc7edcc42b36a7c12188c6787c873adcd8"
  integrity sha1-b9/IzH7cxCs2p8EhiMZ4fIc63Ng=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-runtime@^7.11.0":
  version "7.12.10"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.12.10.tgz?cache=0&sync_timestamp=1607569343439&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-runtime%2Fdownload%2F%40babel%2Fplugin-transform-runtime-7.12.10.tgz#af0fded4e846c4b37078e8e5d06deac6cd848562"
  integrity sha1-rw/e1OhGxLNweOjl0G3qxs2EhWI=
  dependencies:
    "@babel/helper-module-imports" "^7.12.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    semver "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.12.1.tgz?cache=0&sync_timestamp=1602801648312&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-shorthand-properties%2Fdownload%2F%40babel%2Fplugin-transform-shorthand-properties-7.12.1.tgz#0bf9cac5550fce0cfdf043420f661d645fdc75e3"
  integrity sha1-C/nKxVUPzgz98ENCD2YdZF/cdeM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-spread@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.12.1.tgz?cache=0&sync_timestamp=1602802209734&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-spread%2Fdownload%2F%40babel%2Fplugin-transform-spread-7.12.1.tgz#527f9f311be4ec7fdc2b79bb89f7bf884b3e1e1e"
  integrity sha1-Un+fMRvk7H/cK3m7ife/iEs+Hh4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.12.1"

"@babel/plugin-transform-sticky-regex@^7.12.7":
  version "7.12.7"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.12.7.tgz?cache=0&sync_timestamp=1605904545665&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-sticky-regex%2Fdownload%2F%40babel%2Fplugin-transform-sticky-regex-7.12.7.tgz#560224613ab23987453948ed21d0b0b193fa7fad"
  integrity sha1-VgIkYTqyOYdFOUjtIdCwsZP6f60=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-template-literals@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.12.1.tgz?cache=0&sync_timestamp=1602801648892&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-template-literals%2Fdownload%2F%40babel%2Fplugin-transform-template-literals-7.12.1.tgz#b43ece6ed9a79c0c71119f576d299ef09d942843"
  integrity sha1-tD7ObtmnnAxxEZ9XbSme8J2UKEM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-typeof-symbol@^7.12.10":
  version "7.12.10"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.12.10.tgz?cache=0&sync_timestamp=1607569594926&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-typeof-symbol%2Fdownload%2F%40babel%2Fplugin-transform-typeof-symbol-7.12.10.tgz#de01c4c8f96580bd00f183072b0d0ecdcf0dec4b"
  integrity sha1-3gHEyPllgL0A8YMHKw0Ozc8N7Es=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-unicode-escapes@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.12.1.tgz?cache=0&sync_timestamp=1602800393219&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-unicode-escapes%2Fdownload%2F%40babel%2Fplugin-transform-unicode-escapes-7.12.1.tgz#5232b9f81ccb07070b7c3c36c67a1b78f1845709"
  integrity sha1-UjK5+BzLBwcLfDw2xnobePGEVwk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-transform-unicode-regex@^7.12.1":
  version "7.12.1"
  resolved "https://registry.npm.taobao.org/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.12.1.tgz?cache=0&sync_timestamp=1602800384565&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fplugin-transform-unicode-regex%2Fdownload%2F%40babel%2Fplugin-transform-unicode-regex-7.12.1.tgz#cc9661f61390db5c65e3febaccefd5c6ac3faecb"
  integrity sha1-zJZh9hOQ21xl4/66zO/Vxqw/rss=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.12.1"
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/preset-env@^7.11.0":
  version "7.12.11"
  resolved "https://registry.npm.taobao.org/@babel/preset-env/download/@babel/preset-env-7.12.11.tgz#55d5f7981487365c93dbbc84507b1c7215e857f9"
  integrity sha1-VdX3mBSHNlyT27yEUHscchXoV/k=
  dependencies:
    "@babel/compat-data" "^7.12.7"
    "@babel/helper-compilation-targets" "^7.12.5"
    "@babel/helper-module-imports" "^7.12.5"
    "@babel/helper-plugin-utils" "^7.10.4"
    "@babel/helper-validator-option" "^7.12.11"
    "@babel/plugin-proposal-async-generator-functions" "^7.12.1"
    "@babel/plugin-proposal-class-properties" "^7.12.1"
    "@babel/plugin-proposal-dynamic-import" "^7.12.1"
    "@babel/plugin-proposal-export-namespace-from" "^7.12.1"
    "@babel/plugin-proposal-json-strings" "^7.12.1"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.12.1"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.12.1"
    "@babel/plugin-proposal-numeric-separator" "^7.12.7"
    "@babel/plugin-proposal-object-rest-spread" "^7.12.1"
    "@babel/plugin-proposal-optional-catch-binding" "^7.12.1"
    "@babel/plugin-proposal-optional-chaining" "^7.12.7"
    "@babel/plugin-proposal-private-methods" "^7.12.1"
    "@babel/plugin-proposal-unicode-property-regex" "^7.12.1"
    "@babel/plugin-syntax-async-generators" "^7.8.0"
    "@babel/plugin-syntax-class-properties" "^7.12.1"
    "@babel/plugin-syntax-dynamic-import" "^7.8.0"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.0"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.0"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.0"
    "@babel/plugin-syntax-top-level-await" "^7.12.1"
    "@babel/plugin-transform-arrow-functions" "^7.12.1"
    "@babel/plugin-transform-async-to-generator" "^7.12.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.12.1"
    "@babel/plugin-transform-block-scoping" "^7.12.11"
    "@babel/plugin-transform-classes" "^7.12.1"
    "@babel/plugin-transform-computed-properties" "^7.12.1"
    "@babel/plugin-transform-destructuring" "^7.12.1"
    "@babel/plugin-transform-dotall-regex" "^7.12.1"
    "@babel/plugin-transform-duplicate-keys" "^7.12.1"
    "@babel/plugin-transform-exponentiation-operator" "^7.12.1"
    "@babel/plugin-transform-for-of" "^7.12.1"
    "@babel/plugin-transform-function-name" "^7.12.1"
    "@babel/plugin-transform-literals" "^7.12.1"
    "@babel/plugin-transform-member-expression-literals" "^7.12.1"
    "@babel/plugin-transform-modules-amd" "^7.12.1"
    "@babel/plugin-transform-modules-commonjs" "^7.12.1"
    "@babel/plugin-transform-modules-systemjs" "^7.12.1"
    "@babel/plugin-transform-modules-umd" "^7.12.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.12.1"
    "@babel/plugin-transform-new-target" "^7.12.1"
    "@babel/plugin-transform-object-super" "^7.12.1"
    "@babel/plugin-transform-parameters" "^7.12.1"
    "@babel/plugin-transform-property-literals" "^7.12.1"
    "@babel/plugin-transform-regenerator" "^7.12.1"
    "@babel/plugin-transform-reserved-words" "^7.12.1"
    "@babel/plugin-transform-shorthand-properties" "^7.12.1"
    "@babel/plugin-transform-spread" "^7.12.1"
    "@babel/plugin-transform-sticky-regex" "^7.12.7"
    "@babel/plugin-transform-template-literals" "^7.12.1"
    "@babel/plugin-transform-typeof-symbol" "^7.12.10"
    "@babel/plugin-transform-unicode-escapes" "^7.12.1"
    "@babel/plugin-transform-unicode-regex" "^7.12.1"
    "@babel/preset-modules" "^0.1.3"
    "@babel/types" "^7.12.11"
    core-js-compat "^3.8.0"
    semver "^5.5.0"

"@babel/preset-modules@^0.1.3":
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/@babel/preset-modules/download/@babel/preset-modules-0.1.4.tgz?cache=0&sync_timestamp=1598549925898&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fpreset-modules%2Fdownload%2F%40babel%2Fpreset-modules-0.1.4.tgz#362f2b68c662842970fdb5e254ffc8fc1c2e415e"
  integrity sha1-Ni8raMZihClw/bXiVP/I/BwuQV4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/runtime@^7.0.0":
  version "7.14.0"
  resolved "https://registry.yarnpkg.com/@babel/runtime/-/runtime-7.14.0.tgz#46794bc20b612c5f75e62dd071e24dfd95f1cbe6"
  integrity sha512-JELkvo/DlpNdJ7dlyw/eY7E0suy5i5GQH+Vlxaq1nsNJ+H7f4Vtv3jMeCEgRhZZQFXTjldYfQgv2qmM6M1v5wA==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/runtime@^7.11.0", "@babel/runtime@^7.8.4":
  version "7.12.5"
  resolved "https://registry.npm.taobao.org/@babel/runtime/download/@babel/runtime-7.12.5.tgz#410e7e487441e1b360c29be715d870d9b985882e"
  integrity sha1-QQ5+SHRB4bNgwpvnFdhw2bmFiC4=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.0.0", "@babel/template@^7.10.4", "@babel/template@^7.12.7":
  version "7.12.7"
  resolved "https://registry.npm.taobao.org/@babel/template/download/@babel/template-7.12.7.tgz?cache=0&sync_timestamp=1605904794602&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftemplate%2Fdownload%2F%40babel%2Ftemplate-7.12.7.tgz#c817233696018e39fbb6c491d2fb684e05ed43bc"
  integrity sha1-yBcjNpYBjjn7tsSR0vtoTgXtQ7w=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/parser" "^7.12.7"
    "@babel/types" "^7.12.7"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.10.4", "@babel/traverse@^7.12.1", "@babel/traverse@^7.12.10", "@babel/traverse@^7.12.5", "@babel/traverse@^7.7.0":
  version "7.12.12"
  resolved "https://registry.npm.taobao.org/@babel/traverse/download/@babel/traverse-7.12.12.tgz?cache=0&sync_timestamp=1608730482757&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.12.12.tgz#d0cd87892704edd8da002d674bc811ce64743376"
  integrity sha1-0M2HiScE7djaAC1nS8gRzmR0M3Y=
  dependencies:
    "@babel/code-frame" "^7.12.11"
    "@babel/generator" "^7.12.11"
    "@babel/helper-function-name" "^7.12.11"
    "@babel/helper-split-export-declaration" "^7.12.11"
    "@babel/parser" "^7.12.11"
    "@babel/types" "^7.12.12"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.19"

"@babel/types@^7.0.0", "@babel/types@^7.10.4", "@babel/types@^7.10.5", "@babel/types@^7.12.1", "@babel/types@^7.12.10", "@babel/types@^7.12.11", "@babel/types@^7.12.12", "@babel/types@^7.12.5", "@babel/types@^7.12.7", "@babel/types@^7.4.4", "@babel/types@^7.7.0":
  version "7.12.12"
  resolved "https://registry.npm.taobao.org/@babel/types/download/@babel/types-7.12.12.tgz?cache=0&sync_timestamp=1608730459294&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.12.12.tgz#4608a6ec313abbd87afa55004d373ad04a96c299"
  integrity sha1-Rgim7DE6u9h6+lUATTc60EqWwpk=
  dependencies:
    "@babel/helper-validator-identifier" "^7.12.11"
    lodash "^4.17.19"
    to-fast-properties "^2.0.0"

"@eslint/eslintrc@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/@eslint/eslintrc/download/@eslint/eslintrc-0.3.0.tgz#d736d6963d7003b6514e6324bec9c602ac340318"
  integrity sha1-1zbWlj1wA7ZRTmMkvsnGAqw0Axg=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    lodash "^4.17.20"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "https://registry.npm.taobao.org/@hapi/address/download/@hapi/address-2.1.4.tgz?cache=0&sync_timestamp=1603524710662&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hapi%2Faddress%2Fdownload%2F%40hapi%2Faddress-2.1.4.tgz#5d67ed43f3fd41a69d4b9ff7b56e7c0d1d0a81e5"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz#0a7095adea067243ce3283e1b56b8a8f453b242a"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "https://registry.npm.taobao.org/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz?cache=0&sync_timestamp=1609086954944&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40hapi%2Fhoek%2Fdownload%2F%40hapi%2Fhoek-8.5.1.tgz#fde96064ca446dec8c55a8c2f130957b070c6e06"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@^15.0.1":
  version "15.1.1"
  resolved "https://registry.npm.taobao.org/@hapi/joi/download/@hapi/joi-15.1.1.tgz#c675b8a71296f02833f8d6d243b34c57b8ce19d7"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "https://registry.npm.taobao.org/@hapi/topo/download/@hapi/topo-3.1.6.tgz#68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@intervolga/optimize-cssnano-plugin@^1.0.5":
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz#be7c7846128b88f6a9b1d1261a0ad06eb5c0fdf8"
  integrity sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=
  dependencies:
    cssnano "^4.0.0"
    cssnano-preset-default "^4.0.0"
    postcss "^7.0.0"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz#524af240d1a360527b730475ecfa1344aa540dde"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.scandir@2.1.4":
  version "2.1.4"
  resolved "https://registry.npm.taobao.org/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.4.tgz?cache=0&sync_timestamp=1609074594471&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40nodelib%2Ffs.scandir%2Fdownload%2F%40nodelib%2Ffs.scandir-2.1.4.tgz#d4b3549a5db5de2683e0c1071ab4f140904bbf69"
  integrity sha1-1LNUml213iaD4MEHGrTxQJBLv2k=
  dependencies:
    "@nodelib/fs.stat" "2.0.4"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.4", "@nodelib/fs.stat@^2.0.2":
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.4.tgz?cache=0&sync_timestamp=1609074429033&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40nodelib%2Ffs.stat%2Fdownload%2F%40nodelib%2Ffs.stat-2.0.4.tgz#a3f2dd61bab43b8db8fa108a121cfffe4c676655"
  integrity sha1-o/LdYbq0O424+hCKEhz//kxnZlU=

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz?cache=0&sync_timestamp=1609074429033&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40nodelib%2Ffs.stat%2Fdownload%2F%40nodelib%2Ffs.stat-1.1.3.tgz#2b5a3ab3f918cca48a8c754c08168e3f03eba61b"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.6"
  resolved "https://registry.npm.taobao.org/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.6.tgz?cache=0&sync_timestamp=1609077069715&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40nodelib%2Ffs.walk%2Fdownload%2F%40nodelib%2Ffs.walk-1.2.6.tgz#cce9396b30aa5afe9e3756608f5831adcb53d063"
  integrity sha1-zOk5azCqWv6eN1Zgj1gxrctT0GM=
  dependencies:
    "@nodelib/fs.scandir" "2.1.4"
    fastq "^1.6.0"

"@npmcli/move-file@^1.0.1":
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/@npmcli/move-file/download/@npmcli/move-file-1.1.0.tgz?cache=0&sync_timestamp=1610642283881&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40npmcli%2Fmove-file%2Fdownload%2F%40npmcli%2Fmove-file-1.1.0.tgz#4ef8a53d727b9e43facf35404caf55ebf92cfec8"
  integrity sha1-TvilPXJ7nkP6zzVATK9V6/ks/sg=
  dependencies:
    mkdirp "^1.0.4"
    rimraf "^2.7.1"

"@simonwep/pickr@~1.7.0":
  version "1.7.4"
  resolved "https://registry.yarnpkg.com/@simonwep/pickr/-/pickr-1.7.4.tgz#b14fcd945890388b870cd6db4d6c78d531f25141"
  integrity sha512-fq7jgKJT21uWGC1mARBHvvd1JYlEf93o7SuVOB4Lr0x/2UPuNC9Oe9n/GzVeg4oVtqMDfh1wIEJpsdOJEZb+3g==
  dependencies:
    core-js "^3.6.5"
    nanopop "^2.1.0"

"@soda/friendly-errors-webpack-plugin@^1.7.1":
  version "1.8.0"
  resolved "https://registry.npm.taobao.org/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.0.tgz?cache=0&sync_timestamp=1607927398894&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40soda%2Ffriendly-errors-webpack-plugin%2Fdownload%2F%40soda%2Ffriendly-errors-webpack-plugin-1.8.0.tgz#84751d82a93019d5c92c0cf0e45ac59087cd2240"
  integrity sha1-hHUdgqkwGdXJLAzw5FrFkIfNIkA=
  dependencies:
    chalk "^2.4.2"
    error-stack-parser "^2.0.2"
    string-width "^2.0.0"
    strip-ansi "^5"

"@soda/get-current-script@^1.0.0":
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz#a53515db25d8038374381b73af20bb4f2e508d87"
  integrity sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc=

"@stylelint/postcss-css-in-js@^0.37.2":
  version "0.37.2"
  resolved "https://registry.npm.taobao.org/@stylelint/postcss-css-in-js/download/@stylelint/postcss-css-in-js-0.37.2.tgz#7e5a84ad181f4234a2480803422a47b8749af3d2"
  integrity sha1-flqErRgfQjSiSAgDQipHuHSa89I=
  dependencies:
    "@babel/core" ">=7.9.0"

"@stylelint/postcss-markdown@^0.36.2":
  version "0.36.2"
  resolved "https://registry.npm.taobao.org/@stylelint/postcss-markdown/download/@stylelint/postcss-markdown-0.36.2.tgz?cache=0&sync_timestamp=1605623634193&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40stylelint%2Fpostcss-markdown%2Fdownload%2F%40stylelint%2Fpostcss-markdown-0.36.2.tgz#0a540c4692f8dcdfc13c8e352c17e7bfee2bb391"
  integrity sha1-ClQMRpL43N/BPI41LBfnv+4rs5E=
  dependencies:
    remark "^13.0.0"
    unist-util-find-all-after "^3.0.2"

"@tinymce/tinymce-vue@^3.2.6":
  version "3.2.8"
  resolved "https://registry.npm.taobao.org/@tinymce/tinymce-vue/download/@tinymce/tinymce-vue-3.2.8.tgz#014571b52ec8fa83665a7fa887bf65140207de71"
  integrity sha1-AUVxtS7I+oNmWn+oh79lFAIH3nE=

"@types/anymatch@*":
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/@types/anymatch/download/@types/anymatch-1.3.1.tgz#336badc1beecb9dacc38bea2cf32adf627a8421a"
  integrity sha1-M2utwb7sudrMOL6izzKt9ieoQho=

"@types/body-parser@*":
  version "1.19.0"
  resolved "https://registry.npm.taobao.org/@types/body-parser/download/@types/body-parser-1.19.0.tgz?cache=0&sync_timestamp=1605052849660&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fbody-parser%2Fdownload%2F%40types%2Fbody-parser-1.19.0.tgz#0685b3c47eb3006ffed117cdd55164b61f80538f"
  integrity sha1-BoWzxH6zAG/+0RfN1VFkth+AU48=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/connect-history-api-fallback@*":
  version "1.3.3"
  resolved "https://registry.npm.taobao.org/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.3.3.tgz?cache=0&sync_timestamp=1605052862677&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fconnect-history-api-fallback%2Fdownload%2F%40types%2Fconnect-history-api-fallback-1.3.3.tgz#4772b79b8b53185f0f4c9deab09236baf76ee3b4"
  integrity sha1-R3K3m4tTGF8PTJ3qsJI2uvdu47Q=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.34"
  resolved "https://registry.npm.taobao.org/@types/connect/download/@types/connect-3.4.34.tgz#170a40223a6d666006d93ca128af2beb1d9b1901"
  integrity sha1-FwpAIjptZmAG2TyhKK8r6x2bGQE=
  dependencies:
    "@types/node" "*"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  version "4.17.18"
  resolved "https://registry.npm.taobao.org/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.18.tgz?cache=0&sync_timestamp=1610403992683&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fexpress-serve-static-core%2Fdownload%2F%40types%2Fexpress-serve-static-core-4.17.18.tgz#8371e260f40e0e1ca0c116a9afcd9426fa094c40"
  integrity sha1-g3HiYPQODhygwRapr82UJvoJTEA=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  version "4.17.11"
  resolved "https://registry.npm.taobao.org/@types/express/download/@types/express-4.17.11.tgz?cache=0&sync_timestamp=1610486227759&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fexpress%2Fdownload%2F%40types%2Fexpress-4.17.11.tgz#debe3caa6f8e5fcda96b47bd54e2f40c4ee59545"
  integrity sha1-3r48qm+OX82pa0e9VOL0DE7llUU=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/glob@^7.1.1":
  version "7.1.3"
  resolved "https://registry.npm.taobao.org/@types/glob/download/@types/glob-7.1.3.tgz?cache=0&sync_timestamp=1605053250918&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fglob%2Fdownload%2F%40types%2Fglob-7.1.3.tgz#e6ba80f36b7daad2c685acd9266382e68985c183"
  integrity sha1-5rqA82t9qtLGhazZJmOC5omFwYM=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/html-minifier-terser@^5.0.0":
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/@types/html-minifier-terser/download/@types/html-minifier-terser-5.1.1.tgz?cache=0&sync_timestamp=1605053836232&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fhtml-minifier-terser%2Fdownload%2F%40types%2Fhtml-minifier-terser-5.1.1.tgz#3c9ee980f1a10d6021ae6632ca3e79ca2ec4fb50"
  integrity sha1-PJ7pgPGhDWAhrmYyyj55yi7E+1A=

"@types/http-proxy-middleware@*":
  version "0.19.3"
  resolved "https://registry.npm.taobao.org/@types/http-proxy-middleware/download/@types/http-proxy-middleware-0.19.3.tgz#b2eb96fbc0f9ac7250b5d9c4c53aade049497d03"
  integrity sha1-suuW+8D5rHJQtdnExTqt4ElJfQM=
  dependencies:
    "@types/connect" "*"
    "@types/http-proxy" "*"
    "@types/node" "*"

"@types/http-proxy@*":
  version "1.17.5"
  resolved "https://registry.npm.taobao.org/@types/http-proxy/download/@types/http-proxy-1.17.5.tgz?cache=0&sync_timestamp=1610728394965&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fhttp-proxy%2Fdownload%2F%40types%2Fhttp-proxy-1.17.5.tgz#c203c5e6e9dc6820d27a40eb1e511c70a220423d"
  integrity sha1-wgPF5uncaCDSekDrHlEccKIgQj0=
  dependencies:
    "@types/node" "*"

"@types/json-schema@^7.0.5", "@types/json-schema@^7.0.6":
  version "7.0.7"
  resolved "https://registry.npm.taobao.org/@types/json-schema/download/@types/json-schema-7.0.7.tgz?cache=0&sync_timestamp=1611096103663&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fjson-schema%2Fdownload%2F%40types%2Fjson-schema-7.0.7.tgz#98a993516c859eb0d5c4c8f098317a9ea68db9ad"
  integrity sha1-mKmTUWyFnrDVxMjwmDF6nqaNua0=

"@types/mdast@^3.0.0":
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/@types/mdast/download/@types/mdast-3.0.3.tgz#2d7d671b1cd1ea3deb306ea75036c2a0407d2deb"
  integrity sha1-LX1nGxzR6j3rMG6nUDbCoEB9Les=
  dependencies:
    "@types/unist" "*"

"@types/mime@^1":
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/@types/mime/download/@types/mime-1.3.2.tgz?cache=0&sync_timestamp=1610980493651&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fmime%2Fdownload%2F%40types%2Fmime-1.3.2.tgz#93e25bf9ee75fe0fd80b594bc4feb0e862111b5a"
  integrity sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=

"@types/minimatch@*":
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/@types/minimatch/download/@types/minimatch-3.0.3.tgz#3dca0e3f33b200fc7d1139c0cd96c1268cadfd9d"
  integrity sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=

"@types/minimist@^1.2.0":
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/@types/minimist/download/@types/minimist-1.2.1.tgz#283f669ff76d7b8260df8ab7a4262cc83d988256"
  integrity sha1-KD9mn/dte4Jg34q3pCYsyD2YglY=

"@types/node@*":
  version "14.14.22"
  resolved "https://registry.npm.taobao.org/@types/node/download/@types/node-14.14.22.tgz#0d29f382472c4ccf3bd96ff0ce47daf5b7b84b18"
  integrity sha1-DSnzgkcsTM872W/wzkfa9be4Sxg=

"@types/normalize-package-data@^2.4.0":
  version "2.4.0"
  resolved "https://registry.npm.taobao.org/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz?cache=0&sync_timestamp=1605054866275&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fnormalize-package-data%2Fdownload%2F%40types%2Fnormalize-package-data-2.4.0.tgz#e486d0d97396d79beedd0a6e33f4534ff6b4973e"
  integrity sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/@types/parse-json/download/@types/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1605054896578&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fparse-json%2Fdownload%2F%40types%2Fparse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/q@^1.5.1":
  version "1.5.4"
  resolved "https://registry.npm.taobao.org/@types/q/download/@types/q-1.5.4.tgz?cache=0&sync_timestamp=1605055234302&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fq%2Fdownload%2F%40types%2Fq-1.5.4.tgz#15925414e0ad2cd765bfef58842f7e26a7accb24"
  integrity sha1-FZJUFOCtLNdlv+9YhC9+JqesyyQ=

"@types/qs@*":
  version "6.9.5"
  resolved "https://registry.npm.taobao.org/@types/qs/download/@types/qs-6.9.5.tgz?cache=0&sync_timestamp=1605055213283&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fqs%2Fdownload%2F%40types%2Fqs-6.9.5.tgz#434711bdd49eb5ee69d90c1d67c354a9a8ecb18b"
  integrity sha1-Q0cRvdSete5p2QwdZ8NUqajssYs=

"@types/range-parser@*":
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/@types/range-parser/download/@types/range-parser-1.2.3.tgz?cache=0&sync_timestamp=1605055212833&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Frange-parser%2Fdownload%2F%40types%2Frange-parser-1.2.3.tgz#7ee330ba7caafb98090bece86a5ee44115904c2c"
  integrity sha1-fuMwunyq+5gJC+zoal7kQRWQTCw=

"@types/serve-static@*":
  version "1.13.9"
  resolved "https://registry.npm.taobao.org/@types/serve-static/download/@types/serve-static-1.13.9.tgz?cache=0&sync_timestamp=1610978837110&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fserve-static%2Fdownload%2F%40types%2Fserve-static-1.13.9.tgz#aacf28a85a05ee29a11fb7c3ead935ac56f33e4e"
  integrity sha1-qs8oqFoF7imhH7fD6tk1rFbzPk4=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/source-list-map@*":
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/@types/source-list-map/download/@types/source-list-map-0.1.2.tgz#0078836063ffaf17412349bba364087e0ac02ec9"
  integrity sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk=

"@types/tapable@*", "@types/tapable@^1.0.5":
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/@types/tapable/download/@types/tapable-1.0.6.tgz#a9ca4b70a18b270ccb2bc0aaafefd1d486b7ea74"
  integrity sha1-qcpLcKGLJwzLK8Cqr+/R1Ia36nQ=

"@types/uglify-js@*":
  version "3.11.1"
  resolved "https://registry.npm.taobao.org/@types/uglify-js/download/@types/uglify-js-3.11.1.tgz#97ff30e61a0aa6876c270b5f538737e2d6ab8ceb"
  integrity sha1-l/8w5hoKpodsJwtfU4c34tarjOs=
  dependencies:
    source-map "^0.6.1"

"@types/unist@*", "@types/unist@^2.0.0", "@types/unist@^2.0.2":
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/@types/unist/download/@types/unist-2.0.3.tgz#9c088679876f374eb5983f150d4787aa6fb32d7e"
  integrity sha1-nAiGeYdvN061mD8VDUeHqm+zLX4=

"@types/webpack-dev-server@^3.11.0":
  version "3.11.1"
  resolved "https://registry.npm.taobao.org/@types/webpack-dev-server/download/@types/webpack-dev-server-3.11.1.tgz#f8f4dac1da226d530bd15a1d5dc34b23ba766ccb"
  integrity sha1-+PTawdoibVML0VodXcNLI7p2bMs=
  dependencies:
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/http-proxy-middleware" "*"
    "@types/serve-static" "*"
    "@types/webpack" "*"

"@types/webpack-sources@*":
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/@types/webpack-sources/download/@types/webpack-sources-2.1.0.tgz#8882b0bd62d1e0ce62f183d0d01b72e6e82e8c10"
  integrity sha1-iIKwvWLR4M5i8YPQ0Bty5ugujBA=
  dependencies:
    "@types/node" "*"
    "@types/source-list-map" "*"
    source-map "^0.7.3"

"@types/webpack@*", "@types/webpack@^4.0.0", "@types/webpack@^4.41.8":
  version "4.41.26"
  resolved "https://registry.npm.taobao.org/@types/webpack/download/@types/webpack-4.41.26.tgz?cache=0&sync_timestamp=1610402050021&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fwebpack%2Fdownload%2F%40types%2Fwebpack-4.41.26.tgz#27a30d7d531e16489f9c7607c747be6bc1a459ef"
  integrity sha1-J6MNfVMeFkifnHYHx0e+a8GkWe8=
  dependencies:
    "@types/anymatch" "*"
    "@types/node" "*"
    "@types/tapable" "*"
    "@types/uglify-js" "*"
    "@types/webpack-sources" "*"
    source-map "^0.6.0"

"@vue/babel-helper-vue-jsx-merge-props@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.2.1.tgz#31624a7a505fb14da1d58023725a4c5f270e6a81"
  integrity sha1-MWJKelBfsU2h1YAjclpMXycOaoE=

"@vue/babel-helper-vue-transform-on@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.0.2.tgz?cache=0&sync_timestamp=1610812601037&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-helper-vue-transform-on%2Fdownload%2F%40vue%2Fbabel-helper-vue-transform-on-1.0.2.tgz#9b9c691cd06fc855221a2475c3cc831d774bc7dc"
  integrity sha1-m5xpHNBvyFUiGiR1w8yDHXdLx9w=

"@vue/babel-plugin-jsx@^1.0.0-0":
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.0.2.tgz?cache=0&sync_timestamp=1610812598012&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-plugin-jsx%2Fdownload%2F%40vue%2Fbabel-plugin-jsx-1.0.2.tgz#6bfd8e39c48e53391a56705649f81a35fe20b6a1"
  integrity sha1-a/2OOcSOUzkaVnBWSfgaNf4gtqE=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    camelcase "^6.0.0"
    html-tags "^3.1.0"
    svg-tags "^1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.2.1.tgz?cache=0&sync_timestamp=1602851234609&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-plugin-transform-vue-jsx%2Fdownload%2F%40vue%2Fbabel-plugin-transform-vue-jsx-1.2.1.tgz#646046c652c2f0242727f34519d917b064041ed7"
  integrity sha1-ZGBGxlLC8CQnJ/NFGdkXsGQEHtc=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^4.5.11":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/babel-preset-app/download/@vue/babel-preset-app-4.5.11.tgz?cache=0&sync_timestamp=1611327514154&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-preset-app%2Fdownload%2F%40vue%2Fbabel-preset-app-4.5.11.tgz#f677bc10472e418f71f61f10dde5a79976a215b8"
  integrity sha1-9ne8EEcuQY9x9h8Q3eWnmXaiFbg=
  dependencies:
    "@babel/core" "^7.11.0"
    "@babel/helper-compilation-targets" "^7.9.6"
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/plugin-proposal-class-properties" "^7.8.3"
    "@babel/plugin-proposal-decorators" "^7.8.3"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.8.3"
    "@babel/plugin-transform-runtime" "^7.11.0"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.0"
    "@vue/babel-plugin-jsx" "^1.0.0-0"
    "@vue/babel-preset-jsx" "^1.1.2"
    babel-plugin-dynamic-import-node "^2.3.3"
    core-js "^3.6.5"
    core-js-compat "^3.6.5"
    semver "^6.1.0"

"@vue/babel-preset-jsx@^1.1.2":
  version "1.2.4"
  resolved "https://registry.npm.taobao.org/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.2.4.tgz?cache=0&sync_timestamp=1603806993853&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-preset-jsx%2Fdownload%2F%40vue%2Fbabel-preset-jsx-1.2.4.tgz#92fea79db6f13b01e80d3a0099e2924bdcbe4e87"
  integrity sha1-kv6nnbbxOwHoDToAmeKSS9y+Toc=
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "@vue/babel-sugar-composition-api-inject-h" "^1.2.1"
    "@vue/babel-sugar-composition-api-render-instance" "^1.2.4"
    "@vue/babel-sugar-functional-vue" "^1.2.2"
    "@vue/babel-sugar-inject-h" "^1.2.2"
    "@vue/babel-sugar-v-model" "^1.2.3"
    "@vue/babel-sugar-v-on" "^1.2.3"

"@vue/babel-sugar-composition-api-inject-h@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/@vue/babel-sugar-composition-api-inject-h/download/@vue/babel-sugar-composition-api-inject-h-1.2.1.tgz?cache=0&sync_timestamp=1602851126644&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-composition-api-inject-h%2Fdownload%2F%40vue%2Fbabel-sugar-composition-api-inject-h-1.2.1.tgz#05d6e0c432710e37582b2be9a6049b689b6f03eb"
  integrity sha1-BdbgxDJxDjdYKyvppgSbaJtvA+s=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.2.4":
  version "1.2.4"
  resolved "https://registry.npm.taobao.org/@vue/babel-sugar-composition-api-render-instance/download/@vue/babel-sugar-composition-api-render-instance-1.2.4.tgz?cache=0&sync_timestamp=1603806995958&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-composition-api-render-instance%2Fdownload%2F%40vue%2Fbabel-sugar-composition-api-render-instance-1.2.4.tgz#e4cbc6997c344fac271785ad7a29325c51d68d19"
  integrity sha1-5MvGmXw0T6wnF4WteikyXFHWjRk=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.2.2":
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.2.2.tgz?cache=0&sync_timestamp=1602929516892&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-functional-vue%2Fdownload%2F%40vue%2Fbabel-sugar-functional-vue-1.2.2.tgz#267a9ac8d787c96edbf03ce3f392c49da9bd2658"
  integrity sha1-JnqayNeHyW7b8Dzj85LEnam9Jlg=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.2.2":
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.2.2.tgz?cache=0&sync_timestamp=1602929885089&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-inject-h%2Fdownload%2F%40vue%2Fbabel-sugar-inject-h-1.2.2.tgz#d738d3c893367ec8491dcbb669b000919293e3aa"
  integrity sha1-1zjTyJM2fshJHcu2abAAkZKT46o=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.2.3":
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.2.3.tgz?cache=0&sync_timestamp=1603184347642&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-v-model%2Fdownload%2F%40vue%2Fbabel-sugar-v-model-1.2.3.tgz#fa1f29ba51ebf0aa1a6c35fa66d539bc459a18f2"
  integrity sha1-+h8pulHr8KoabDX6ZtU5vEWaGPI=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.2.3":
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.2.3.tgz?cache=0&sync_timestamp=1603181896860&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fbabel-sugar-v-on%2Fdownload%2F%40vue%2Fbabel-sugar-v-on-1.2.3.tgz#342367178586a69f392f04bfba32021d02913ada"
  integrity sha1-NCNnF4WGpp85LwS/ujICHQKROto=
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    camelcase "^5.0.0"

"@vue/cli-overlay@^4.5.11":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/cli-overlay/download/@vue/cli-overlay-4.5.11.tgz?cache=0&sync_timestamp=1611327512257&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-overlay%2Fdownload%2F%40vue%2Fcli-overlay-4.5.11.tgz#ea99493131182285f7ac2762290354d6e5b188e8"
  integrity sha1-6plJMTEYIoX3rCdiKQNU1uWxiOg=

"@vue/cli-plugin-babel@~4.5.8":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-4.5.11.tgz?cache=0&sync_timestamp=1611327404054&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-plugin-babel%2Fdownload%2F%40vue%2Fcli-plugin-babel-4.5.11.tgz#7c1db4ca2f911e2156e7d1cf774fe2ad0f7428eb"
  integrity sha1-fB20yi+RHiFW59HPd0/irQ90KOs=
  dependencies:
    "@babel/core" "^7.11.0"
    "@vue/babel-preset-app" "^4.5.11"
    "@vue/cli-shared-utils" "^4.5.11"
    babel-loader "^8.1.0"
    cache-loader "^4.1.0"
    thread-loader "^2.1.3"
    webpack "^4.0.0"

"@vue/cli-plugin-eslint@~4.5.8":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-4.5.11.tgz?cache=0&sync_timestamp=1611327516346&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-plugin-eslint%2Fdownload%2F%40vue%2Fcli-plugin-eslint-4.5.11.tgz#660eb7f8077a022c93bfad7b1cfb81e70a8be142"
  integrity sha1-Zg63+Ad6AiyTv617HPuB5wqL4UI=
  dependencies:
    "@vue/cli-shared-utils" "^4.5.11"
    eslint-loader "^2.2.1"
    globby "^9.2.0"
    inquirer "^7.1.0"
    webpack "^4.0.0"
    yorkie "^2.0.0"

"@vue/cli-plugin-router@^4.5.11":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/cli-plugin-router/download/@vue/cli-plugin-router-4.5.11.tgz?cache=0&sync_timestamp=1611327402318&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-plugin-router%2Fdownload%2F%40vue%2Fcli-plugin-router-4.5.11.tgz#3b6df738c5a1a5f50376822bf661d9a3b0c3fa62"
  integrity sha1-O233OMWhpfUDdoIr9mHZo7DD+mI=
  dependencies:
    "@vue/cli-shared-utils" "^4.5.11"

"@vue/cli-plugin-vuex@^4.5.11":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-4.5.11.tgz?cache=0&sync_timestamp=1611327808313&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-plugin-vuex%2Fdownload%2F%40vue%2Fcli-plugin-vuex-4.5.11.tgz#f6f619bcfb66c86cc45340d73152844635e548bd"
  integrity sha1-9vYZvPtmyGzEU0DXMVKERjXlSL0=

"@vue/cli-service@~4.5.8":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/cli-service/download/@vue/cli-service-4.5.11.tgz?cache=0&sync_timestamp=1611327414736&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-service%2Fdownload%2F%40vue%2Fcli-service-4.5.11.tgz#b157e2eee2351889cbbd4ccb4a4a9d8575409175"
  integrity sha1-sVfi7uI1GInLvUzLSkqdhXVAkXU=
  dependencies:
    "@intervolga/optimize-cssnano-plugin" "^1.0.5"
    "@soda/friendly-errors-webpack-plugin" "^1.7.1"
    "@soda/get-current-script" "^1.0.0"
    "@types/minimist" "^1.2.0"
    "@types/webpack" "^4.0.0"
    "@types/webpack-dev-server" "^3.11.0"
    "@vue/cli-overlay" "^4.5.11"
    "@vue/cli-plugin-router" "^4.5.11"
    "@vue/cli-plugin-vuex" "^4.5.11"
    "@vue/cli-shared-utils" "^4.5.11"
    "@vue/component-compiler-utils" "^3.1.2"
    "@vue/preload-webpack-plugin" "^1.1.0"
    "@vue/web-component-wrapper" "^1.2.0"
    acorn "^7.4.0"
    acorn-walk "^7.1.1"
    address "^1.1.2"
    autoprefixer "^9.8.6"
    browserslist "^4.12.0"
    cache-loader "^4.1.0"
    case-sensitive-paths-webpack-plugin "^2.3.0"
    cli-highlight "^2.1.4"
    clipboardy "^2.3.0"
    cliui "^6.0.0"
    copy-webpack-plugin "^5.1.1"
    css-loader "^3.5.3"
    cssnano "^4.1.10"
    debug "^4.1.1"
    default-gateway "^5.0.5"
    dotenv "^8.2.0"
    dotenv-expand "^5.1.0"
    file-loader "^4.2.0"
    fs-extra "^7.0.1"
    globby "^9.2.0"
    hash-sum "^2.0.0"
    html-webpack-plugin "^3.2.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.1"
    lodash.mapvalues "^4.6.0"
    lodash.transform "^4.6.0"
    mini-css-extract-plugin "^0.9.0"
    minimist "^1.2.5"
    pnp-webpack-plugin "^1.6.4"
    portfinder "^1.0.26"
    postcss-loader "^3.0.0"
    ssri "^7.1.0"
    terser-webpack-plugin "^2.3.6"
    thread-loader "^2.1.3"
    url-loader "^2.2.0"
    vue-loader "^15.9.2"
    vue-style-loader "^4.1.2"
    webpack "^4.0.0"
    webpack-bundle-analyzer "^3.8.0"
    webpack-chain "^6.4.0"
    webpack-dev-server "^3.11.0"
    webpack-merge "^4.2.2"
  optionalDependencies:
    vue-loader-v16 "npm:vue-loader@^16.1.0"

"@vue/cli-shared-utils@^4.5.11":
  version "4.5.11"
  resolved "https://registry.npm.taobao.org/@vue/cli-shared-utils/download/@vue/cli-shared-utils-4.5.11.tgz?cache=0&sync_timestamp=1611327387324&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40vue%2Fcli-shared-utils%2Fdownload%2F%40vue%2Fcli-shared-utils-4.5.11.tgz#fff71673ee9128f998c691515b9d327071b4f41e"
  integrity sha1-//cWc+6RKPmYxpFRW50ycHG09B4=
  dependencies:
    "@hapi/joi" "^15.0.1"
    chalk "^2.4.2"
    execa "^1.0.0"
    launch-editor "^2.2.1"
    lru-cache "^5.1.1"
    node-ipc "^9.1.1"
    open "^6.3.0"
    ora "^3.4.0"
    read-pkg "^5.1.1"
    request "^2.88.2"
    semver "^6.1.0"
    strip-ansi "^6.0.0"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.1.2":
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.2.0.tgz#8f85182ceed28e9b3c75313de669f83166d11e5d"
  integrity sha1-j4UYLO7Sjps8dTE95mn4MWbRHl0=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.14"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2"

"@vue/preload-webpack-plugin@^1.1.0":
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.2.tgz#ceb924b4ecb3b9c43871c7a429a02f8423e621ab"
  integrity sha1-zrkktOyzucQ4ccekKaAvhCPmIas=

"@vue/web-component-wrapper@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.2.0.tgz#bb0e46f1585a7e289b4ee6067dcc5a6ae62f1dd1"
  integrity sha1-uw5G8VhafiibTuYGfcxaauYvHdE=

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz?cache=0&sync_timestamp=1610041386122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.9.0.tgz#bd850604b4042459a5a41cd7d338cbed695ed964"
  integrity sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz?cache=0&sync_timestamp=1610041384204&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Ffloating-point-hex-parser%2Fdownload%2F%40webassemblyjs%2Ffloating-point-hex-parser-1.9.0.tgz#3c3d3b271bddfc84deb00f71344438311d52ffb4"
  integrity sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz?cache=0&sync_timestamp=1610041385672&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-api-error%2Fdownload%2F%40webassemblyjs%2Fhelper-api-error-1.9.0.tgz#203f676e333b96c9da2eeab3ccef33c45928b6a2"
  integrity sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz?cache=0&sync_timestamp=1610041385156&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.9.0.tgz#a1442d269c5feb23fcbc9ef759dac3547f29de00"
  integrity sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz?cache=0&sync_timestamp=1610045502739&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-code-frame%2Fdownload%2F%40webassemblyjs%2Fhelper-code-frame-1.9.0.tgz#647f8892cd2043a82ac0c8c5e75c36f1d9159f27"
  integrity sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz?cache=0&sync_timestamp=1610045497114&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-fsm%2Fdownload%2F%40webassemblyjs%2Fhelper-fsm-1.9.0.tgz#c05256b71244214671f4b08ec108ad63b70eddb8"
  integrity sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz#25d8884b76839871a08a6c6f806c3979ef712f07"
  integrity sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz?cache=0&sync_timestamp=1610041385277&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-bytecode%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-bytecode-1.9.0.tgz#4fed8beac9b8c14f8c58b70d124d549dd1fe5790"
  integrity sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz?cache=0&sync_timestamp=1610041387398&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.9.0.tgz#5a4138d5a6292ba18b04c5ae49717e4167965346"
  integrity sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz?cache=0&sync_timestamp=1610041385781&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fieee754%2Fdownload%2F%40webassemblyjs%2Fieee754-1.9.0.tgz#15c7a0fbaae83fb26143bbacf6d6df1702ad39e4"
  integrity sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz?cache=0&sync_timestamp=1610041385570&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fleb128%2Fdownload%2F%40webassemblyjs%2Fleb128-1.9.0.tgz#f19ca0b76a6dc55623a09cffa769e838fa1e1c95"
  integrity sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz?cache=0&sync_timestamp=1610045498791&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Futf8%2Fdownload%2F%40webassemblyjs%2Futf8-1.9.0.tgz#04d33b636f78e6a6813227e82402f7637b6229ab"
  integrity sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz?cache=0&sync_timestamp=1610041387713&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-edit%2Fdownload%2F%40webassemblyjs%2Fwasm-edit-1.9.0.tgz#3fe6d79d3f0f922183aa86002c42dd256cfee9cf"
  integrity sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz?cache=0&sync_timestamp=1610041387011&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.9.0.tgz#50bc70ec68ded8e2763b01a1418bf43491a7a49c"
  integrity sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz?cache=0&sync_timestamp=1610041387249&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-opt%2Fdownload%2F%40webassemblyjs%2Fwasm-opt-1.9.0.tgz#2211181e5b31326443cc8112eb9f0b9028721a61"
  integrity sha1-IhEYHlsxMmRDzIES658LkChyGmE=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz?cache=0&sync_timestamp=1610045501288&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.9.0.tgz#9d48e44826df4a6598294aa6c87469d642fff65e"
  integrity sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz?cache=0&sync_timestamp=1610041387557&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-parser%2Fdownload%2F%40webassemblyjs%2Fwast-parser-1.9.0.tgz#3031115d79ac5bd261556cecc3fa90a3ef451914"
  integrity sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz?cache=0&sync_timestamp=1610041386456&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40webassemblyjs%2Fwast-printer%2Fdownload%2F%40webassemblyjs%2Fwast-printer-1.9.0.tgz#4935d54c85fef637b00ce9f52377451d00d47899"
  integrity sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@winner-fed/vue-cli-plugin-stylelint@^1.0.4":
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/@winner-fed/vue-cli-plugin-stylelint/download/@winner-fed/vue-cli-plugin-stylelint-1.0.4.tgz#948836bdcb174f05a79ef0a5c45922ec9e9d1f6f"
  integrity sha1-lIg2vcsXTwWnnvClxFki7J6dH28=
  dependencies:
    chalk "^4.1.0"
    inquirer "^7.3.2"
    javascript-stringify "^2.0.1"
    micromatch "^4.0.2"
    stylelint "^13.6.1"
    stylelint-codeframe-formatter "^1.0.4"
    stylelint-webpack-plugin "^2.1.0"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npm.taobao.org/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-jsx@^5.2.0, acorn-jsx@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-5.3.1.tgz?cache=0&sync_timestamp=1599546317194&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn-jsx%2Fdownload%2Facorn-jsx-5.3.1.tgz#fc8661e11b7ac1539c47dbfea2e72b3af34d267b"
  integrity sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://registry.npm.taobao.org/acorn-walk/download/acorn-walk-7.2.0.tgz#0de889a601203909b0fbe07b8938dc21d2e967bc"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^6.4.1:
  version "6.4.2"
  resolved "https://registry.npm.taobao.org/acorn/download/acorn-6.4.2.tgz?cache=0&sync_timestamp=1611561275462&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-6.4.2.tgz#35866fd710528e92de10cf06016498e47e39e1e6"
  integrity sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=

acorn@^7.1.1, acorn@^7.4.0:
  version "7.4.1"
  resolved "https://registry.npm.taobao.org/acorn/download/acorn-7.4.1.tgz?cache=0&sync_timestamp=1611561275462&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Facorn%2Fdownload%2Facorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

add-dom-event-listener@^1.0.2:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/add-dom-event-listener/-/add-dom-event-listener-1.1.0.tgz#6a92db3a0dd0abc254e095c0f1dc14acbbaae310"
  integrity sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw==
  dependencies:
    object-assign "4.x"

address@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/address/download/address-1.1.2.tgz?cache=0&sync_timestamp=1593529661616&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faddress%2Fdownload%2Faddress-1.1.2.tgz#bf1116c9c758c51b7a933d296b72c221ed9428b6"
  integrity sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY=

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/aggregate-error/download/aggregate-error-3.1.0.tgz?cache=0&sync_timestamp=1609930663052&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faggregate-error%2Fdownload%2Faggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/ajv-errors/download/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^6.1.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.3, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.npm.taobao.org/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1609583976611&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^7.0.2:
  version "7.0.3"
  resolved "https://registry.npm.taobao.org/ajv/download/ajv-7.0.3.tgz?cache=0&sync_timestamp=1609583976611&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-7.0.3.tgz#13ae747eff125cafb230ac504b2406cf371eece2"
  integrity sha1-E650fv8SXK+yMKxQSyQGzzce7OI=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

alphanum-sort@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz#97a1119649b211ad33691d9f9f486a8ec9fbe0a3"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-3.2.4.tgz?cache=0&sync_timestamp=1593529711167&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-colors%2Fdownload%2Fansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-colors@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-4.1.1.tgz?cache=0&sync_timestamp=1593529711167&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-colors%2Fdownload%2Fansi-colors-4.1.1.tgz#cbb9ae256bf750af1eab344f229aa27fe94ba348"
  integrity sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=

ansi-escapes@^4.2.1:
  version "4.3.1"
  resolved "https://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-4.3.1.tgz#a5c47cc43181f1f38ffd7076837700d395522a61"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-html@0.0.7:
  version "0.0.7"
  resolved "https://registry.npm.taobao.org/ansi-html/download/ansi-html-0.0.7.tgz#813584021962a9e9e6fd039f940d12f56ca7859e"
  integrity sha1-gTWEAhliqenm/QOflA0S9WynhZ4=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ant-design-vue@^1.7.2:
  version "1.7.5"
  resolved "https://registry.yarnpkg.com/ant-design-vue/-/ant-design-vue-1.7.5.tgz#e7ed04cb358adc56be02c7453d2db026d2c405e3"
  integrity sha512-QVrirCz6eetzui+Dv/ujMqUwIr8e/2H+pKr4VC3mpc+cHkw6L9cBRJK7DhfO7GTyK4EWKkEH3lfLWlD/XjUoQQ==
  dependencies:
    "@ant-design/icons" "^2.1.1"
    "@ant-design/icons-vue" "^2.0.0"
    "@simonwep/pickr" "~1.7.0"
    add-dom-event-listener "^1.0.2"
    array-tree-filter "^2.1.0"
    async-validator "^3.0.3"
    babel-helper-vue-jsx-merge-props "^2.0.3"
    babel-runtime "6.x"
    classnames "^2.2.5"
    component-classes "^1.2.6"
    dom-align "^1.10.4"
    dom-closest "^0.2.0"
    dom-scroll-into-view "^2.0.0"
    enquire.js "^2.1.6"
    intersperse "^1.0.0"
    is-mobile "^2.2.1"
    is-negative-zero "^2.0.0"
    ismobilejs "^1.0.0"
    json2mq "^0.2.0"
    lodash "^4.17.5"
    moment "^2.21.0"
    mutationobserver-shim "^0.3.2"
    node-emoji "^1.10.0"
    omit.js "^1.0.0"
    raf "^3.4.0"
    resize-observer-polyfill "^1.5.1"
    shallow-equal "^1.0.0"
    shallowequal "^1.0.2"
    vue-ref "^2.0.0"
    warning "^4.0.0"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@~3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/anymatch/download/anymatch-3.1.1.tgz#c55ecf02185e2469259399310c173ce31233b142"
  integrity sha1-xV7PAhheJGklk5kxDBc84xIzsUI=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

arch@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/arch/download/arch-2.2.0.tgz#1bc47818f305764f23ab3306b0bfc086c5a29d11"
  integrity sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE=

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz?cache=0&sync_timestamp=1598649397806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fargparse%2Fdownload%2Fargparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/array-flatten/download/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/array-tree-filter/-/array-tree-filter-2.1.0.tgz#873ac00fec83749f255ac8dd083814b4f6329190"
  integrity sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

arrify@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/arrify/download/arrify-2.0.1.tgz#c9655e9331e0abcd588d2a7cad7e9956f66701fa"
  integrity sha1-yWVekzHgq81YjSp8rX6ZVvZnAfo=

asn1.js@^5.2.0:
  version "5.4.1"
  resolved "https://registry.npm.taobao.org/asn1.js/download/asn1.js-5.4.1.tgz#11a980b84ebb91781ce35b0fdc2ee294e3783f07"
  integrity sha1-EamAuE67kXgc41sP3C7ilON4Pwc=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

asn1@~0.2.3:
  version "0.2.4"
  resolved "https://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/assert/download/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async-each@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/async-each/download/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-validator@^3.0.3:
  version "3.5.2"
  resolved "https://registry.yarnpkg.com/async-validator/-/async-validator-3.5.2.tgz#68e866a96824e8b2694ff7a831c1a25c44d5e500"
  integrity sha512-8eLCg00W9pIRZSB781UUX/H6Oskmm8xloZfr09lz5bikRpBVDlJ3hRVuxxP1SxcwsEYfJ4IU8Q19Y8/893r3rQ==

async-validator@~1.8.1:
  version "1.8.5"
  resolved "https://registry.npm.taobao.org/async-validator/download/async-validator-1.8.5.tgz?cache=0&sync_timestamp=1605751734916&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync-validator%2Fdownload%2Fasync-validator-1.8.5.tgz#dc3e08ec1fd0dddb67e60842f02c0cd1cec6d7f0"
  integrity sha1-3D4I7B/Q3dtn5ghC8CwM0c7G1/A=
  dependencies:
    babel-runtime "6.x"

async@^2.6.2:
  version "2.6.3"
  resolved "https://registry.npm.taobao.org/async/download/async-2.6.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync%2Fdownload%2Fasync-2.6.3.tgz#d72625e2344a3656e3a3ad4fa749fa83299d82ff"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

async@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/async/download/async-3.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync%2Fdownload%2Fasync-3.2.0.tgz#b3a2685c5ebb641d3de02d161002c60fc9f85720"
  integrity sha1-s6JoXF67ZB094C0WEALGD8n4VyA=

async@~0.9.0:
  version "0.9.2"
  resolved "https://registry.npm.taobao.org/async/download/async-0.9.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fasync%2Fdownload%2Fasync-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^9.8.6:
  version "9.8.6"
  resolved "https://registry.npm.taobao.org/autoprefixer/download/autoprefixer-9.8.6.tgz#3b73594ca1bf9266320c5acf1588d74dea74210f"
  integrity sha1-O3NZTKG/kmYyDFrPFYjXTep0IQ8=
  dependencies:
    browserslist "^4.12.0"
    caniuse-lite "^1.0.30001109"
    colorette "^1.2.1"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^7.0.32"
    postcss-value-parser "^4.1.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "https://registry.npm.taobao.org/aws4/download/aws4-1.11.0.tgz?cache=0&sync_timestamp=1604101230105&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faws4%2Fdownload%2Faws4-1.11.0.tgz#d61f46d83b2519250e2784daf5b09479a8b41c59"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axios@^0.21.0:
  version "0.21.1"
  resolved "https://registry.npm.taobao.org/axios/download/axios-0.21.1.tgz?cache=0&sync_timestamp=1608609188013&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faxios%2Fdownload%2Faxios-0.21.1.tgz#22563481962f4d6bde9a76d516ef0e5d3c09b2b8"
  integrity sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg=
  dependencies:
    follow-redirects "^1.10.0"

babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.taobao.org/babel-code-frame/download/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npm.taobao.org/babel-eslint/download/babel-eslint-10.1.0.tgz?cache=0&sync_timestamp=1593529664540&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-eslint%2Fdownload%2Fbabel-eslint-10.1.0.tgz#6968e568a910b78fb3779cdd8b6ac2f479943232"
  integrity sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-helper-vue-jsx-merge-props@^2.0.0, babel-helper-vue-jsx-merge-props@^2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz#22aebd3b33902328e513293a8e4992b384f9f1b6"
  integrity sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg==

babel-loader@^8.1.0:
  version "8.2.2"
  resolved "https://registry.npm.taobao.org/babel-loader/download/babel-loader-8.2.2.tgz?cache=0&sync_timestamp=1606424705083&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.2.2.tgz#9363ce84c10c9a40e6c753748e1441b60c8a0b81"
  integrity sha1-k2POhMEMmkDmx1N0jhRBtgyKC4E=
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^1.4.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://registry.npm.taobao.org/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz#84fda19c976ec5c6defef57f9427b3def66e17a3"
  integrity sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-import@^1.13.3:
  version "1.13.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-import/-/babel-plugin-import-1.13.3.tgz#9dbbba7d1ac72bd412917a830d445e00941d26d7"
  integrity sha512-1qCWdljJOrDRH/ybaCZuDgySii4yYrtQ8OJQwrcDqdt0y67N30ng3X3nABg6j7gR7qUJgcMa9OMhc4AGViDwWw==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

babel-runtime@6.x, babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

bail@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/bail/download/bail-1.0.5.tgz#b6fa133404a392cbc1f8c4bf63f5953351e7a776"
  integrity sha1-tvoTNASjksvB+MS/Y/WVM1Hnp3Y=

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base64-js@^1.0.2:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/base64-js/download/base64-js-1.5.1.tgz?cache=0&sync_timestamp=1605123700994&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbase64-js%2Fdownload%2Fbase64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.npm.taobao.org/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.taobao.org/batch/download/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "https://registry.npm.taobao.org/bfj/download/bfj-6.1.2.tgz#325c861a822bcb358a41c78a33b8e6e2086dde7f"
  integrity sha1-MlyGGoIryzWKQceKM7jm4ght3n8=
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/big.js/download/big.js-3.2.0.tgz#a5fc298b81b9e0dca2e458824784b65c52ba588e"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npm.taobao.org/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

bin-pack@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/bin-pack/download/bin-pack-1.0.2.tgz#c2a014edbf0bed70a3292062ed46577b96120679"
  integrity sha1-wqAU7b8L7XCjKSBi7UZXe5YSBnk=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://registry.npm.taobao.org/binary-extensions/download/binary-extensions-1.13.1.tgz?cache=0&sync_timestamp=1610299268308&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbinary-extensions%2Fdownload%2Fbinary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/binary-extensions/download/binary-extensions-2.2.0.tgz?cache=0&sync_timestamp=1610299268308&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbinary-extensions%2Fdownload%2Fbinary-extensions-2.2.0.tgz#75f502eeaf9ffde42fc98829645be4ea76bd9e2d"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bluebird@^3.1.1, bluebird@^3.5.0, bluebird@^3.5.5:
  version "3.7.2"
  resolved "https://registry.npm.taobao.org/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.4.0:
  version "4.11.9"
  resolved "https://registry.npm.taobao.org/bn.js/download/bn.js-4.11.9.tgz#26d556829458f9d1e81fc48952493d0ba3507828"
  integrity sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=

bn.js@^5.0.0, bn.js@^5.1.1:
  version "5.1.3"
  resolved "https://registry.npm.taobao.org/bn.js/download/bn.js-5.1.3.tgz#beca005408f642ebebea80b042b4d18d2ac0ee6b"
  integrity sha1-vsoAVAj2Quvr6oCwQrTRjSrA7ms=

body-parser@1.19.0, body-parser@^1.18.3:
  version "1.19.0"
  resolved "https://registry.npm.taobao.org/body-parser/download/body-parser-1.19.0.tgz#96b2709e57c9c4e09a6fd66a8fd979844f69f08a"
  integrity sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.npm.taobao.org/bonjour/download/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz?cache=0&sync_timestamp=1601898285594&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbrace-expansion%2Fdownload%2Fbrace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.2.2, braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/brorand/download/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/browserify-aes/download/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/browserify-cipher/download/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/browserify-des/download/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/browserify-rsa/download/browserify-rsa-4.1.0.tgz#b2fd06b5b75ae297f7ce2dc651f918f5be158c8d"
  integrity sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=
  dependencies:
    bn.js "^5.0.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.1"
  resolved "https://registry.npm.taobao.org/browserify-sign/download/browserify-sign-4.2.1.tgz?cache=0&sync_timestamp=1596557839950&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbrowserify-sign%2Fdownload%2Fbrowserify-sign-4.2.1.tgz#eaf4add46dd54be3bb3b36c0cf15abbeba7956c3"
  integrity sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=
  dependencies:
    bn.js "^5.1.1"
    browserify-rsa "^4.0.1"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.3"
    inherits "^2.0.4"
    parse-asn1 "^5.1.5"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^4.0.0, browserslist@^4.12.0, browserslist@^4.14.5, browserslist@^4.16.1:
  version "4.16.1"
  resolved "https://registry.npm.taobao.org/browserslist/download/browserslist-4.16.1.tgz?cache=0&sync_timestamp=1609895749453&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbrowserslist%2Fdownload%2Fbrowserslist-4.16.1.tgz#bf757a2da376b3447b800a16f0f1c96358138766"
  integrity sha1-v3V6LaN2s0R7gAoW8PHJY1gTh2Y=
  dependencies:
    caniuse-lite "^1.0.30001173"
    colorette "^1.2.1"
    electron-to-chromium "^1.3.634"
    escalade "^3.1.1"
    node-releases "^1.1.69"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-json@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/buffer-json/download/buffer-json-2.0.0.tgz#f73e13b1e42f196fe2fd67d001c7d7107edd7c23"
  integrity sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/buffer-xor/download/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "https://registry.npm.taobao.org/buffer/download/buffer-4.9.2.tgz?cache=0&sync_timestamp=1606098073225&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer%2Fdownload%2Fbuffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/bytes/download/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/bytes/download/bytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cacache@^12.0.2, cacache@^12.0.3:
  version "12.0.4"
  resolved "https://registry.npm.taobao.org/cacache/download/cacache-12.0.4.tgz?cache=0&sync_timestamp=1594429684526&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcacache%2Fdownload%2Fcacache-12.0.4.tgz#668bcbd105aeb5f1d92fe25570ec9525c8faa40c"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cacache@^13.0.1:
  version "13.0.1"
  resolved "https://registry.npm.taobao.org/cacache/download/cacache-13.0.1.tgz?cache=0&sync_timestamp=1594429684526&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcacache%2Fdownload%2Fcacache-13.0.1.tgz#a8000c21697089082f85287a1aec6e382024a71c"
  integrity sha1-qAAMIWlwiQgvhSh6GuxuOCAkpxw=
  dependencies:
    chownr "^1.1.2"
    figgy-pudding "^3.5.1"
    fs-minipass "^2.0.0"
    glob "^7.1.4"
    graceful-fs "^4.2.2"
    infer-owner "^1.0.4"
    lru-cache "^5.1.1"
    minipass "^3.0.0"
    minipass-collect "^1.0.2"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.2"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    p-map "^3.0.0"
    promise-inflight "^1.0.1"
    rimraf "^2.7.1"
    ssri "^7.0.0"
    unique-filename "^1.1.1"

cacache@^15.0.5:
  version "15.0.5"
  resolved "https://registry.npm.taobao.org/cacache/download/cacache-15.0.5.tgz?cache=0&sync_timestamp=1594429684526&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcacache%2Fdownload%2Fcacache-15.0.5.tgz#69162833da29170d6732334643c60e005f5f17d0"
  integrity sha1-aRYoM9opFw1nMjNGQ8YOAF9fF9A=
  dependencies:
    "@npmcli/move-file" "^1.0.1"
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    glob "^7.1.4"
    infer-owner "^1.0.4"
    lru-cache "^6.0.0"
    minipass "^3.1.1"
    minipass-collect "^1.0.2"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.2"
    mkdirp "^1.0.3"
    p-map "^4.0.0"
    promise-inflight "^1.0.1"
    rimraf "^3.0.2"
    ssri "^8.0.0"
    tar "^6.0.2"
    unique-filename "^1.1.1"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-loader@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/cache-loader/download/cache-loader-4.1.0.tgz?cache=0&sync_timestamp=1601432152909&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcache-loader%2Fdownload%2Fcache-loader-4.1.0.tgz#9948cae353aec0a1fcb1eafda2300816ec85387e"
  integrity sha1-mUjK41OuwKH8ser9ojAIFuyFOH4=
  dependencies:
    buffer-json "^2.0.0"
    find-cache-dir "^3.0.0"
    loader-utils "^1.2.3"
    mkdirp "^0.5.1"
    neo-async "^2.6.1"
    schema-utils "^2.0.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/call-bind/download/call-bind-1.0.2.tgz?cache=0&sync_timestamp=1610405478355&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcall-bind%2Fdownload%2Fcall-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/call-me-maybe/download/call-me-maybe-1.0.1.tgz#26d208ea89e37b5cbde60250a15f031c16a4d66b"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz?cache=0&sync_timestamp=1606869170809&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamel-case%2Fdownload%2Fcamel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camel-case@^4.1.1:
  version "4.1.2"
  resolved "https://registry.npm.taobao.org/camel-case/download/camel-case-4.1.2.tgz?cache=0&sync_timestamp=1606869170809&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamel-case%2Fdownload%2Fcamel-case-4.1.2.tgz#9728072a954f805228225a6deea6b38461e1bd5a"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase-keys@^6.2.2:
  version "6.2.2"
  resolved "https://registry.npm.taobao.org/camelcase-keys/download/camelcase-keys-6.2.2.tgz#5e755d6ba51aa223ec7d3d52f25778210f9dc3c0"
  integrity sha1-XnVda6UaoiPsfT1S8ld4IQ+dw8A=
  dependencies:
    camelcase "^5.3.1"
    map-obj "^4.0.0"
    quick-lru "^4.0.1"

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1603921884289&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.0.0:
  version "6.2.0"
  resolved "https://registry.npm.taobao.org/camelcase/download/camelcase-6.2.0.tgz?cache=0&sync_timestamp=1603921884289&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamelcase%2Fdownload%2Fcamelcase-6.2.0.tgz#924af881c9d525ac9d87f40d964e5cea982a1809"
  integrity sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk=

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/caniuse-api/download/caniuse-api-3.0.0.tgz#5e4d90e2274961d46291997df599e3ed008ee4c0"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001109, caniuse-lite@^1.0.30001173:
  version "1.0.30001179"
  resolved "https://registry.npm.taobao.org/caniuse-lite/download/caniuse-lite-1.0.30001179.tgz#b0803883b4471a6c62066fb1752756f8afc699c8"
  integrity sha1-sIA4g7RHGmxiBm+xdSdW+K/Gmcg=

case-sensitive-paths-webpack-plugin@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.3.0.tgz#23ac613cc9a856e4f88ff8bb73bbb5e989825cf7"
  integrity sha1-I6xhPMmoVuT4j/i7c7u16YmCXPc=

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz?cache=0&sync_timestamp=1592843133653&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchalk%2Fdownload%2Fchalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.3.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npm.taobao.org/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1592843133653&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/chalk/download/chalk-4.1.0.tgz?cache=0&sync_timestamp=1592843133653&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchalk%2Fdownload%2Fchalk-4.1.0.tgz#4e14870a618d9e2edd97dd8345fd9d9dc315646a"
  integrity sha1-ThSHCmGNni7dl92DRf2dncMVZGo=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

character-entities-legacy@^1.0.0:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/character-entities-legacy/download/character-entities-legacy-1.1.4.tgz#94bc1845dce70a5bb9d2ecc748725661293d8fc1"
  integrity sha1-lLwYRdznClu50uzHSHJWYSk9j8E=

character-entities@^1.0.0:
  version "1.2.4"
  resolved "https://registry.npm.taobao.org/character-entities/download/character-entities-1.2.4.tgz#e12c3939b7eaf4e5b15e7ad4c5e28e1d48c5b16b"
  integrity sha1-4Sw5Obfq9OWxXnrUxeKOHUjFsWs=

character-reference-invalid@^1.0.0:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/character-reference-invalid/download/character-reference-invalid-1.1.4.tgz#083329cda0eae272ab3dbbf37e9a382c13af1560"
  integrity sha1-CDMpzaDq4nKrPbvzfpo4LBOvFWA=

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/chardet/download/chardet-0.7.0.tgz?cache=0&sync_timestamp=1601032467034&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchardet%2Fdownload%2Fchardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-types@^8.0.3:
  version "8.0.3"
  resolved "https://registry.npm.taobao.org/check-types/download/check-types-8.0.3.tgz#3356cca19c889544f2d7a95ed49ce508a0ecf552"
  integrity sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=

"chokidar@>=2.0.0 <4.0.0", chokidar@^3.4.1:
  version "3.5.1"
  resolved "https://registry.npm.taobao.org/chokidar/download/chokidar-3.5.1.tgz?cache=0&sync_timestamp=1610719499558&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-3.5.1.tgz#ee9ce7bbebd2b79f49f304799d5468e31e14e68a"
  integrity sha1-7pznu+vSt59J8wR5nVRo4x4U5oo=
  dependencies:
    anymatch "~3.1.1"
    braces "~3.0.2"
    glob-parent "~5.1.0"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.5.0"
  optionalDependencies:
    fsevents "~2.3.1"

chokidar@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npm.taobao.org/chokidar/download/chokidar-2.1.8.tgz?cache=0&sync_timestamp=1610719499558&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chownr@^1.1.1, chownr@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/chownr/download/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/chownr/download/chownr-2.0.0.tgz#15bfbe53d2eab4cf70f18a8cd68ebe5b3cb1dece"
  integrity sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=

chrome-trace-event@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz#234090ee97c7d4ad1a2c4beae27505deffc608a4"
  integrity sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ=
  dependencies:
    tslib "^1.9.0"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://registry.npm.taobao.org/ci-info/download/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/cipher-base/download/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

classnames@^2.2.5:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/classnames/-/classnames-2.3.1.tgz#dfcfa3891e306ec1dad105d0e88f4417b8535e8e"
  integrity sha512-OlQdbZ7gLfGarSqxesMesDa5uz7KFbID8Kpq/SxIoNGDqY8lSYs0D+hhtBXhcdB3rcbXArFr7vlHheLk1voeNA==

clean-css@4.2.x, clean-css@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npm.taobao.org/clean-css/download/clean-css-4.2.3.tgz#507b5de7d97b48ee53d84adb0160ff6216380f78"
  integrity sha1-UHtd59l7SO5T2ErbAWD/YhY4D3g=
  dependencies:
    source-map "~0.6.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/clean-stack/download/clean-stack-2.2.0.tgz?cache=0&sync_timestamp=1605702448295&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fclean-stack%2Fdownload%2Fclean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.4:
  version "2.1.10"
  resolved "https://registry.npm.taobao.org/cli-highlight/download/cli-highlight-2.1.10.tgz?cache=0&sync_timestamp=1610119762804&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-highlight%2Fdownload%2Fcli-highlight-2.1.10.tgz#26a087da9209dce4fcb8cf5427dc97cd96ac173a"
  integrity sha1-JqCH2pIJ3OT8uM9UJ9yXzZasFzo=
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.0.0"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.0.0:
  version "2.5.0"
  resolved "https://registry.npm.taobao.org/cli-spinners/download/cli-spinners-2.5.0.tgz#12763e47251bf951cb75c201dfa58ff1bcb2d047"
  integrity sha1-EnY+RyUb+VHLdcIB36WP8byy0Ec=

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

clipboard@^2.0.0:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/clipboard/download/clipboard-2.0.6.tgz#52921296eec0fdf77ead1749421b21c968647376"
  integrity sha1-UpISlu7A/fd+rRdJQhshyWhkc3Y=
  dependencies:
    good-listener "^1.2.2"
    select "^1.1.2"
    tiny-emitter "^2.0.0"

clipboardy@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/clipboardy/download/clipboardy-2.3.0.tgz#3c2903650c68e46a91b388985bc2774287dba290"
  integrity sha1-PCkDZQxo5GqRs4iYW8J3QofbopA=
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/cliui/download/cliui-6.0.0.tgz#511d702c0c4e41ca156d7d0e96021f23e13225b1"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://registry.npm.taobao.org/cliui/download/cliui-7.0.4.tgz#a0265ee655476fc807aea9df3df8df7783808b4f"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone-regexp@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/clone-regexp/download/clone-regexp-2.2.0.tgz#7d65e00885cd8796405c35a737e7a86b7429e36f"
  integrity sha1-fWXgCIXNh5ZAXDWnN+eoa3Qp428=
  dependencies:
    is-regexp "^2.0.0"

clone-stats@^0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/clone-stats/download/clone-stats-0.0.1.tgz#b88f94a82cf38b8791d58046ea4029ad88ca99d1"
  integrity sha1-uI+UqCzzi4eR1YBG6kAprYjKmdE=

clone@^1.0.0, clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/clone/download/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

coa@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/coa/download/coa-2.0.2.tgz#43f6c21151b4ef2bf57187db0d73de229e3e7ec3"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.1:
  version "1.9.3"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.5.4:
  version "1.5.4"
  resolved "https://registry.npm.taobao.org/color-string/download/color-string-1.5.4.tgz#dd51cd25cfee953d138fe4002372cc3d0e504cb6"
  integrity sha1-3VHNJc/ulT0Tj+QAI3LMPQ5QTLY=
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.0.0:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/color/download/color-3.1.3.tgz#ca67fb4e7b97d611dcde39eceed422067d91596e"
  integrity sha1-ymf7TnuX1hHc3jns7tQiBn2RWW4=
  dependencies:
    color-convert "^1.9.1"
    color-string "^1.5.4"

colorette@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/colorette/download/colorette-1.2.1.tgz?cache=0&sync_timestamp=1593955804228&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcolorette%2Fdownload%2Fcolorette-1.2.1.tgz#4d0b921325c14faf92633086a536db6e89564b1b"
  integrity sha1-TQuSEyXBT6+SYzCGpTbbbolWSxs=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@*:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/commander/download/commander-7.0.0.tgz?cache=0&sync_timestamp=1610702173050&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-7.0.0.tgz#3e2bbfd8bb6724760980988fb5b22b7ee6b71ab2"
  integrity sha1-Piu/2LtnJHYJgJiPtbIrfua3GrI=

commander@2.17.x:
  version "2.17.1"
  resolved "https://registry.npm.taobao.org/commander/download/commander-2.17.1.tgz?cache=0&sync_timestamp=1610702173050&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.17.1.tgz#bd77ab7de6de94205ceacc72f1716d29f20a77bf"
  integrity sha1-vXerfebelCBc6sxy8XFtKfIKd78=

commander@^2.18.0, commander@^2.20.0, commander@^2.20.3:
  version "2.20.3"
  resolved "https://registry.npm.taobao.org/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1610702173050&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/commander/download/commander-4.1.1.tgz?cache=0&sync_timestamp=1610702173050&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-4.1.1.tgz#9fd602bd936294e9e9ef46a3f4d6964044b18068"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

commander@~2.19.0:
  version "2.19.0"
  resolved "https://registry.npm.taobao.org/commander/download/commander-2.19.0.tgz?cache=0&sync_timestamp=1610702173050&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcommander%2Fdownload%2Fcommander-2.19.0.tgz#f6198aa84e5b83c46054b94ddedbfed5ee9ff12a"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-classes@^1.2.6:
  version "1.2.6"
  resolved "https://registry.yarnpkg.com/component-classes/-/component-classes-1.2.6.tgz#c642394c3618a4d8b0b8919efccbbd930e5cd691"
  integrity sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE=
  dependencies:
    component-indexof "0.0.3"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

component-indexof@0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/component-indexof/-/component-indexof-0.0.3.tgz#11d091312239eb8f32c8f25ae9cb002ffe8d3c24"
  integrity sha1-EdCRMSI5648yyPJa6csAL/6NPCQ=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npm.taobao.org/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression-webpack-plugin@^6.1.1:
  version "6.1.1"
  resolved "https://registry.npm.taobao.org/compression-webpack-plugin/download/compression-webpack-plugin-6.1.1.tgz?cache=0&sync_timestamp=1610367126141&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcompression-webpack-plugin%2Fdownload%2Fcompression-webpack-plugin-6.1.1.tgz#ae8e4b2ffdb7396bb776e66918d751a20d8ccf0e"
  integrity sha1-ro5LL/23OWu3duZpGNdRog2Mzw4=
  dependencies:
    cacache "^15.0.5"
    find-cache-dir "^3.3.1"
    schema-utils "^3.0.0"
    serialize-javascript "^5.0.1"
    webpack-sources "^1.4.3"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npm.taobao.org/compression/download/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "https://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

concat-stream@~1.5.1:
  version "1.5.2"
  resolved "https://registry.npm.taobao.org/concat-stream/download/concat-stream-1.5.2.tgz#708978624d856af41a5a741defdd261da752c266"
  integrity sha1-cIl4Yk2FavQaWnQd790mHadSwmY=
  dependencies:
    inherits "~2.0.1"
    readable-stream "~2.0.0"
    typedarray "~0.0.5"

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/console-browserify/download/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

consolidate@^0.15.1:
  version "0.15.1"
  resolved "https://registry.npm.taobao.org/consolidate/download/consolidate-0.15.1.tgz#21ab043235c71a07d45d9aad98593b0dba56bab7"
  integrity sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

contentstream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/contentstream/download/contentstream-1.0.0.tgz#0bdcfa46da30464a86ce8fa7ece565410dc6f9a5"
  integrity sha1-C9z6RtowRkqGzo+n7OVlQQ3G+aU=
  dependencies:
    readable-stream "~1.0.33-1"

convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npm.taobao.org/convert-source-map/download/convert-source-map-1.7.0.tgz#17a2cb882d7f77d3490585e2ce6c524424a3a442"
  integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/cookie/download/cookie-0.4.0.tgz#beb437e7022b3b6d49019d088665303ebe9c14ba"
  integrity sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-webpack-plugin@^5.1.1:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/copy-webpack-plugin/download/copy-webpack-plugin-5.1.2.tgz?cache=0&sync_timestamp=1608126698047&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcopy-webpack-plugin%2Fdownload%2Fcopy-webpack-plugin-5.1.2.tgz#8a889e1dcafa6c91c6cd4be1ad158f1d3823bae2"
  integrity sha1-ioieHcr6bJHGzUvhrRWPHTgjuuI=
  dependencies:
    cacache "^12.0.3"
    find-cache-dir "^2.1.0"
    glob-parent "^3.1.0"
    globby "^7.1.1"
    is-glob "^4.0.1"
    loader-utils "^1.2.3"
    minimatch "^3.0.4"
    normalize-path "^3.0.0"
    p-limit "^2.2.1"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    webpack-log "^2.0.0"

core-js-compat@^3.6.5, core-js-compat@^3.8.0:
  version "3.8.3"
  resolved "https://registry.npm.taobao.org/core-js-compat/download/core-js-compat-3.8.3.tgz?cache=0&sync_timestamp=1611038800535&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js-compat%2Fdownload%2Fcore-js-compat-3.8.3.tgz#9123fb6b9cad30f0651332dc77deba48ef9b0b3f"
  integrity sha1-kSP7a5ytMPBlEzLcd966SO+bCz8=
  dependencies:
    browserslist "^4.16.1"
    semver "7.0.0"

core-js@^2.4.0:
  version "2.6.12"
  resolved "https://registry.npm.taobao.org/core-js/download/core-js-2.6.12.tgz?cache=0&sync_timestamp=1611040749668&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js%2Fdownload%2Fcore-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-js@^3.6.4, core-js@^3.6.5:
  version "3.8.3"
  resolved "https://registry.npm.taobao.org/core-js/download/core-js-3.8.3.tgz?cache=0&sync_timestamp=1611040749668&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js%2Fdownload%2Fcore-js-3.8.3.tgz#c21906e1f14f3689f93abcc6e26883550dd92dd0"
  integrity sha1-whkG4fFPNon5OrzG4miDVQ3ZLdA=

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "https://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cosmiconfig@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-7.0.0.tgz#ef9b44d773959cae63ddecd122de23853b60f8d3"
  integrity sha1-75tE13OVnK5j3ezRIt4jhTtg+NM=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "https://registry.npm.taobao.org/create-ecdh/download/create-ecdh-4.0.4.tgz?cache=0&sync_timestamp=1596557810113&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcreate-ecdh%2Fdownload%2Fcreate-ecdh-4.0.4.tgz#d6e7f4bffa66736085a0762fd3a632684dabcc4e"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/create-hash/download/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://registry.npm.taobao.org/create-hmac/download/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.2:
  version "7.0.3"
  resolved "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-color-names@0.0.4, css-color-names@^0.0.4:
  version "0.0.4"
  resolved "https://registry.npm.taobao.org/css-color-names/download/css-color-names-0.0.4.tgz#808adc2e79cf84738069b646cb20ec27beb629e0"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz#c198940f63a76d7e36c1e71018b001721054cb22"
  integrity sha1-wZiUD2OnbX42wecQGLABchBUyyI=
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-loader@^3.5.3:
  version "3.6.0"
  resolved "https://registry.npm.taobao.org/css-loader/download/css-loader-3.6.0.tgz?cache=0&sync_timestamp=1604507486215&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-loader%2Fdownload%2Fcss-loader-3.6.0.tgz#2e4b2c7e6e2d27f8c8f28f61bffcd2e6c91ef645"
  integrity sha1-Lkssfm4tJ/jI8o9hv/zS5ske9kU=
  dependencies:
    camelcase "^5.3.1"
    cssesc "^3.0.0"
    icss-utils "^4.1.1"
    loader-utils "^1.2.3"
    normalize-path "^3.0.0"
    postcss "^7.0.32"
    postcss-modules-extract-imports "^2.0.0"
    postcss-modules-local-by-default "^3.0.2"
    postcss-modules-scope "^2.2.0"
    postcss-modules-values "^3.0.0"
    postcss-value-parser "^4.1.0"
    schema-utils "^2.7.0"
    semver "^6.3.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz#3b2ff4972cc362ab88561507a95408a1432135d7"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^2.0.0, css-select@^2.0.2:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/css-select/download/css-select-2.1.0.tgz?cache=0&sync_timestamp=1608486296143&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-select%2Fdownload%2Fcss-select-2.1.0.tgz#6a34653356635934a81baca68d0255432105dbef"
  integrity sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8=
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://registry.npm.taobao.org/css-tree/download/css-tree-1.0.0-alpha.37.tgz?cache=0&sync_timestamp=1606404102945&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.0.0-alpha.37.tgz#98bebd62c4c1d9f960ec340cf9f7522e30709a22"
  integrity sha1-mL69YsTB2flg7DQM+fdSLjBwmiI=
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-tree@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/css-tree/download/css-tree-1.1.2.tgz?cache=0&sync_timestamp=1606404102945&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-tree%2Fdownload%2Fcss-tree-1.1.2.tgz#9ae393b5dafd7dae8a622475caec78d3d8fbd7b5"
  integrity sha1-muOTtdr9fa6KYiR1yux409j717U=
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://registry.npm.taobao.org/css-what/download/css-what-3.4.2.tgz?cache=0&sync_timestamp=1602570915327&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcss-what%2Fdownload%2Fcss-what-3.4.2.tgz#ea7026fcb01777edbde52124e21f327e7ae950e4"
  integrity sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssfilter@0.0.10:
  version "0.0.10"
  resolved "https://registry.npm.taobao.org/cssfilter/download/cssfilter-0.0.10.tgz#c6d2672632a2e5c83e013e6864a42ce8defd20ae"
  integrity sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=

cssnano-preset-default@^4.0.0, cssnano-preset-default@^4.0.7:
  version "4.0.7"
  resolved "https://registry.npm.taobao.org/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz?cache=0&sync_timestamp=1610386861036&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssnano-preset-default%2Fdownload%2Fcssnano-preset-default-4.0.7.tgz#51ec662ccfca0f88b396dcd9679cdb931be17f76"
  integrity sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y=
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.2"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz#ed3a08299f21d75741b20f3b81f194ed49cc150f"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz#c0e4ca07f5386bb17ec5e52250b4f5961365156d"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz#b26d5fd5f72a11dfe7a7846fb4c67260f96bf282"
  integrity sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz#574082fb2859d2db433855835d9a8456ea18bbf3"
  integrity sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=

cssnano@^4.0.0, cssnano@^4.1.10:
  version "4.1.10"
  resolved "https://registry.npm.taobao.org/cssnano/download/cssnano-4.1.10.tgz?cache=0&sync_timestamp=1610386860907&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssnano%2Fdownload%2Fcssnano-4.1.10.tgz#0ac41f0b13d13d465487e111b778d42da631b8b2"
  integrity sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.7"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^4.0.2:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/csso/download/csso-4.2.0.tgz?cache=0&sync_timestamp=1606408849393&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcsso%2Fdownload%2Fcsso-4.2.0.tgz#ea3a561346e8dc9f546d6febedd50187cf389529"
  integrity sha1-6jpWE0bo3J9UbW/r7dUBh884lSk=
  dependencies:
    css-tree "^1.1.2"

cwise-compiler@^1.0.0, cwise-compiler@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/cwise-compiler/download/cwise-compiler-1.1.3.tgz#f4d667410e850d3a313a7d2db7b1e505bb034cc5"
  integrity sha1-9NZnQQ6FDToxOn0tt7HlBbsDTMU=
  dependencies:
    uniq "^1.0.0"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/cyclist/download/cyclist-1.0.1.tgz#596e9698fd0c80e12038c2b82d6eb1b35b6224d9"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz?cache=0&sync_timestamp=1601073602368&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdashdash%2Fdownload%2Fdashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-uri-to-buffer@0.0.3:
  version "0.0.3"
  resolved "https://registry.npm.taobao.org/data-uri-to-buffer/download/data-uri-to-buffer-0.0.3.tgz#18ae979a6a0ca994b0625853916d2662bbae0b1a"
  integrity sha1-GK6XmmoMqZSwYlhTkW0mYruuCxo=

dayjs@^1.9.4:
  version "1.10.4"
  resolved "https://registry.npm.taobao.org/dayjs/download/dayjs-1.10.4.tgz?cache=0&sync_timestamp=1611309982734&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdayjs%2Fdownload%2Fdayjs-1.10.4.tgz#8e544a9b8683f61783f570980a8a80eaf54ab1e2"
  integrity sha1-jlRKm4aD9heD9XCYCoqA6vVKseI=

de-indent@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/de-indent/download/de-indent-1.0.2.tgz#b2038e846dc33baa5796128d0804b455b8c1e21d"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1607566571506&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@^3.1.1, debug@^3.2.6:
  version "3.2.7"
  resolved "https://registry.npm.taobao.org/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1607566571506&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.0.0, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npm.taobao.org/debug/download/debug-4.3.1.tgz?cache=0&sync_timestamp=1607566571506&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdebug%2Fdownload%2Fdebug-4.3.1.tgz#f0d229c505e0c6d8c49ac553d1b13dc183f6b2ee"
  integrity sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=
  dependencies:
    ms "2.1.2"

decamelize-keys@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/decamelize-keys/download/decamelize-keys-1.1.0.tgz#d171a87933252807eb3cb61dc1c1445d078df2d9"
  integrity sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk=
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1610348666353&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/deep-equal/download/deep-equal-1.1.1.tgz#b5c98c942ceffaf7cb051e24e1434a25a2e6076a"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npm.taobao.org/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@1.3.2:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/deepmerge/download/deepmerge-1.3.2.tgz#1663691629d4dbfe364fa12a2a4f0aa86aa3a050"
  integrity sha1-FmNpFinU2/42T6EqKk8KqGqjoFA=

deepmerge@^1.2.0, deepmerge@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npm.taobao.org/deepmerge/download/deepmerge-1.5.2.tgz#10499d868844cdad4fee0842df8c7f6f0c95a753"
  integrity sha1-EEmdhohEza1P7ghC34x/bwyVp1M=

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npm.taobao.org/deepmerge/download/deepmerge-4.2.2.tgz#44d2ea3679b8f4d4ffba33f03d865fc1e7bf4955"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/default-gateway/download/default-gateway-4.2.0.tgz?cache=0&sync_timestamp=1610365857779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdefault-gateway%2Fdownload%2Fdefault-gateway-4.2.0.tgz#167104c7500c2115f6dd69b0a536bb8ed720552b"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

default-gateway@^5.0.5:
  version "5.0.5"
  resolved "https://registry.npm.taobao.org/default-gateway/download/default-gateway-5.0.5.tgz?cache=0&sync_timestamp=1610365857779&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdefault-gateway%2Fdownload%2Fdefault-gateway-5.0.5.tgz#4fd6bd5d2855d39b34cc5a59505486e9aafc9b10"
  integrity sha1-T9a9XShV05s0zFpZUFSG6ar8mxA=
  dependencies:
    execa "^3.3.0"

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/del/download/del-4.1.1.tgz#9e8f117222ea44a31ff3a156c049b99052a9f0b4"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegate@^3.1.2:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/delegate/download/delegate-3.2.0.tgz#b66b71c3158522e8ab5744f720d8ca0c2af59166"
  integrity sha1-tmtxwxWFIuirV0T3INjKDCr1kWY=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/des.js/download/des.js-1.0.1.tgz#5382142e1bdc53f85d86d53e5f4aa7deb91e0843"
  integrity sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-node@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/detect-node/download/detect-node-2.0.4.tgz#014ee8f8f669c5c58023da64b8179c083a28c46c"
  integrity sha1-AU7o+PZpxcWAI9pkuBecCDooxGw=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://registry.npm.taobao.org/diffie-hellman/download/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dir-glob@^2.0.0, dir-glob@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.2.2.tgz#fa09f0694153c8918b18ba0deafae94769fc50c4"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/dns-equal/download/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/dns-packet/download/dns-packet-1.3.1.tgz#12aa426981075be500b910eedcd0b47dd7deda5a"
  integrity sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/dns-txt/download/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-align@^1.10.4:
  version "1.12.1"
  resolved "https://registry.yarnpkg.com/dom-align/-/dom-align-1.12.1.tgz#aedbc2c312983b46ea6b7a4d636617336a490ee9"
  integrity sha512-CdTD9EdA5WviP8oO3n+okOm0Xt7dSuWxRTLcJiW0memwUr3Tvz66JDDCh9cb50IZFHXvBmLoyX454uJU/EVg+g==

dom-closest@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/dom-closest/-/dom-closest-0.2.0.tgz#ebd9f91d1bf22e8d6f477876bbcd3ec90216c0cf"
  integrity sha1-69n5HRvyLo1vR3h2u80+yQIWwM8=
  dependencies:
    dom-matches ">=1.0.1"

dom-converter@^0.2:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-matches@>=1.0.1:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/dom-matches/-/dom-matches-2.0.0.tgz#d2728b416a87533980eb089b848d253cf23a758c"
  integrity sha1-0nKLQWqHUzmA6wibhI0lPPI6dYw=

dom-scroll-into-view@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/dom-scroll-into-view/-/dom-scroll-into-view-2.0.1.tgz#0decc8522801fd8d3f1c6ba355a74d382c5f989b"
  integrity sha512-bvVTQe1lfaUr1oFzZX80ce9KLDlZ3iU+XGNE/bz9HnGdklTieqsbmsLHe+rT2XWqopvL0PckkYqN7ksmm5pe3w==

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.npm.taobao.org/dom-serializer/download/dom-serializer-0.2.2.tgz?cache=0&sync_timestamp=1607193380961&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdom-serializer%2Fdownload%2Fdom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/domain-browser/download/domain-browser-1.2.0.tgz?cache=0&sync_timestamp=1604239998047&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomain-browser%2Fdownload%2Fdomain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1, domelementtype@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.1.tgz?cache=0&sync_timestamp=1606865995285&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomelementtype%2Fdownload%2Fdomelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/domelementtype/download/domelementtype-2.1.0.tgz?cache=0&sync_timestamp=1606865995285&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomelementtype%2Fdownload%2Fdomelementtype-2.1.0.tgz#a851c080a6d1c3d94344aed151d99f669edf585e"
  integrity sha1-qFHAgKbRw9lDRK7RUdmfZp7fWF4=

domhandler@^2.3.0:
  version "2.4.2"
  resolved "https://registry.npm.taobao.org/domhandler/download/domhandler-2.4.2.tgz?cache=0&sync_timestamp=1606872280855&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomhandler%2Fdownload%2Fdomhandler-2.4.2.tgz#8805097e933d65e85546f726d60f5eb88b44f803"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domready@1.0.8:
  version "1.0.8"
  resolved "https://registry.npm.taobao.org/domready/download/domready-1.0.8.tgz#91f252e597b65af77e745ae24dd0185d5e26d58c"
  integrity sha1-kfJS5Ze2Wvd+dFriTdAYXV4m1Yw=

domutils@^1.5.1, domutils@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npm.taobao.org/domutils/download/domutils-1.7.0.tgz?cache=0&sync_timestamp=1607393056952&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomutils%2Fdownload%2Fdomutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/dot-case/download/dot-case-3.0.4.tgz?cache=0&sync_timestamp=1606867306848&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-case%2Fdownload%2Fdot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npm.taobao.org/dot-prop/download/dot-prop-5.3.0.tgz?cache=0&sync_timestamp=1605778245785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-prop%2Fdownload%2Fdot-prop-5.3.0.tgz#90ccce708cd9cd82cc4dc8c3ddd9abdd55b20e88"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npm.taobao.org/dotenv-expand/download/dotenv-expand-5.1.0.tgz#3fbaf020bfd794884072ea26b1e9791d45a629f0"
  integrity sha1-P7rwIL/XlIhAcuomsel5HUWmKfA=

dotenv@^8.2.0:
  version "8.2.0"
  resolved "https://registry.npm.taobao.org/dotenv/download/dotenv-8.2.0.tgz#97e619259ada750eea3e4ea3e26bceea5424b16a"
  integrity sha1-l+YZJZradQ7qPk6j4mvO6lQksWo=

duplexer@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/duplexer/download/duplexer-0.1.2.tgz#3abe43aef3835f8ae077d136ddce0f276b0400e6"
  integrity sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://registry.npm.taobao.org/duplexify/download/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

easy-stack@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/easy-stack/download/easy-stack-1.0.1.tgz#8afe4264626988cabb11f3c704ccd0c835411066"
  integrity sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "https://registry.npm.taobao.org/ejs/download/ejs-2.7.4.tgz?cache=0&sync_timestamp=1597678431282&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fejs%2Fdownload%2Fejs-2.7.4.tgz#48661287573dcc53e366c7a1ae52c3a120eec9ba"
  integrity sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=

electron-to-chromium@^1.3.634:
  version "1.3.645"
  resolved "https://registry.npm.taobao.org/electron-to-chromium/download/electron-to-chromium-1.3.645.tgz?cache=0&sync_timestamp=1611601526973&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Felectron-to-chromium%2Fdownload%2Felectron-to-chromium-1.3.645.tgz#c0b269ae2ecece5aedc02dd4586397d8096affb1"
  integrity sha1-wLJpri7OzlrtwC3UWGOX2Alq/7E=

element-ui@^2.14.0:
  version "2.15.0"
  resolved "https://registry.npm.taobao.org/element-ui/download/element-ui-2.15.0.tgz?cache=0&sync_timestamp=1610711151767&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Felement-ui%2Fdownload%2Felement-ui-2.15.0.tgz#de9b73a8d1e3e3b50e82b923a5fa95295239bd41"
  integrity sha1-3ptzqNHj47UOgrkjpfqVKVI5vUE=
  dependencies:
    async-validator "~1.8.1"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"

elliptic@^6.5.3:
  version "6.5.3"
  resolved "https://registry.npm.taobao.org/elliptic/download/elliptic-6.5.3.tgz#cb59eb2efdaf73a0bd78ccd7015a62ad6e0f93d6"
  integrity sha1-y1nrLv2vc6C9eMzXAVpirW4Pk9Y=
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-7.0.3.tgz?cache=0&sync_timestamp=1603212288390&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Femoji-regex%2Fdownload%2Femoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1603212288390&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/emojis-list/download/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npm.taobao.org/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz?cache=0&sync_timestamp=1610568507654&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-4.5.0.tgz#2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec"
  integrity sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.yarnpkg.com/enquire.js/-/enquire.js-2.1.6.tgz#3e8780c9b8b835084c3f60e166dbc3c2a3c89814"
  integrity sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ=

enquirer@^2.3.5:
  version "2.3.6"
  resolved "https://registry.npm.taobao.org/enquirer/download/enquirer-2.3.6.tgz#2a7fe5dd634a1e4125a975ec994ff5456dc3734d"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

entities@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/entities/download/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/entities/download/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

errno@^0.1.3, errno@~0.1.7:
  version "0.1.8"
  resolved "https://registry.npm.taobao.org/errno/download/errno-0.1.8.tgz#8bb3e9c7d463be4976ff888f76b4809ebc2e811f"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.2:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.6.tgz#5a99a707bd7a4c58a797902d48d82803ede6aad8"
  integrity sha1-WpmnB716TFinl5AtSNgoA+3mqtg=
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.17.2:
  version "1.17.7"
  resolved "https://registry.npm.taobao.org/es-abstract/download/es-abstract-1.17.7.tgz?cache=0&sync_timestamp=1610935617257&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fes-abstract%2Fdownload%2Fes-abstract-1.17.7.tgz#a4de61b2f66989fc7421676c1cb9787573ace54c"
  integrity sha1-pN5hsvZpifx0IWdsHLl4dXOs5Uw=
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.2"
    is-regex "^1.1.1"
    object-inspect "^1.8.0"
    object-keys "^1.1.1"
    object.assign "^4.1.1"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-abstract@^1.18.0-next.1:
  version "1.18.0-next.2"
  resolved "https://registry.npm.taobao.org/es-abstract/download/es-abstract-1.18.0-next.2.tgz?cache=0&sync_timestamp=1610935617257&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fes-abstract%2Fdownload%2Fes-abstract-1.18.0-next.2.tgz#088101a55f0541f595e7e057199e27ddc8f3a5c2"
  integrity sha1-CIEBpV8FQfWV5+BXGZ4n3cjzpcI=
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.2"
    is-negative-zero "^2.0.1"
    is-regex "^1.1.1"
    object-inspect "^1.9.0"
    object-keys "^1.1.1"
    object.assign "^4.1.2"
    string.prototype.trimend "^1.0.3"
    string.prototype.trimstart "^1.0.3"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/escalade/download/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@1.0.5, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

eslint-loader@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/eslint-loader/download/eslint-loader-2.2.1.tgz#28b9c12da54057af0845e2a6112701a2f6bf8337"
  integrity sha1-KLnBLaVAV68IReKmEScBova/gzc=
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-plugin-vue@^7.1.0:
  version "7.5.0"
  resolved "https://registry.npm.taobao.org/eslint-plugin-vue/download/eslint-plugin-vue-7.5.0.tgz#cc6d983eb22781fa2440a7573cf39af439bb5725"
  integrity sha1-zG2YPrIngfokQKdXPPOa9Dm7VyU=
  dependencies:
    eslint-utils "^2.1.0"
    natural-compare "^1.4.0"
    semver "^7.3.2"
    vue-eslint-parser "^7.4.1"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/eslint-scope/download/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.0.0, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/eslint-utils/download/eslint-utils-2.1.0.tgz?cache=0&sync_timestamp=1592843197443&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-utils%2Fdownload%2Feslint-utils-2.1.0.tgz#d2de5e03424e707dc10c74068ddedae708741b27"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1597435068105&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/eslint-visitor-keys/download/eslint-visitor-keys-2.0.0.tgz?cache=0&sync_timestamp=1597435068105&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-2.0.0.tgz#21fdc8fbcd9c795cc0321f0563702095751511a8"
  integrity sha1-If3I+82ceVzAMh8FY3AglXUVEag=

eslint@^7.12.1:
  version "7.18.0"
  resolved "https://registry.npm.taobao.org/eslint/download/eslint-7.18.0.tgz?cache=0&sync_timestamp=1610750192074&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feslint%2Fdownload%2Feslint-7.18.0.tgz#7fdcd2f3715a41fe6295a16234bd69aed2c75e67"
  integrity sha1-f9zS83FaQf5ilaFiNL1prtLHXmc=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@eslint/eslintrc" "^0.3.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.2.0"
    esutils "^2.0.2"
    file-entry-cache "^6.0.0"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash "^4.17.20"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.4"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.2.1:
  version "6.2.1"
  resolved "https://registry.npm.taobao.org/espree/download/espree-6.2.1.tgz?cache=0&sync_timestamp=1607144072939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fespree%2Fdownload%2Fespree-6.2.1.tgz#77fc72e1fd744a2052c20f38a5b575832e82734a"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "https://registry.npm.taobao.org/espree/download/espree-7.3.1.tgz?cache=0&sync_timestamp=1607144072939&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fespree%2Fdownload%2Fespree-7.3.1.tgz#f2df330b752c6f55019f8bd89b7660039c1bbbb6"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1, esquery@^1.2.0:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/esquery/download/esquery-1.3.1.tgz#b78b5828aa8e214e29fb74c4d5b752e1c033da57"
  integrity sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0, esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/estraverse/download/estraverse-5.2.0.tgz#307df42547e6cc7324d3cf03c155d5cdb8c53880"
  integrity sha1-MH30JUfmzHMk088DwVXVzbjFOIA=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/event-pubsub/download/event-pubsub-4.3.0.tgz?cache=0&sync_timestamp=1606361604403&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fevent-pubsub%2Fdownload%2Fevent-pubsub-4.3.0.tgz#f68d816bc29f1ec02c539dc58c8dd40ce72cb36e"
  integrity sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://registry.npm.taobao.org/eventemitter3/download/eventemitter3-4.0.7.tgz?cache=0&sync_timestamp=1598517819668&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Feventemitter3%2Fdownload%2Feventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/events/download/events-3.2.0.tgz#93b87c18f8efcd4202a461aec4dfc0556b639379"
  integrity sha1-k7h8GPjvzUICpGGuxN/AVWtjk3k=

eventsource@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npm.taobao.org/eventsource/download/eventsource-1.0.7.tgz#8fbc72c93fcd34088090bc0a4e64f4b5cee6d8d0"
  integrity sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA=
  dependencies:
    original "^1.0.0"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npm.taobao.org/execa/download/execa-0.8.0.tgz?cache=0&sync_timestamp=1606971018065&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-0.8.0.tgz#d8d76bbc1b55217ed190fd6dd49d3c774ecfc8da"
  integrity sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/execa/download/execa-1.0.0.tgz?cache=0&sync_timestamp=1606971018065&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^3.3.0:
  version "3.4.0"
  resolved "https://registry.npm.taobao.org/execa/download/execa-3.4.0.tgz?cache=0&sync_timestamp=1606971018065&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexeca%2Fdownload%2Fexeca-3.4.0.tgz#c08ed4550ef65d858fac269ffc8572446f37eb89"
  integrity sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execall@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/execall/download/execall-2.0.0.tgz#16a06b5fe5099df7d00be5d9c06eecded1663b45"
  integrity sha1-FqBrX+UJnffQC+XZwG7s3tFmO0U=
  dependencies:
    clone-regexp "^2.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

express-mock-restful@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/express-mock-restful/download/express-mock-restful-1.1.3.tgz#95ec62327f39d49914e7d015fc604d5c1deaab29"
  integrity sha1-lexiMn851JkU59AV/GBNXB3qqyk=
  dependencies:
    body-parser "^1.18.3"
    chalk "^2.4.1"
    debug "^3.1.0"
    path-to-regexp "^2.2.1"

express@^4.16.3, express@^4.17.1:
  version "4.17.1"
  resolved "https://registry.npm.taobao.org/express/download/express-4.17.1.tgz?cache=0&sync_timestamp=1596722127254&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz#4491fc38605cf51f8629d39c2b5d026f98a4c134"
  integrity sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.0, extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.2, extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"
  integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^2.2.6:
  version "2.2.7"
  resolved "https://registry.npm.taobao.org/fast-glob/download/fast-glob-2.2.7.tgz?cache=0&sync_timestamp=1610876590762&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-glob%2Fdownload%2Ffast-glob-2.2.7.tgz#6953857c3afa475fff92ee6015d52da70a4cd39d"
  integrity sha1-aVOFfDr6R1//ku5gFdUtpwpM050=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@^3.1.1, fast-glob@^3.2.5:
  version "3.2.5"
  resolved "https://registry.npm.taobao.org/fast-glob/download/fast-glob-3.2.5.tgz?cache=0&sync_timestamp=1610876590762&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-glob%2Fdownload%2Ffast-glob-3.2.5.tgz#7939af2a656de79a4f1901903ee8adcaa7cb9661"
  integrity sha1-eTmvKmVt55pPGQGQPuityqfLlmE=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.0"
    merge2 "^1.3.0"
    micromatch "^4.0.2"
    picomatch "^2.2.1"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz?cache=0&sync_timestamp=1605292839055&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-levenshtein%2Fdownload%2Ffast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastest-levenshtein@^1.0.12:
  version "1.0.12"
  resolved "https://registry.npm.taobao.org/fastest-levenshtein/download/fastest-levenshtein-1.0.12.tgz#9990f7d3a88cc5a9ffd1f1745745251700d497e2"
  integrity sha1-mZD306iMxan/0fF0V0UlFwDUl+I=

fastq@^1.6.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/fastq/download/fastq-1.10.0.tgz?cache=0&sync_timestamp=1608462962338&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffastq%2Fdownload%2Ffastq-1.10.0.tgz#74dbefccade964932cdf500473ef302719c652bb"
  integrity sha1-dNvvzK3pZJMs31AEc+8wJxnGUrs=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.3"
  resolved "https://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.11.3.tgz#5c0e9a8968e8912c286639fde977a8b209f2508e"
  integrity sha1-XA6aiWjokSwoZjn96XeosgnyUI4=
  dependencies:
    websocket-driver ">=0.5.1"

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "https://registry.npm.taobao.org/figgy-pudding/download/figgy-pudding-3.5.2.tgz#b4eee8148abb01dcf1d1ac34367d59e12fa61d6e"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-6.0.0.tgz?cache=0&sync_timestamp=1604832308218&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffile-entry-cache%2Fdownload%2Ffile-entry-cache-6.0.0.tgz#7921a89c391c6d93efec2169ac6bf300c527ea0a"
  integrity sha1-eSGonDkcbZPv7CFprGvzAMUn6go=
  dependencies:
    flat-cache "^3.0.4"

file-loader@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/file-loader/download/file-loader-4.3.0.tgz#780f040f729b3d18019f20605f723e844b8a58af"
  integrity sha1-eA8ED3KbPRgBnyBgX3I+hEuKWK8=
  dependencies:
    loader-utils "^1.2.3"
    schema-utils "^2.5.0"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filesize@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npm.taobao.org/filesize/download/filesize-3.6.1.tgz#090bb3ee01b6f801a8a8be99d31710b3422bb317"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npm.taobao.org/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/finalhandler/download/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-0.1.1.tgz#c8defae57c8a52a8a784f9e31c57c742e993a0b9"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-cache-dir@^3.0.0, find-cache-dir@^3.3.1:
  version "3.3.1"
  resolved "https://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-3.3.1.tgz#89b33fad4a4670daa94f855f7fbe31d6d84fe880"
  integrity sha1-ibM/rUpGcNqpT4Vff74x1thP6IA=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

first-chunk-stream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/first-chunk-stream/download/first-chunk-stream-1.0.0.tgz#59bfb50cd905f60d7c394cd3d9acaab4e6ad934e"
  integrity sha1-Wb+1DNkF9g18OUzT2ayqtOatk04=

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/flat-cache/download/flat-cache-3.0.4.tgz#61b0338302b2fe9f957dcc32fc2a87f1c3048b11"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/flatted/download/flatted-3.1.1.tgz?cache=0&sync_timestamp=1611059459175&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fflatted%2Fdownload%2Fflatted-3.1.1.tgz#c4b489e80096d9df1dfc97c79871aea7c617c469"
  integrity sha1-xLSJ6ACW2d8d/JfHmHGup8YXxGk=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

follow-redirects@^1.0.0, follow-redirects@^1.10.0:
  version "1.13.2"
  resolved "https://registry.npm.taobao.org/follow-redirects/download/follow-redirects-1.13.2.tgz?cache=0&sync_timestamp=1611606737937&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.13.2.tgz#dd73c8effc12728ba5cf4259d760ea5fb83e3147"
  integrity sha1-3XPI7/wScoulz0JZ12DqX7g+MUc=

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/forwarded/download/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npm.taobao.org/fs-extra/download/fs-extra-7.0.1.tgz?cache=0&sync_timestamp=1611075495956&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffs-extra%2Fdownload%2Ffs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/fs-minipass/download/fs-minipass-2.1.0.tgz#7f5036fdbf12c63c169190cbe4199c852271f9fb"
  integrity sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=
  dependencies:
    minipass "^3.0.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "https://registry.npm.taobao.org/fsevents/download/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.3.1:
  version "2.3.1"
  resolved "https://registry.npm.taobao.org/fsevents/download/fsevents-2.3.1.tgz#b209ab14c61012636c8863507edf7fb68cc54e9f"
  integrity sha1-sgmrFMYQEmNsiGNQft9/tozFTp8=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gaze@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/gaze/download/gaze-1.1.3.tgz#c441733e13b927ac8c0ff0b4c3b033f28812924a"
  integrity sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko=
  dependencies:
    globule "^1.0.0"

gensync@^1.0.0-beta.1:
  version "1.0.0-beta.2"
  resolved "https://registry.npm.taobao.org/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/get-intrinsic/download/get-intrinsic-1.1.0.tgz#892e62931e6938c8a23ea5aaebcfb67bd97da97e"
  integrity sha1-iS5ikx5pOMiiPqWq68+2e9l9qX4=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-pixels@~3.3.0:
  version "3.3.2"
  resolved "https://registry.npm.taobao.org/get-pixels/download/get-pixels-3.3.2.tgz#3f62fb8811932c69f262bba07cba72b692b4ff03"
  integrity sha1-P2L7iBGTLGnyYrugfLpytpK0/wM=
  dependencies:
    data-uri-to-buffer "0.0.3"
    jpeg-js "^0.3.2"
    mime-types "^2.0.1"
    ndarray "^1.0.13"
    ndarray-pack "^1.1.1"
    node-bitmap "0.0.1"
    omggif "^1.0.5"
    parse-data-uri "^0.2.0"
    pngjs "^3.3.3"
    request "^2.44.0"
    through "^2.3.4"

get-stdin@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npm.taobao.org/get-stdin/download/get-stdin-8.0.0.tgz#cbad6a73feb75f6eeb22ba9e01f89aa28aa97a53"
  integrity sha1-y61qc/63X27rIrqeAfiaooqpelM=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz?cache=0&sync_timestamp=1597056544847&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stream%2Fdownload%2Fget-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-4.1.0.tgz?cache=0&sync_timestamp=1597056544847&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stream%2Fdownload%2Fget-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-5.2.0.tgz?cache=0&sync_timestamp=1597056544847&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-stream%2Fdownload%2Fget-stream-5.2.0.tgz#4966a1795ee5ace65e706c4b7beb71257d6e22d3"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

gif-encoder@~0.4.1:
  version "0.4.3"
  resolved "https://registry.npm.taobao.org/gif-encoder/download/gif-encoder-0.4.3.tgz#8a2b4fe8ca895a48e3a0b6cbb340a0a6a3571899"
  integrity sha1-iitP6MqJWkjjoLbLs0CgpqNXGJk=
  dependencies:
    readable-stream "~1.1.9"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0, glob-parent@^5.1.0, glob-parent@~5.1.0:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/glob-parent/download/glob-parent-5.1.1.tgz#b6c1ef417c4e5663ea498f1c45afac6916bbc229"
  integrity sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz#8c5a1494d2066c570cc3bfe4496175acc4d502ab"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@^7.0.3, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6, glob@~7.1.1:
  version "7.1.6"
  resolved "https://registry.npm.taobao.org/glob/download/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/global-modules/download/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/global-prefix/download/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npm.taobao.org/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1608438901537&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "https://registry.npm.taobao.org/globals/download/globals-12.4.0.tgz?cache=0&sync_timestamp=1608438901537&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobals%2Fdownload%2Fglobals-12.4.0.tgz#a18813576a41b00a24a97e7f815918c2e19925f8"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globby@^11.0.2:
  version "11.0.2"
  resolved "https://registry.npm.taobao.org/globby/download/globby-11.0.2.tgz#1af538b766a3b540ebfb58a32b2e2d5897321d83"
  integrity sha1-GvU4t2ajtUDr+1ijKy4tWJcyHYM=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.1.1"
    ignore "^5.1.4"
    merge2 "^1.3.0"
    slash "^3.0.0"

globby@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npm.taobao.org/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npm.taobao.org/globby/download/globby-7.1.1.tgz#fb2ccff9401f8600945dfada97440cca972b8680"
  integrity sha1-+yzP+UAfhgCUXfral0QMypcrhoA=
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "https://registry.npm.taobao.org/globby/download/globby-9.2.0.tgz#fd029a706c703d29bdd170f4b6db3a3f7a7cb63d"
  integrity sha1-/QKacGxwPSm90XD0tts6P3p8tj0=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/globjoin/download/globjoin-0.1.4.tgz#2f4494ac8919e3767c5cbb691e9f463324285d43"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

globule@^1.0.0:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/globule/download/globule-1.3.2.tgz#d8bdd9e9e4eef8f96e245999a5dee7eb5d8529c4"
  integrity sha1-2L3Z6eTu+PluJFmZpd7n612FKcQ=
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.10"
    minimatch "~3.0.2"

gonzales-pe@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/gonzales-pe/download/gonzales-pe-4.3.0.tgz#fe9dec5f3c557eead09ff868c65826be54d067b3"
  integrity sha1-/p3sXzxVfurQn/hoxlgmvlTQZ7M=
  dependencies:
    minimist "^1.2.5"

good-listener@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/good-listener/download/good-listener-1.2.2.tgz#d53b30cdf9313dffb7dc9a0d477096aa6d145c50"
  integrity sha1-1TswzfkxPf+33JoNR3CWqm0UXFA=
  dependencies:
    delegate "^3.1.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.2:
  version "4.2.4"
  resolved "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.2.4.tgz#2256bde14d3632958c465ebc96dc467ca07a29fb"
  integrity sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/gzip-size/download/gzip-size-5.1.1.tgz?cache=0&sync_timestamp=1605523125680&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fgzip-size%2Fdownload%2Fgzip-size-5.1.1.tgz#cb9bee692f87c0612b232840a873904e4c135274"
  integrity sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/handle-thing/download/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

handlebars-layouts@^3.1.4:
  version "3.1.4"
  resolved "https://registry.npm.taobao.org/handlebars-layouts/download/handlebars-layouts-3.1.4.tgz#26b3beb931b4b877dfbf7e6feaf4058ee6228b02"
  integrity sha1-JrO+uTG0uHffv35v6vQFjuYiiwI=

handlebars@^4.6.0:
  version "4.7.6"
  resolved "https://registry.npm.taobao.org/handlebars/download/handlebars-4.7.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhandlebars%2Fdownload%2Fhandlebars-4.7.6.tgz#d4c05c1baf90e9945f77aa68a7a219aa4a7df74e"
  integrity sha1-1MBcG6+Q6ZRfd6pop6IZqkp9904=
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.0"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://registry.npm.taobao.org/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/hard-rejection/download/hard-rejection-2.1.0.tgz#1c6eda5c1685c63942766d79bb40ae773cecd883"
  integrity sha1-HG7aXBaFxjlCdm15u0Cudzzs2IM=

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/has-flag/download/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.1.tgz#9f5214758a44196c406d9bd76cebf81ec2dd31e8"
  integrity sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npm.taobao.org/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/hash-base/download/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz#33b40777754c6432573c120cc3808bbd10d47f04"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/hash-sum/download/hash-sum-2.0.0.tgz#81d01bb5de8ea4a214ad5d6ead1b523460b0b45a"
  integrity sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://registry.npm.taobao.org/hash.js/download/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@1.2.x, he@^1.1.0, he@^1.1.1, he@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/he/download/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/hex-color-regex/download/hex-color-regex-1.1.0.tgz#4c06fccb4602fe2602b3c93df82d7e7dbf1a8a8e"
  integrity sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=

highlight.js-async-webpack@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/highlight.js-async-webpack/download/highlight.js-async-webpack-1.0.4.tgz#c06b67bf99f049045d62b756e5855b0912ec616c"
  integrity sha1-wGtnv5nwSQRdYrdW5YVbCRLsYWw=

highlight.js@^10.0.0:
  version "10.5.0"
  resolved "https://registry.npm.taobao.org/highlight.js/download/highlight.js-10.5.0.tgz#3f09fede6a865757378f2d9ebdcbc15ba268f98f"
  integrity sha1-Pwn+3mqGV1c3jy2evcvBW6Jo+Y8=

highlight.js@^9.11.0:
  version "9.18.5"
  resolved "https://registry.npm.taobao.org/highlight.js/download/highlight.js-9.18.5.tgz#d18a359867f378c138d6819edfc2a8acd5f29825"
  integrity sha1-0Yo1mGfzeME41oGe38KorNXymCU=

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoopy@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/hoopy/download/hoopy-0.1.4.tgz#609207d661100033a9a9402ad3dea677381c1b1d"
  integrity sha1-YJIH1mEQADOpqUAq096mdzgcGx0=

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "https://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.8.8.tgz?cache=0&sync_timestamp=1602803832496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhosted-git-info%2Fdownload%2Fhosted-git-info-2.8.8.tgz#7539bd4bc1e0e0a895815a2e0262420b12858488"
  integrity sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=

hosted-git-info@^3.0.6:
  version "3.0.7"
  resolved "https://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-3.0.7.tgz?cache=0&sync_timestamp=1602803832496&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhosted-git-info%2Fdownload%2Fhosted-git-info-3.0.7.tgz#a30727385ea85acfcee94e0aad9e368c792e036c"
  integrity sha1-owcnOF6oWs/O6U4KrZ42jHkuA2w=
  dependencies:
    lru-cache "^6.0.0"

hotkeys-js@^3.8.1:
  version "3.8.2"
  resolved "https://registry.npm.taobao.org/hotkeys-js/download/hotkeys-js-3.8.2.tgz#177c3e7dc19279c881e69983177849bed76e4747"
  integrity sha1-F3w+fcGSeciB5pmDF3hJvtduR0c=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npm.taobao.org/hpack.js/download/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/hsl-regex/download/hsl-regex-1.0.0.tgz#d49330c789ed819e276a4c0d272dffa30b18fe6e"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/hsla-regex/download/hsla-regex-1.0.0.tgz#c1ce7a3168c8c6614033a4b5f7877f3b225f9c38"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-comment-regex@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/html-comment-regex/download/html-comment-regex-1.1.2.tgz#97d4688aeb5c81886a364faa0cad1dda14d433a7"
  integrity sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c=

html-entities@^1.3.1:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/html-entities/download/html-entities-1.4.0.tgz?cache=0&sync_timestamp=1611574043246&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-entities%2Fdownload%2Fhtml-entities-1.4.0.tgz#cfbd1b01d2afaf9adca1b10ae7dffab98c71d2dc"
  integrity sha1-z70bAdKvr5rcobEK59/6uYxx0tw=

html-minifier-terser@^5.0.1:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/html-minifier-terser/download/html-minifier-terser-5.1.1.tgz#922e96f1f3bb60832c2634b79884096389b1f054"
  integrity sha1-ki6W8fO7YIMsJjS3mIQJY4mx8FQ=
  dependencies:
    camel-case "^4.1.1"
    clean-css "^4.2.3"
    commander "^4.1.1"
    he "^1.2.0"
    param-case "^3.0.3"
    relateurl "^0.2.7"
    terser "^4.6.3"

html-minifier@^3.2.3:
  version "3.5.21"
  resolved "https://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.21.tgz#d0040e054730e354db008463593194015212d20c"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/html-tags/download/html-tags-2.0.0.tgz#10b30a386085f43cede353cc8fa7cb0deeea668b"
  integrity sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=

html-tags@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/html-tags/download/html-tags-3.1.0.tgz#7b5e6f7e665e9fb41f30007ed9e0d41e97fb2140"
  integrity sha1-e15vfmZen7QfMAB+2eDUHpf7IUA=

html-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz?cache=0&sync_timestamp=1611174522750&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-webpack-plugin%2Fdownload%2Fhtml-webpack-plugin-3.2.0.tgz#b01abbd723acaaa7b37b6af4492ebda03d9dd37b"
  integrity sha1-sBq71yOsqqeze2r0SS69oD2d03s=
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

html-webpack-plugin@^4.5.0:
  version "4.5.1"
  resolved "https://registry.npm.taobao.org/html-webpack-plugin/download/html-webpack-plugin-4.5.1.tgz?cache=0&sync_timestamp=1611174522750&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-webpack-plugin%2Fdownload%2Fhtml-webpack-plugin-4.5.1.tgz#40aaf1b5cb78f2f23a83333999625c20929cda65"
  integrity sha1-QKrxtct48vI6gzM5mWJcIJKc2mU=
  dependencies:
    "@types/html-minifier-terser" "^5.0.0"
    "@types/tapable" "^1.0.5"
    "@types/webpack" "^4.41.8"
    html-minifier-terser "^5.0.1"
    loader-utils "^1.2.3"
    lodash "^4.17.20"
    pretty-error "^2.1.1"
    tapable "^1.1.3"
    util.promisify "1.0.0"

htmlparser2@^3.10.0, htmlparser2@^3.10.1, htmlparser2@^3.8.3:
  version "3.10.1"
  resolved "https://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.10.1.tgz?cache=0&sync_timestamp=1607394323827&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-3.10.1.tgz#bd679dc3f59897b6a34bb10749c855bb53a9392f"
  integrity sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=
  dependencies:
    domelementtype "^1.3.1"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^3.1.1"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npm.taobao.org/http-deceiver/download/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@1.7.2:
  version "1.7.2"
  resolved "https://registry.npm.taobao.org/http-errors/download/http-errors-1.7.2.tgz#4f5029cf13239f31036e5b2e55292bcfbcc85c8f"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://registry.npm.taobao.org/http-errors/download/http-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-parser-js@>=0.5.1:
  version "0.5.3"
  resolved "https://registry.npm.taobao.org/http-parser-js/download/http-parser-js-0.5.3.tgz?cache=0&sync_timestamp=1609542336109&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-parser-js%2Fdownload%2Fhttp-parser-js-0.5.3.tgz#01d2709c79d41698bb01d4decc5e9da4e4a033d9"
  integrity sha1-AdJwnHnUFpi7AdTezF6dpOSgM9k=

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "https://registry.npm.taobao.org/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz?cache=0&sync_timestamp=1602445480546&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-proxy-middleware%2Fdownload%2Fhttp-proxy-middleware-0.19.1.tgz#183c7dc4aa1479150306498c210cdaf96080a43a"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0:
  version "1.18.1"
  resolved "https://registry.npm.taobao.org/http-proxy/download/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1600868441269&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/https-browserify/download/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^4.0.0, icss-utils@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/icss-utils/download/icss-utils-4.1.1.tgz?cache=0&sync_timestamp=1605801375650&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ficss-utils%2Fdownload%2Ficss-utils-4.1.1.tgz#21170b53789ee27447c2f47dd683081403f9a467"
  integrity sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc=
  dependencies:
    postcss "^7.0.14"

ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/ieee754/download/ieee754-1.2.1.tgz?cache=0&sync_timestamp=1603838418666&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fieee754%2Fdownload%2Fieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^3.3.5:
  version "3.3.10"
  resolved "https://registry.npm.taobao.org/ignore/download/ignore-3.3.10.tgz#0a97fb876986e8081c631160f8f9f389157f0043"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.3, ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npm.taobao.org/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.1.4, ignore@^5.1.8:
  version "5.1.8"
  resolved "https://registry.npm.taobao.org/ignore/download/ignore-5.1.8.tgz#f150a8b50a34289b33e22f5889abd4d8016f0e57"
  integrity sha1-8VCotQo0KJsz4i9YiavU2AFvDlc=

image-size@^0.5.1:
  version "0.5.5"
  resolved "https://registry.npm.taobao.org/image-size/download/image-size-0.5.5.tgz?cache=0&sync_timestamp=1603731285656&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimage-size%2Fdownload%2Fimage-size-0.5.5.tgz#09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/import-cwd/download/import-cwd-2.1.0.tgz#aa6cf36e722761285cb371ec6519f53e2435b0a9"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/import-fresh/download/import-fresh-2.0.0.tgz?cache=0&sync_timestamp=1608469472392&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npm.taobao.org/import-fresh/download/import-fresh-3.3.0.tgz?cache=0&sync_timestamp=1608469472392&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/import-from/download/import-from-2.1.0.tgz#335db7f2a7affd53aaa471d4b8021dee36b7f3b1"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-lazy@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/import-lazy/download/import-lazy-4.0.0.tgz#e8eb627483a0a43da3c03f3e35548be5cb0cc153"
  integrity sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=

import-local@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/import-local/download/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz#f30f716c8e2bd346c7b67d3df3915566a7c05607"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

infer-owner@^1.0.3, infer-owner@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/infer-owner/download/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/inherits/download/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.5:
  version "1.3.8"
  resolved "https://registry.npm.taobao.org/ini/download/ini-1.3.8.tgz?cache=0&sync_timestamp=1607907801722&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fini%2Fdownload%2Fini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inquirer@^7.1.0, inquirer@^7.3.2:
  version "7.3.3"
  resolved "https://registry.npm.taobao.org/inquirer/download/inquirer-7.3.3.tgz?cache=0&sync_timestamp=1595475980671&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finquirer%2Fdownload%2Finquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/internal-ip/download/internal-ip-4.3.0.tgz?cache=0&sync_timestamp=1605885556992&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finternal-ip%2Fdownload%2Finternal-ip-4.3.0.tgz#845452baad9d2ca3b69c635a137acb9a0dad0907"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

intersperse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/intersperse/-/intersperse-1.0.0.tgz#f2561fb1cfef9f5277cc3347a22886b4351a5181"
  integrity sha1-8lYfsc/vn1J3zDNHoiiGtDUaUYE=

iota-array@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/iota-array/download/iota-array-1.0.0.tgz#81ef57fe5d05814cd58c2483632a99c30a0e8087"
  integrity sha1-ge9X/l0FgUzVjCSDYyqZwwoOgIc=

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/ip-regex/download/ip-regex-2.1.0.tgz#fa78bf5d2e6913c911ce9f819ee5146bb6d844e9"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npm.taobao.org/ip/download/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@1.9.1, ipaddr.js@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

irregular-plurals@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/irregular-plurals/download/irregular-plurals-1.4.0.tgz#2ca9b033651111855412f16be5d77c62a458a766"
  integrity sha1-LKmwM2UREYVUEvFr5dd8YqRYp2Y=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz#50530dfb84fcc9aa7dbe7852e83a37b93b9f2aa6"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-3.0.3.tgz#96c6a22b6a23929b11ea0afb1836c36ad4a5d698"
  integrity sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-alphabetical@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/is-alphabetical/download/is-alphabetical-1.0.4.tgz#9e7d6b94916be22153745d184c298cbf986a686d"
  integrity sha1-nn1rlJFr4iFTdF0YTCmMv5hqaG0=

is-alphanumerical@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/is-alphanumerical/download/is-alphanumerical-1.0.4.tgz#7eb9a2431f855f6b1ef1a78e326df515696c4dbf"
  integrity sha1-frmiQx+FX2se8aeOMm31FWlsTb8=
  dependencies:
    is-alphabetical "^1.0.0"
    is-decimal "^1.0.0"

is-arguments@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-arguments/download/is-arguments-1.1.0.tgz#62353031dfbee07ceb34656a6bde59efecae8dd9"
  integrity sha1-YjUwMd++4HzrNGVqa95Z7+yujdk=
  dependencies:
    call-bind "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.0.2, is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz?cache=0&sync_timestamp=1604432378894&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-buffer%2Fdownload%2Fis-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/is-buffer/download/is-buffer-2.0.5.tgz?cache=0&sync_timestamp=1604432378894&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-buffer%2Fdownload%2Fis-buffer-2.0.5.tgz#ebc252e400d22ff8d77fa09888821a24a658c191"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.1.4, is-callable@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/is-callable/download/is-callable-1.2.2.tgz#c7c6715cd22d4ddb48d3e19970223aceabb080d9"
  integrity sha1-x8ZxXNItTdtI0+GZcCI6zquwgNk=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/is-ci/download/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-color-stop/download/is-color-stop-1.1.0.tgz#cfff471aee4dd5c9e158598fbe12967b5cdad345"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-core-module@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/is-core-module/download/is-core-module-2.2.0.tgz?cache=0&sync_timestamp=1606411588663&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-core-module%2Fdownload%2Fis-core-module-2.2.0.tgz#97037ef3d52224d85163f5597b2b63d9afed981a"
  integrity sha1-lwN+89UiJNhRY/VZeytj2a/tmBo=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-date-object/download/is-date-object-1.0.2.tgz#bda736f2cd8fd06d32844e7743bfa7494c3bfd7e"
  integrity sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=

is-decimal@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/is-decimal/download/is-decimal-1.0.4.tgz#65a3a5958a1c5b63a706e1b333d7cd9f630d3fa5"
  integrity sha1-ZaOllYocW2OnBuGzM9fNn2MNP6U=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/is-docker/download/is-docker-2.1.1.tgz?cache=0&sync_timestamp=1596559460885&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-docker%2Fdownload%2Fis-docker-2.1.1.tgz#4125a88e44e450d384e09047ede71adc2d144156"
  integrity sha1-QSWojkTkUNOE4JBH7eca3C0UQVY=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/is-glob/download/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/is-hexadecimal/download/is-hexadecimal-1.0.4.tgz#cc35c97588da4bd49a8eedd6bc4082d44dcb23a7"
  integrity sha1-zDXJdYjaS9Saju3WvECC1E3LI6c=

is-mobile@^2.2.1:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/is-mobile/-/is-mobile-2.2.2.tgz#f6c9c5d50ee01254ce05e739bdd835f1ed4e9954"
  integrity sha512-wW/SXnYJkTjs++tVK5b6kVITZpAZPtUrt9SF80vvxGiF/Oywal+COk1jlRkiVq15RFNEQKQY31TkV24/1T5cVg==

is-negative-zero@^2.0.0, is-negative-zero@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/is-negative-zero/-/is-negative-zero-2.0.1.tgz#3de746c18dda2319241a53675908d8f766f11c24"
  integrity sha512-2z6JzQvZRa9A2Y7xC6dQQm4FSTSTNWjKIYYTt4246eMTJmIo0Q+ZyOsU66X8lxK1AbB92dFeglPLrhwpeRKO6w==

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-obj/download/is-obj-2.0.0.tgz#473fb05d973705e3fd9620545018ca8e22ef4982"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz#bfe2dca26c69f397265a4009963602935a053acb"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-path-inside/download/is-path-inside-2.1.0.tgz#7c9810587d659a40d27bcdb4d5616eab059494b2"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@^1.0.0, is-plain-obj@^1.1, is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-obj@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-2.1.0.tgz#45e42e37fccf1f40da8e5f76ee21515840c09287"
  integrity sha1-ReQuN/zPH0Dajl927iFRWEDAkoc=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz?cache=0&sync_timestamp=1599667273698&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-plain-object%2Fdownload%2Fis-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-regex@^1.0.4, is-regex@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/is-regex/download/is-regex-1.1.1.tgz#c6f98aacc546f6cec5468a07b7b153ab564a57b9"
  integrity sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k=
  dependencies:
    has-symbols "^1.0.1"

is-regexp@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-regexp/download/is-regexp-2.1.0.tgz#cd734a56864e23b956bf4e7c66c396a4c0b22c2d"
  integrity sha1-zXNKVoZOI7lWv058ZsOWpMCyLC0=

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.1.0.tgz#fb18f87ce1feb925169c9a407c19318a3206ed88"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-stream/download/is-stream-2.0.0.tgz#bde9c32680d6fae04129d6ac9d921ce7815f78e3"
  integrity sha1-venDJoDW+uBBKdasnZIc54FfeOM=

is-svg@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-svg/download/is-svg-3.0.0.tgz#9321dbd29c212e5ca99c4fa9794c714bcafa2f75"
  integrity sha1-kyHb0pwhLlypnE+peUxxS8r6L3U=
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/is-symbol/download/is-symbol-1.0.3.tgz#38e1014b9e6329be0de9d24a414fd7441ec61937"
  integrity sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=
  dependencies:
    has-symbols "^1.0.1"

is-typedarray@^1.0.0, is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/is-wsl/download/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

ismobilejs@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/ismobilejs/-/ismobilejs-1.1.1.tgz#c56ca0ae8e52b24ca0f22ba5ef3215a2ddbbaa0e"
  integrity sha512-VaFW53yt8QO61k2WJui0dHf4SlL8lxBofUuUmwBo0ljPk0Drz2TiuDW4jo3wDcv41qy/SxrJ+VAzJ/qYqsmzRw==

isobject@^2.0.0, isobject@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

javascript-stringify@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/javascript-stringify/download/javascript-stringify-2.0.1.tgz#6ef358035310e35d667c675ed63d3eb7c1aa19e5"
  integrity sha1-bvNYA1MQ411mfGde1j0+t8GqGeU=

jest-worker@^25.4.0:
  version "25.5.0"
  resolved "https://registry.npm.taobao.org/jest-worker/download/jest-worker-25.5.0.tgz#2611d071b79cea0f43ee57a3d118593ac1547db1"
  integrity sha1-JhHQcbec6g9D7lej0RhZOsFUfbE=
  dependencies:
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jpeg-js@0.0.4:
  version "0.0.4"
  resolved "https://registry.npm.taobao.org/jpeg-js/download/jpeg-js-0.0.4.tgz#06aaf47efec7af0b1924a59cd695a6d2b5ed870e"
  integrity sha1-Bqr0fv7HrwsZJKWc1pWm0rXthw4=

jpeg-js@^0.3.2:
  version "0.3.7"
  resolved "https://registry.npm.taobao.org/jpeg-js/download/jpeg-js-0.3.7.tgz#471a89d06011640592d314158608690172b1028d"
  integrity sha1-RxqJ0GARZAWS0xQVhghpAXKxAo0=

js-base64@^2.1.9:
  version "2.6.4"
  resolved "https://registry.npm.taobao.org/js-base64/download/js-base64-2.6.4.tgz?cache=0&sync_timestamp=1604450395858&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-base64%2Fdownload%2Fjs-base64-2.6.4.tgz#f4e686c5de1ea1f867dbcad3d46d969428df98c4"
  integrity sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=

js-message@1.0.7:
  version "1.0.7"
  resolved "https://registry.npm.taobao.org/js-message/download/js-message-1.0.7.tgz?cache=0&sync_timestamp=1605128905632&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-message%2Fdownload%2Fjs-message-1.0.7.tgz#fbddd053c7a47021871bb8b2c95397cc17c20e47"
  integrity sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc=

js-queue@2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/js-queue/download/js-queue-2.0.2.tgz#0be590338f903b36c73d33c31883a821412cd482"
  integrity sha1-C+WQM4+QOzbHPTPDGIOoIUEs1II=
  dependencies:
    easy-stack "^1.0.1"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/js-tokens/download/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.14.1.tgz?cache=0&sync_timestamp=1609680109116&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-yaml%2Fdownload%2Fjs-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npm.taobao.org/jsesc/download/jsesc-2.5.2.tgz?cache=0&sync_timestamp=1603891161295&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz?cache=0&sync_timestamp=1603891161295&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-content-demux@~0.1.2:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/json-content-demux/download/json-content-demux-0.1.4.tgz#6d573fbb5a2b224ae225736e287f32ea91a4273d"
  integrity sha1-bVc/u1orIkriJXNuKH8y6pGkJz0=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npm.taobao.org/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz?cache=0&sync_timestamp=1609553686459&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson-schema%2Fdownload%2Fjson-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json2mq@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/json2mq/-/json2mq-0.2.0.tgz#b637bd3ba9eabe122c83e9720483aeb10d2c904a"
  integrity sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=
  dependencies:
    string-convert "^0.2.0"

json3@^3.3.3:
  version "3.3.3"
  resolved "https://registry.npm.taobao.org/json3/download/json3-3.3.3.tgz#7fc10e375fc5ae42c4705a5cc0aa6f62be305b81"
  integrity sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=

json5@^0.5.0:
  version "0.5.1"
  resolved "https://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/json5/download/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.1.3"
  resolved "https://registry.npm.taobao.org/json5/download/json5-2.1.3.tgz#c9b0f7fa9233bfe5807fe66fcf3a5617ed597d43"
  integrity sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=
  dependencies:
    minimist "^1.2.5"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/jsonfile/download/jsonfile-4.0.0.tgz?cache=0&sync_timestamp=1604161844511&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsonfile%2Fdownload%2Fjsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

killable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/killable/download/killable-1.0.1.tgz#4c8ce441187a061c7474fb87ca08e2a638194892"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0, kind-of@^5.0.2:
  version "5.1.0"
  resolved "https://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npm.taobao.org/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klona@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/klona/download/klona-2.0.4.tgz?cache=0&sync_timestamp=1600226636423&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fklona%2Fdownload%2Fklona-2.0.4.tgz#7bb1e3affb0cb8624547ef7e8f6708ea2e39dfc0"
  integrity sha1-e7Hjr/sMuGJFR+9+j2cI6i4538A=

known-css-properties@^0.20.0:
  version "0.20.0"
  resolved "https://registry.npm.taobao.org/known-css-properties/download/known-css-properties-0.20.0.tgz?cache=0&sync_timestamp=1600340246198&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fknown-css-properties%2Fdownload%2Fknown-css-properties-0.20.0.tgz#0570831661b47dd835293218381166090ff60e96"
  integrity sha1-BXCDFmG0fdg1KTIYOBFmCQ/2DpY=

launch-editor-middleware@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz#e14b07e6c7154b0a4b86a0fd345784e45804c157"
  integrity sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc=
  dependencies:
    launch-editor "^2.2.1"

launch-editor@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/launch-editor/download/launch-editor-2.2.1.tgz#871b5a3ee39d6680fcc26d37930b6eeda89db0ca"
  integrity sha1-hxtaPuOdZoD8wm03kwtu7aidsMo=
  dependencies:
    chalk "^2.3.0"
    shell-quote "^1.6.1"

layout@~2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/layout/download/layout-2.2.0.tgz#31e44bfc18dd1019b3ffb208e402a4bbfe2e6af4"
  integrity sha1-MeRL/BjdEBmz/7II5AKku/4uavQ=
  dependencies:
    bin-pack "~1.0.1"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npm.taobao.org/lines-and-columns/download/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz#f08657646d607078be2f0a032f8bd69dd6f277d9"
  integrity sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.3.1, loader-runner@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npm.taobao.org/loader-runner/download/loader-runner-2.4.0.tgz?cache=0&sync_timestamp=1610027852811&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Floader-runner%2Fdownload%2Floader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3, loader-utils@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/loader-utils/download/loader-utils-1.4.0.tgz#c579b5e34cb34b1a74edc6c1fb36bfa371d5a613"
  integrity sha1-xXm140yzSxp07cbB+za/o3HVphM=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/loader-utils/download/loader-utils-2.0.0.tgz#e4cace5b816d425a166b5f097e10cd12b36064b0"
  integrity sha1-5MrOW4FtQloWa18JfhDNErNgZLA=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.defaultsdeep@^4.6.1:
  version "4.6.1"
  resolved "https://registry.npm.taobao.org/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz#512e9bd721d272d94e3d3a63653fa17516741ca6"
  integrity sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY=

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz#8489b1cb0d29ff88195cceca448ff6d6cc295c36"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npm.taobao.org/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz#1bafa5005de9dd6f4f26668c30ca37230cc9689c"
  integrity sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npm.taobao.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "https://registry.yarnpkg.com/lodash.toarray/-/lodash.toarray-4.4.0.tgz#24c4bfcd6b2fba38bfd0594db1179d8e9b656561"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash.transform@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npm.taobao.org/lodash.transform/download/lodash.transform-4.6.0.tgz#12306422f63324aed8483d3f38332b5f670547a0"
  integrity sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.3, lodash@~4.17.10:
  version "4.17.20"
  resolved "https://registry.npm.taobao.org/lodash/download/lodash-4.17.20.tgz?cache=0&sync_timestamp=1597336125847&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flodash%2Fdownload%2Flodash-4.17.20.tgz#b44a9b6297bcb698f1c51a3545a2b3b368d59c52"
  integrity sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=

lodash@^4.17.5:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^2.1.0, log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-symbols@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/log-symbols/download/log-symbols-4.0.0.tgz#69b3cc46d20f448eccdb75ea1fa733d9e821c920"
  integrity sha1-abPMRtIPRI7M23XqH6cz2eghySA=
  dependencies:
    chalk "^4.0.0"

loglevel@^1.6.8:
  version "1.7.1"
  resolved "https://registry.npm.taobao.org/loglevel/download/loglevel-1.7.1.tgz?cache=0&sync_timestamp=1606314029553&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Floglevel%2Fdownload%2Floglevel-1.7.1.tgz#005fde2f5e6e47068f935ff28573e125ef72f197"
  integrity sha1-AF/eL15uRwaPk1/yhXPhJe9y8Zc=

longest-streak@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/longest-streak/download/longest-streak-2.0.4.tgz#b8599957da5b5dab64dee3fe316fa774597d90e4"
  integrity sha1-uFmZV9pbXatk3uP+MW+ndFl9kOQ=

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz?cache=0&sync_timestamp=1606867292121&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flower-case%2Fdownload%2Flower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/lower-case/download/lower-case-2.0.2.tgz?cache=0&sync_timestamp=1606867292121&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flower-case%2Fdownload%2Flower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^4.0.1, lru-cache@^4.1.2:
  version "4.1.5"
  resolved "https://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

make-dir@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/map-obj/download/map-obj-4.1.0.tgz#b91221b542734b9f14256c0132c897c5d7256fd5"
  integrity sha1-uRIhtUJzS58UJWwBMsiXxdclb9U=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npm.taobao.org/mathml-tag-names/download/mathml-tag-names-2.1.3.tgz#4ddadd67308e780cf16a47685878ee27b736a0a3"
  integrity sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=

mavon-editor@^2.9.1:
  version "2.9.1"
  resolved "https://registry.npm.taobao.org/mavon-editor/download/mavon-editor-2.9.1.tgz#59adc7cbd1a1a71f691cf5b2a27e60ec3e6997cc"
  integrity sha1-Wa3Hy9Ghpx9pHPWyon5g7D5pl8w=
  dependencies:
    highlight.js "^9.11.0"
    highlight.js-async-webpack "^1.0.4"
    xss "^1.0.6"

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://registry.npm.taobao.org/md5.js/download/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdast-util-from-markdown@^0.8.0:
  version "0.8.4"
  resolved "https://registry.npm.taobao.org/mdast-util-from-markdown/download/mdast-util-from-markdown-0.8.4.tgz#2882100c1b9fc967d3f83806802f303666682d32"
  integrity sha1-KIIQDBufyWfT+DgGgC8wNmZoLTI=
  dependencies:
    "@types/mdast" "^3.0.0"
    mdast-util-to-string "^2.0.0"
    micromark "~2.11.0"
    parse-entities "^2.0.0"
    unist-util-stringify-position "^2.0.0"

mdast-util-to-markdown@^0.6.0:
  version "0.6.2"
  resolved "https://registry.npm.taobao.org/mdast-util-to-markdown/download/mdast-util-to-markdown-0.6.2.tgz?cache=0&sync_timestamp=1608986843096&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdast-util-to-markdown%2Fdownload%2Fmdast-util-to-markdown-0.6.2.tgz#8fe6f42a2683c43c5609dfb40407c095409c85b4"
  integrity sha1-j+b0KiaDxDxWCd+0BAfAlUCchbQ=
  dependencies:
    "@types/unist" "^2.0.0"
    longest-streak "^2.0.0"
    mdast-util-to-string "^2.0.0"
    parse-entities "^2.0.0"
    repeat-string "^1.0.0"
    zwitch "^1.0.0"

mdast-util-to-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/mdast-util-to-string/download/mdast-util-to-string-2.0.0.tgz#b8cfe6a713e1091cb5b728fc48885a4767f8b97b"
  integrity sha1-uM/mpxPhCRy1tyj8SIhaR2f4uXs=

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.npm.taobao.org/mdn-data/download/mdn-data-2.0.14.tgz?cache=0&sync_timestamp=1610056910483&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.14.tgz#7113fc4281917d63ce29b43446f701e68c25ba50"
  integrity sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA=

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/mdn-data/download/mdn-data-2.0.4.tgz?cache=0&sync_timestamp=1610056910483&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmdn-data%2Fdownload%2Fmdn-data-2.0.4.tgz#699b3c38ac6f1d728091a64650b65d388502fd5b"
  integrity sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-fs@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.5.0.tgz#324c01288b88652966d161db77838720845a8e3c"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npm.taobao.org/meow/download/meow-9.0.0.tgz?cache=0&sync_timestamp=1610076659129&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmeow%2Fdownload%2Fmeow-9.0.0.tgz#cd9510bc5cac9dee7d03c73ee1f9ad959f4ea364"
  integrity sha1-zZUQvFysne59A8c+4fmtlZ9Oo2Q=
  dependencies:
    "@types/minimist" "^1.2.0"
    camelcase-keys "^6.2.2"
    decamelize "^1.2.0"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.0"
    read-pkg-up "^7.0.1"
    redent "^3.0.0"
    trim-newlines "^3.0.0"
    type-fest "^0.18.0"
    yargs-parser "^20.2.3"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-options@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/merge-options/download/merge-options-1.0.1.tgz?cache=0&sync_timestamp=1606147071422&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmerge-options%2Fdownload%2Fmerge-options-1.0.1.tgz#2a64b24457becd4e4dc608283247e94ce589aa32"
  integrity sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI=
  dependencies:
    is-plain-obj "^1.1"

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/merge-source-map/download/merge-source-map-1.1.0.tgz#2fdde7e6020939f70906a68f2d7ae685e4c8c646"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3, merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromark@~2.11.0:
  version "2.11.2"
  resolved "https://registry.npm.taobao.org/micromark/download/micromark-2.11.2.tgz?cache=0&sync_timestamp=1607527484531&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromark%2Fdownload%2Fmicromark-2.11.2.tgz#e8b6a05f54697d2d3d27fc89600c6bc40dd05f35"
  integrity sha1-6LagX1RpfS09J/yJYAxrxA3QXzU=
  dependencies:
    debug "^4.0.0"
    parse-entities "^2.0.0"

micromatch@3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/micromatch/download/micromatch-3.1.0.tgz#5102d4eaf20b6997d6008e3acfe1c44a3fa815e2"
  integrity sha1-UQLU6vILaZfWAI46z+HESj+oFeI=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.2.2"
    define-property "^1.0.0"
    extend-shallow "^2.0.1"
    extglob "^2.0.2"
    fragment-cache "^0.2.1"
    kind-of "^5.0.2"
    nanomatch "^1.2.1"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/micromatch/download/micromatch-4.0.2.tgz#4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259"
  integrity sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.0.5"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/miller-rabin/download/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.45.0, "mime-db@>= 1.43.0 < 2":
  version "1.45.0"
  resolved "https://registry.npm.taobao.org/mime-db/download/mime-db-1.45.0.tgz?cache=0&sync_timestamp=1600831150274&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.45.0.tgz#cceeda21ccd7c3a745eba2decd55d4b73e7879ea"
  integrity sha1-zO7aIczXw6dF66LezVXUtz54eeo=

mime-types@^2.0.1, mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.7:
  version "2.1.28"
  resolved "https://registry.npm.taobao.org/mime-types/download/mime-types-2.1.28.tgz?cache=0&sync_timestamp=1609559952590&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.28.tgz#1160c4757eab2c5363888e005273ecf79d2a0ecd"
  integrity sha1-EWDEdX6rLFNjiI4AUnPs950qDs0=
  dependencies:
    mime-db "1.45.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npm.taobao.org/mime/download/mime-1.6.0.tgz?cache=0&sync_timestamp=1610756280096&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime%2Fdownload%2Fmime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.4:
  version "2.5.0"
  resolved "https://registry.npm.taobao.org/mime/download/mime-2.5.0.tgz?cache=0&sync_timestamp=1610756280096&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime%2Fdownload%2Fmime-2.5.0.tgz#2b4af934401779806ee98026bb42e8c1ae1876b1"
  integrity sha1-K0r5NEAXeYBu6YAmu0Lowa4YdrE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/min-indent/download/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

mini-css-extract-plugin@^0.9.0:
  version "0.9.0"
  resolved "https://registry.npm.taobao.org/mini-css-extract-plugin/download/mini-css-extract-plugin-0.9.0.tgz?cache=0&sync_timestamp=1610548205916&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmini-css-extract-plugin%2Fdownload%2Fmini-css-extract-plugin-0.9.0.tgz#47f2cf07aa165ab35733b1fc97d4c46c0564339e"
  integrity sha1-R/LPB6oWWrNXM7H8l9TEbAVkM54=
  dependencies:
    loader-utils "^1.1.0"
    normalize-url "1.9.1"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/minimist-options/download/minimist-options-4.1.0.tgz#c0655713c53a8a2ebd77ffa247d342c40f010619"
  integrity sha1-wGVXE8U6ii69d/+iR9NCxA8BBhk=
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npm.taobao.org/minimist/download/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

minipass-collect@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/minipass-collect/download/minipass-collect-1.0.2.tgz#22b813bf745dc6edba2576b940022ad6edc8c617"
  integrity sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=
  dependencies:
    minipass "^3.0.0"

minipass-flush@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/minipass-flush/download/minipass-flush-1.0.5.tgz#82e7135d7e89a50ffe64610a787953c4c4cbb373"
  integrity sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=
  dependencies:
    minipass "^3.0.0"

minipass-pipeline@^1.2.2:
  version "1.2.4"
  resolved "https://registry.npm.taobao.org/minipass-pipeline/download/minipass-pipeline-1.2.4.tgz#68472f79711c084657c067c5c6ad93cddea8214c"
  integrity sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=
  dependencies:
    minipass "^3.0.0"

minipass@^3.0.0, minipass@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/minipass/download/minipass-3.1.3.tgz#7d42ff1f39635482e15f9cdb53184deebd5815fd"
  integrity sha1-fUL/HzljVILhX5zbUxhN7r1YFf0=
  dependencies:
    yallist "^4.0.0"

minizlib@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/minizlib/download/minizlib-2.1.2.tgz#e90d3466ba209b932451508a11ce3d3632145931"
  integrity sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mississippi@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/mississippi/download/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mitt@1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/mitt/download/mitt-1.1.2.tgz#380e61480d6a615b660f07abb60d51e0a4e4bed6"
  integrity sha1-OA5hSA1qYVtmDwertg1R4KTkvtY=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.1, mkdirp@^0.5.3, mkdirp@^0.5.5, mkdirp@~0.5.1:
  version "0.5.5"
  resolved "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mkdirp@^1.0.3, mkdirp@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mockjs@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/mockjs/download/mockjs-1.1.0.tgz#e6a0c378e91906dbaff20911cc0273b3c7d75b06"
  integrity sha1-5qDDeOkZBtuv8gkRzAJzs8fXWwY=
  dependencies:
    commander "*"

moment@^2.21.0:
  version "2.29.1"
  resolved "https://registry.yarnpkg.com/moment/-/moment-2.29.1.tgz#b2be769fa31940be9eeea6469c075e35006fa3d3"
  integrity sha512-kHmoybcPV8Sqy59DwNDY3Jefr64lK/by/da0ViFcuA4DH0vQg5Q6Ze5VimxkfQNSC+Mls/Kx53s7TjP1RhFEDQ==

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/move-concurrently/download/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.2.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.3.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npm.taobao.org/multicast-dns/download/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mutationobserver-shim@^0.3.2:
  version "0.3.7"
  resolved "https://registry.yarnpkg.com/mutationobserver-shim/-/mutationobserver-shim-0.3.7.tgz#8bf633b0c0b0291a1107255ed32c13088a8c5bf3"
  integrity sha512-oRIDTyZQU96nAiz2AQyngwx1e89iApl2hN5AOYwyxLUB47UYsU3Wv9lJWqH5y/QdiYkc5HQLi23ZNB3fELdHcQ==

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.4.0, mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npm.taobao.org/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1:
  version "2.14.2"
  resolved "https://registry.npm.taobao.org/nan/download/nan-2.14.2.tgz?cache=0&sync_timestamp=1602591709094&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnan%2Fdownload%2Fnan-2.14.2.tgz#f5376400695168f4cc694ac9393d0c9585eeea19"
  integrity sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk=

nanomatch@^1.2.1, nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

nanopop@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/nanopop/-/nanopop-2.1.0.tgz#23476513cee2405888afd2e8a4b54066b70b9e60"
  integrity sha512-jGTwpFRexSH+fxappnGQtN9dspgE2ipa1aOjtR24igG0pv6JCxImIAmrLRHX+zUF5+1wtsFVbKyfP51kIGAVNw==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

ndarray-ops@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/ndarray-ops/download/ndarray-ops-1.2.2.tgz#59e88d2c32a7eebcb1bc690fae141579557a614e"
  integrity sha1-WeiNLDKn7ryxvGkPrhQVeVV6YU4=
  dependencies:
    cwise-compiler "^1.0.0"

ndarray-pack@^1.1.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/ndarray-pack/download/ndarray-pack-1.2.1.tgz#8caebeaaa24d5ecf70ff86020637977da8ee585a"
  integrity sha1-jK6+qqJNXs9w/4YCBjeXfajuWFo=
  dependencies:
    cwise-compiler "^1.1.2"
    ndarray "^1.0.13"

ndarray@^1.0.13, ndarray@^1.0.18, ndarray@~1.0.15:
  version "1.0.19"
  resolved "https://registry.npm.taobao.org/ndarray/download/ndarray-1.0.19.tgz#6785b5f5dfa58b83e31ae5b2a058cfd1ab3f694e"
  integrity sha1-Z4W19d+li4PjGuWyoFjP0as/aU4=
  dependencies:
    iota-array "^1.0.0"
    is-buffer "^1.0.2"

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.npm.taobao.org/negotiator/download/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.5.0, neo-async@^2.6.0, neo-async@^2.6.1, neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.npm.taobao.org/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz?cache=0&sync_timestamp=1602353330797&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnice-try%2Fdownload%2Fnice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/no-case/download/no-case-2.3.2.tgz?cache=0&sync_timestamp=1606867290260&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fno-case%2Fdownload%2Fno-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/no-case/download/no-case-3.0.4.tgz?cache=0&sync_timestamp=1606867290260&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fno-case%2Fdownload%2Fno-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-bitmap@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/node-bitmap/download/node-bitmap-0.0.1.tgz#180eac7003e0c707618ef31368f62f84b2a69091"
  integrity sha1-GA6scAPgxwdhjvMTaPYvhLKmkJE=

node-emoji@^1.10.0:
  version "1.10.0"
  resolved "https://registry.yarnpkg.com/node-emoji/-/node-emoji-1.10.0.tgz#8886abd25d9c7bb61802a658523d1f8d2a89b2da"
  integrity sha512-Yt3384If5H6BYGVHiHwTL+99OzJKHhgp82S8/dktEK73T26BazdgZ4JZh92xSVtGNJvz9UbXdNAc5hcrXV42vw==
  dependencies:
    lodash.toarray "^4.4.0"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "https://registry.npm.taobao.org/node-forge/download/node-forge-0.10.0.tgz?cache=0&sync_timestamp=1599010719234&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-forge%2Fdownload%2Fnode-forge-0.10.0.tgz#32dea2afb3e9926f02ee5ce8794902691a676bf3"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-ipc@^9.1.1:
  version "9.1.3"
  resolved "https://registry.npm.taobao.org/node-ipc/download/node-ipc-9.1.3.tgz#1df3f069d103184ae9127fa885dbdaea56a4436f"
  integrity sha1-HfPwadEDGErpEn+ohdva6lakQ28=
  dependencies:
    event-pubsub "4.3.0"
    js-message "1.0.7"
    js-queue "2.0.2"

node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/node-libs-browser/download/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^1.1.69:
  version "1.1.70"
  resolved "https://registry.npm.taobao.org/node-releases/download/node-releases-1.1.70.tgz#66e0ed0273aa65666d7fe78febe7634875426a08"
  integrity sha1-ZuDtAnOqZWZtf+eP6+djSHVCagg=

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1602547356577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-package-data@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-3.0.0.tgz?cache=0&sync_timestamp=1602547356577&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-3.0.0.tgz#1f8a7c423b3d2e85eb36985eaf81de381d01301a"
  integrity sha1-H4p8Qjs9LoXrNpher4HeOB0BMBo=
  dependencies:
    hosted-git-info "^3.0.6"
    resolve "^1.17.0"
    semver "^7.3.2"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/normalize-path/download/normalize-path-1.0.0.tgz#32d0e472f91ff345701c15a8311018d3b0a90379"
  integrity sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-selector@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/normalize-selector/download/normalize-selector-0.2.0.tgz#d0b145eb691189c63a78d201dc4fdb1293ef0c03"
  integrity sha1-0LFF62kRicY6eNIB3E/bEpPvDAM=

normalize-url@1.9.1:
  version "1.9.1"
  resolved "https://registry.npm.taobao.org/normalize-url/download/normalize-url-1.9.1.tgz#2cc0d66b31ea23036458436e3620d85954c66c3c"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npm.taobao.org/normalize-url/download/normalize-url-3.3.0.tgz#b2e1c4dc4f7c6d57743df733a4f5978d18650559"
  integrity sha1-suHE3E98bVd0PfczpPWXjRhlBVk=

normalize-wheel@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/normalize-wheel/download/normalize-wheel-1.0.1.tgz#aec886affdb045070d856447df62ecf86146ec45"
  integrity sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nprogress@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/nprogress/download/nprogress-0.2.0.tgz#cb8f34c53213d895723fcbab907e9422adbcafb1"
  integrity sha1-y480xTIT2JVyP8urkH6UIq28r7E=

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/nth-check/download/nth-check-1.0.2.tgz?cache=0&sync_timestamp=1606860664533&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnth-check%2Fdownload%2Fnth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

obj-extend@~0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/obj-extend/download/obj-extend-0.1.0.tgz#bb448a4775fb95eb34a781f908bbac2df23dbb5b"
  integrity sha1-u0SKR3X7les0p4H5CLusLfI9u1s=

object-assign@4.x, object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/object-hash/download/object-hash-1.3.1.tgz?cache=0&sync_timestamp=1608924025400&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-hash%2Fdownload%2Fobject-hash-1.3.1.tgz#fde452098a951cb145f039bb7d455449ddc126df"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.8.0, object-inspect@^1.9.0:
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/object-inspect/download/object-inspect-1.9.0.tgz?cache=0&sync_timestamp=1606804292445&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-inspect%2Fdownload%2Fobject-inspect-1.9.0.tgz#c90521d74e1127b67266ded3394ad6116986533a"
  integrity sha1-yQUh104RJ7ZyZt7TOUrWEWmGUzo=

object-is@^1.0.1:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/object-is/download/object-is-1.1.4.tgz?cache=0&sync_timestamp=1606460463646&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-is%2Fdownload%2Fobject-is-1.1.4.tgz#63d6c83c00a43f4cbc9434eb9757c8a5b8565068"
  integrity sha1-Y9bIPACkP0y8lDTrl1fIpbhWUGg=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0, object.assign@^4.1.1, object.assign@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npm.taobao.org/object.assign/download/object.assign-4.1.2.tgz?cache=0&sync_timestamp=1604115167242&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.assign%2Fdownload%2Fobject.assign-4.1.2.tgz#0ed54a342eceb37b38ff76eb831a0e788cb63940"
  integrity sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.getownpropertydescriptors@^2.0.3, object.getownpropertydescriptors@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.1.tgz?cache=0&sync_timestamp=1606458156847&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject.getownpropertydescriptors%2Fdownload%2Fobject.getownpropertydescriptors-2.1.1.tgz#0dfda8d108074d9c563e80490c883b6661091544"
  integrity sha1-Df2o0QgHTZxWPoBJDIg7ZmEJFUQ=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/object.values/download/object.values-1.1.2.tgz#7a2015e06fcb0f546bd652486ce8583a4731c731"
  integrity sha1-eiAV4G/LD1Rr1lJIbOhYOkcxxzE=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    has "^1.0.3"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/obuf/download/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

omggif@^1.0.5:
  version "1.0.10"
  resolved "https://registry.npm.taobao.org/omggif/download/omggif-1.0.10.tgz#ddaaf90d4a42f532e9e7cb3a95ecdd47f17c7b19"
  integrity sha1-3ar5DUpC9TLp58s6lezdR/F8exk=

omit.js@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/omit.js/-/omit.js-1.0.2.tgz#91a14f0eba84066dfa015bf30e474c47f30bc858"
  integrity sha512-/QPc6G2NS+8d4L/cQhbk6Yit1WTB6Us2g84A7A/1+w9d/eRGHyEqC5kkQtHVoHZ5NFWGG7tUGgrhVZwgZanKrQ==
  dependencies:
    babel-runtime "^6.23.0"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.3.0:
  version "6.4.0"
  resolved "https://registry.npm.taobao.org/open/download/open-6.4.0.tgz?cache=0&sync_timestamp=1610003231392&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fopen%2Fdownload%2Fopen-6.4.0.tgz#5c13e96d0dc894686164f18965ecfe889ecfc8a9"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

opener@^1.5.1:
  version "1.5.2"
  resolved "https://registry.npm.taobao.org/opener/download/opener-1.5.2.tgz#5d37e1f35077b9dcac4301372271afdeb2a13598"
  integrity sha1-XTfh81B3udysQwE3InGv3rKhNZg=

opn@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npm.taobao.org/opn/download/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npm.taobao.org/optionator/download/optionator-0.9.1.tgz#4f236a6373dae0566a6d43e1326674f50c291499"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

ora@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npm.taobao.org/ora/download/ora-3.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fora%2Fdownload%2Fora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

original@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/original/download/original-1.0.2.tgz#e442a61cffe1c5fd20a65f3261c26663b303f25f"
  integrity sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/os-browserify/download/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-finally@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/p-finally/download/p-finally-2.0.1.tgz#bd6fcaa9c559a096b680806f4d657b3f0f240561"
  integrity sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=

p-limit@^2.0.0, p-limit@^2.2.0, p-limit@^2.2.1, p-limit@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/p-limit/download/p-limit-2.3.0.tgz?cache=0&sync_timestamp=1606288352885&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-map@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/p-map/download/p-map-3.0.0.tgz#d704d9af8a2ba684e2600d9a215983d4141a979d"
  integrity sha1-1wTZr4orpoTiYA2aIVmD1BQal50=
  dependencies:
    aggregate-error "^3.0.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/p-map/download/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/p-retry/download/p-retry-3.0.1.tgz#316b4c8893e2c8dc1cfa891f406c4b422bebf328"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.npm.taobao.org/pako/download/pako-1.0.11.tgz?cache=0&sync_timestamp=1610208860443&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpako%2Fdownload%2Fpako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/param-case/download/param-case-2.1.1.tgz?cache=0&sync_timestamp=1606867288643&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparam-case%2Fdownload%2Fparam-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

param-case@^3.0.3:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/param-case/download/param-case-3.0.4.tgz?cache=0&sync_timestamp=1606867288643&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparam-case%2Fdownload%2Fparam-case-3.0.4.tgz#7d17fe4aa12bde34d4a77d91acfb6219caad01c5"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.5:
  version "5.1.6"
  resolved "https://registry.npm.taobao.org/parse-asn1/download/parse-asn1-5.1.6.tgz?cache=0&sync_timestamp=1597165710136&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse-asn1%2Fdownload%2Fparse-asn1-5.1.6.tgz#385080a3ec13cb62a62d39409cb3e88844cdaed4"
  integrity sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=
  dependencies:
    asn1.js "^5.2.0"
    browserify-aes "^1.0.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-data-uri@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/parse-data-uri/download/parse-data-uri-0.2.0.tgz#bf04d851dd5c87b0ab238e5d01ace494b604b4c9"
  integrity sha1-vwTYUd1ch7CrI45dAazklLYEtMk=
  dependencies:
    data-uri-to-buffer "0.0.3"

parse-entities@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/parse-entities/download/parse-entities-2.0.0.tgz#53c6eb5b9314a1f4ec99fa0fdf7ce01ecda0cbe8"
  integrity sha1-U8brW5MUofTsmfoP33zgHs2gy+g=
  dependencies:
    character-entities "^1.0.0"
    character-entities-legacy "^1.0.0"
    character-reference-invalid "^1.0.0"
    is-alphanumerical "^1.0.0"
    is-decimal "^1.0.0"
    is-hexadecimal "^1.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/parse-json/download/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1610966642419&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse-json%2Fdownload%2Fparse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/parse-json/download/parse-json-5.2.0.tgz?cache=0&sync_timestamp=1610966642419&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse-json%2Fdownload%2Fparse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npm.taobao.org/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz#2cdf9ad823321140370d4dbf5d3e92c7c8ddc6e6"
  integrity sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=
  dependencies:
    parse5 "^6.0.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/parse5/download/parse5-5.1.1.tgz?cache=0&sync_timestamp=1595850971402&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse5%2Fdownload%2Fparse5-5.1.1.tgz#f68e4e5ba1852ac2cadc00f4555fff6c2abb6178"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parse5@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npm.taobao.org/parse5/download/parse5-6.0.1.tgz?cache=0&sync_timestamp=1595850971402&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fparse5%2Fdownload%2Fparse5-6.0.1.tgz#e1a1c085c569b3dc08321184f19a39cc27f7c30b"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npm.taobao.org/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npm.taobao.org/pascal-case/download/pascal-case-3.1.2.tgz?cache=0&sync_timestamp=1606867306970&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpascal-case%2Fdownload%2Fpascal-case-3.1.2.tgz#b48e0ef2b98e205e7c1dae747d0b1508237660eb"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/path-exists/download/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@^2.2.1:
  version "2.4.0"
  resolved "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-2.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-2.4.0.tgz#35ce7f333d5616f1c1e1bfe266c3aba2e5b2e704"
  integrity sha1-Nc5/Mz1WFvHB4b/iZsOrouWy5wQ=

path-to-regexp@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-6.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-6.2.0.tgz#f7b3803336104c346889adece614669230645f38"
  integrity sha1-97OAMzYQTDRoia3s5hRmkjBkXzg=

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pbkdf2@^3.0.3:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/pbkdf2/download/pbkdf2-3.1.1.tgz#cb8724b0fada984596856d1a6ebafd3584654b94"
  integrity sha1-y4cksPramEWWhW0abrr9NYRlS5Q=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.4, picomatch@^2.0.5, picomatch@^2.2.1:
  version "2.2.2"
  resolved "https://registry.npm.taobao.org/picomatch/download/picomatch-2.2.2.tgz#21f333e9b6b8eaff02468f5146ea406d345f4dad"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz?cache=0&sync_timestamp=1593529716831&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpify%2Fdownload%2Fpify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz?cache=0&sync_timestamp=1593529716831&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpify%2Fdownload%2Fpify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/pify/download/pify-4.0.1.tgz?cache=0&sync_timestamp=1593529716831&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpify%2Fdownload%2Fpify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pixelsmith@^2.3.0:
  version "2.4.1"
  resolved "https://registry.npm.taobao.org/pixelsmith/download/pixelsmith-2.4.1.tgz#4ccfecf6eb5447da1d38fe26f2d70d246a40179e"
  integrity sha1-TM/s9utUR9odOP4m8tcNJGpAF54=
  dependencies:
    async "~0.9.0"
    concat-stream "~1.5.1"
    get-pixels "~3.3.0"
    mime-types "~2.1.7"
    ndarray "~1.0.15"
    obj-extend "~0.1.0"
    save-pixels "~2.3.0"
    vinyl-file "~1.3.0"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-1.0.0.tgz?cache=0&sync_timestamp=1602859056682&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpkg-dir%2Fdownload%2Fpkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-3.0.0.tgz?cache=0&sync_timestamp=1602859056682&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpkg-dir%2Fdownload%2Fpkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/pkg-dir/download/pkg-dir-4.2.0.tgz?cache=0&sync_timestamp=1602859056682&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpkg-dir%2Fdownload%2Fpkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

plur@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/plur/download/plur-2.1.2.tgz#7482452c1a0f508e3e344eaec312c91c29dc655a"
  integrity sha1-dIJFLBoPUI4+NE6uwxLJHCncZVo=
  dependencies:
    irregular-plurals "^1.0.0"

pngjs-nozlib@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/pngjs-nozlib/download/pngjs-nozlib-1.0.0.tgz#9e64d602cfe9cce4d9d5997d0687429a73f0b7d7"
  integrity sha1-nmTWAs/pzOTZ1Zl9BodCmnPwt9c=

pngjs@^3.3.3:
  version "3.4.0"
  resolved "https://registry.npm.taobao.org/pngjs/download/pngjs-3.4.0.tgz?cache=0&sync_timestamp=1603548566507&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpngjs%2Fdownload%2Fpngjs-3.4.0.tgz#99ca7d725965fb655814eaf65f38f12bbdbf555f"
  integrity sha1-mcp9clll+2VYFOr2XzjxK72/VV8=

pnp-webpack-plugin@^1.6.4:
  version "1.6.4"
  resolved "https://registry.npm.taobao.org/pnp-webpack-plugin/download/pnp-webpack-plugin-1.6.4.tgz#c9711ac4dc48a685dabafc86f8b6dd9f8df84149"
  integrity sha1-yXEaxNxIpoXauvyG+Lbdn434QUk=
  dependencies:
    ts-pnp "^1.1.6"

portfinder@^1.0.26:
  version "1.0.28"
  resolved "https://registry.npm.taobao.org/portfinder/download/portfinder-1.0.28.tgz#67c4622852bd5374dd1dd900f779f53462fac778"
  integrity sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.5"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^7.0.1:
  version "7.0.5"
  resolved "https://registry.npm.taobao.org/postcss-calc/download/postcss-calc-7.0.5.tgz?cache=0&sync_timestamp=1609689190192&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-calc%2Fdownload%2Fpostcss-calc-7.0.5.tgz#f8a6e99f12e619c2ebc23cf6c486fdc15860933e"
  integrity sha1-+KbpnxLmGcLrwjz2xIb9wVhgkz4=
  dependencies:
    postcss "^7.0.27"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.2"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/postcss-colormin/download/postcss-colormin-4.0.3.tgz?cache=0&sync_timestamp=1610386865817&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-colormin%2Fdownload%2Fpostcss-colormin-4.0.3.tgz#ae060bce93ed794ac71264f08132d550956bd381"
  integrity sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz?cache=0&sync_timestamp=1610386865654&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-convert-values%2Fdownload%2Fpostcss-convert-values-4.0.1.tgz#ca3813ed4da0f812f9d43703584e449ebe189a7f"
  integrity sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz?cache=0&sync_timestamp=1610386865934&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-discard-comments%2Fdownload%2Fpostcss-discard-comments-4.0.2.tgz#1fbabd2c246bff6aaad7997b2b0918f4d7af4033"
  integrity sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz?cache=0&sync_timestamp=1610386866060&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-discard-duplicates%2Fdownload%2Fpostcss-discard-duplicates-4.0.2.tgz#3fe133cd3c82282e550fc9b239176a9207b784eb"
  integrity sha1-P+EzzTyCKC5VD8myORdqkge3hOs=
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz?cache=0&sync_timestamp=1610386866169&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-discard-empty%2Fdownload%2Fpostcss-discard-empty-4.0.1.tgz#c8c951e9f73ed9428019458444a02ad90bb9f765"
  integrity sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz?cache=0&sync_timestamp=1610386866271&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-discard-overridden%2Fdownload%2Fpostcss-discard-overridden-4.0.1.tgz#652aef8a96726f029f5e3e00146ee7a4e755ff57"
  integrity sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=
  dependencies:
    postcss "^7.0.0"

postcss-html@^0.36.0:
  version "0.36.0"
  resolved "https://registry.npm.taobao.org/postcss-html/download/postcss-html-0.36.0.tgz#b40913f94eaacc2453fd30a1327ad6ee1f88b204"
  integrity sha1-tAkT+U6qzCRT/TChMnrW7h+IsgQ=
  dependencies:
    htmlparser2 "^3.10.0"

postcss-less@^3.1.4:
  version "3.1.4"
  resolved "https://registry.npm.taobao.org/postcss-less/download/postcss-less-3.1.4.tgz#369f58642b5928ef898ffbc1a6e93c958304c5ad"
  integrity sha1-Np9YZCtZKO+Jj/vBpuk8lYMExa0=
  dependencies:
    postcss "^7.0.14"

postcss-load-config@^2.0.0:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-load-config%2Fdownload%2Fpostcss-load-config-2.1.2.tgz#c5ea504f2c4aef33c7359a34de3573772ad7502a"
  integrity sha1-xepQTyxK7zPHNZo03jVzdyrXUCo=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/postcss-loader/download/postcss-loader-3.0.0.tgz?cache=0&sync_timestamp=1611230874297&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-loader%2Fdownload%2Fpostcss-loader-3.0.0.tgz#6b97943e47c72d845fa9e03f273773d4e8dd6c2d"
  integrity sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^7.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^1.0.0"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npm.taobao.org/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "https://registry.npm.taobao.org/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz?cache=0&sync_timestamp=1610386866607&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-merge-longhand%2Fdownload%2Fpostcss-merge-longhand-4.0.11.tgz#62f49a13e4a0ee04e7b98f42bb16062ca2549e24"
  integrity sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz?cache=0&sync_timestamp=1610386866739&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-4.0.3.tgz#362bea4ff5a1f98e4075a713c6cb25aefef9a650"
  integrity sha1-NivqT/Wh+Y5AdacTxsslrv75plA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz?cache=0&sync_timestamp=1610386866957&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-minify-font-values%2Fdownload%2Fpostcss-minify-font-values-4.0.2.tgz#cd4c344cce474343fac5d82206ab2cbcb8afd5a6"
  integrity sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz?cache=0&sync_timestamp=1610386867073&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-minify-gradients%2Fdownload%2Fpostcss-minify-gradients-4.0.2.tgz#93b29c2ff5099c535eecda56c4aa6e665a663471"
  integrity sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz?cache=0&sync_timestamp=1610386866854&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-minify-params%2Fdownload%2Fpostcss-minify-params-4.0.2.tgz#6b9cef030c11e35261f95f618c90036d680db874"
  integrity sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz?cache=0&sync_timestamp=1610386867194&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-minify-selectors%2Fdownload%2Fpostcss-minify-selectors-4.0.2.tgz#e2e5eb40bfee500d0cd9243500f5f8ea4262fbd8"
  integrity sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz?cache=0&sync_timestamp=1602588202058&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-extract-imports%2Fdownload%2Fpostcss-modules-extract-imports-2.0.0.tgz#818719a1ae1da325f9832446b01136eeb493cd7e"
  integrity sha1-gYcZoa4doyX5gyRGsBE27rSTzX4=
  dependencies:
    postcss "^7.0.5"

postcss-modules-local-by-default@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.3.tgz#bb14e0cc78279d504dbdcbfd7e0ca28993ffbbb0"
  integrity sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A=
  dependencies:
    icss-utils "^4.1.1"
    postcss "^7.0.32"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-2.2.0.tgz#385cae013cc7743f5a7d7602d1073a89eaae62ee"
  integrity sha1-OFyuATzHdD9afXYC0Qc6iequYu4=
  dependencies:
    postcss "^7.0.6"
    postcss-selector-parser "^6.0.0"

postcss-modules-values@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz?cache=0&sync_timestamp=1602586308035&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-modules-values%2Fdownload%2Fpostcss-modules-values-3.0.0.tgz#5b5000d6ebae29b4255301b4a3a54574423e7f10"
  integrity sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA=
  dependencies:
    icss-utils "^4.0.0"
    postcss "^7.0.6"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz?cache=0&sync_timestamp=1610386867305&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-charset%2Fdownload%2Fpostcss-normalize-charset-4.0.1.tgz#8b35add3aee83a136b0471e0d59be58a50285dd4"
  integrity sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz?cache=0&sync_timestamp=1610386867411&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-display-values%2Fdownload%2Fpostcss-normalize-display-values-4.0.2.tgz#0dbe04a4ce9063d4667ed2be476bb830c825935a"
  integrity sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz?cache=0&sync_timestamp=1610386867511&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-positions%2Fdownload%2Fpostcss-normalize-positions-4.0.2.tgz#05f757f84f260437378368a91f8932d4b102917f"
  integrity sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz?cache=0&sync_timestamp=1610386867614&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-repeat-style%2Fdownload%2Fpostcss-normalize-repeat-style-4.0.2.tgz#c4ebbc289f3991a028d44751cbdd11918b17910c"
  integrity sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz?cache=0&sync_timestamp=1610386868122&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-string%2Fdownload%2Fpostcss-normalize-string-4.0.2.tgz#cd44c40ab07a0c7a36dc5e99aace1eca4ec2690c"
  integrity sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz?cache=0&sync_timestamp=1610386867711&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-timing-functions%2Fdownload%2Fpostcss-normalize-timing-functions-4.0.2.tgz#8e009ca2a3949cdaf8ad23e6b6ab99cb5e7d28d9"
  integrity sha1-jgCcoqOUnNr4rSPmtquZy159KNk=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz?cache=0&sync_timestamp=1610386867804&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-unicode%2Fdownload%2Fpostcss-normalize-unicode-4.0.1.tgz#841bd48fdcf3019ad4baa7493a3d363b52ae1cfb"
  integrity sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz?cache=0&sync_timestamp=1610386867926&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-url%2Fdownload%2Fpostcss-normalize-url-4.0.1.tgz#10e437f86bc7c7e58f7b9652ed878daaa95faae1"
  integrity sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz?cache=0&sync_timestamp=1610386868021&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-normalize-whitespace%2Fdownload%2Fpostcss-normalize-whitespace-4.0.2.tgz#bf1d4070fe4fcea87d1348e825d8cc0c5faa7d82"
  integrity sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npm.taobao.org/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz?cache=0&sync_timestamp=1610386868226&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-ordered-values%2Fdownload%2Fpostcss-ordered-values-4.1.2.tgz#0cf75c820ec7d5c4d280189559e0b571ebac0eee"
  integrity sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-prefix-selector@^1.6.0:
  version "1.7.2"
  resolved "https://registry.npm.taobao.org/postcss-prefix-selector/download/postcss-prefix-selector-1.7.2.tgz#3adeed903985734298f19d8f5e0b657f9d90d43c"
  integrity sha1-Ot7tkDmFc0KY8Z2PXgtlf52Q1Dw=
  dependencies:
    postcss "^7.0.0"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz?cache=0&sync_timestamp=1610386864429&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-reduce-initial%2Fdownload%2Fpostcss-reduce-initial-4.0.3.tgz#7fd42ebea5e9c814609639e2c2e84ae270ba48df"
  integrity sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz?cache=0&sync_timestamp=1610386864320&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-reduce-transforms%2Fdownload%2Fpostcss-reduce-transforms-4.0.2.tgz#17efa405eacc6e07be3414a5ca2d1074681d4e29"
  integrity sha1-F++kBerMbge+NBSlyi0QdGgdTik=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.1.tgz#29ccbc7c37dedfac304e9fff0bf1596b3f6a0e4e"
  integrity sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=

postcss-safe-parser@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-safe-parser/download/postcss-safe-parser-4.0.2.tgz#a6d4e48f0f37d9f7c11b2a581bf00f8ba4870b96"
  integrity sha1-ptTkjw832ffBGypYG/APi6SHC5Y=
  dependencies:
    postcss "^7.0.26"

postcss-sass@^0.4.4:
  version "0.4.4"
  resolved "https://registry.npm.taobao.org/postcss-sass/download/postcss-sass-0.4.4.tgz#91f0f3447b45ce373227a98b61f8d8f0785285a3"
  integrity sha1-kfDzRHtFzjcyJ6mLYfjY8HhShaM=
  dependencies:
    gonzales-pe "^4.3.0"
    postcss "^7.0.21"

postcss-scss@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/postcss-scss/download/postcss-scss-2.1.1.tgz?cache=0&sync_timestamp=1604595237660&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-scss%2Fdownload%2Fpostcss-scss-2.1.1.tgz#ec3a75fa29a55e016b90bf3269026c53c1d2b383"
  integrity sha1-7Dp1+imlXgFrkL8yaQJsU8HSs4M=
  dependencies:
    postcss "^7.0.6"

postcss-selector-parser@^3.0.0:
  version "3.1.2"
  resolved "https://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-3.1.2.tgz?cache=0&sync_timestamp=1601045450967&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-3.1.2.tgz#b310f5c4c0fdaf76f94902bbaa30db6aa84f5270"
  integrity sha1-sxD1xMD9r3b5SQK7qjDbaqhPUnA=
  dependencies:
    dot-prop "^5.2.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.0, postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4:
  version "6.0.4"
  resolved "https://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-6.0.4.tgz?cache=0&sync_timestamp=1601045450967&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-6.0.4.tgz#56075a1380a04604c38b063ea7767a129af5c2b3"
  integrity sha1-VgdaE4CgRgTDiwY+p3Z6Epr1wrM=
  dependencies:
    cssesc "^3.0.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"
    util-deprecate "^1.0.2"

postcss-svgo@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/postcss-svgo/download/postcss-svgo-4.0.2.tgz?cache=0&sync_timestamp=1610386864560&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-svgo%2Fdownload%2Fpostcss-svgo-4.0.2.tgz#17b997bc711b333bab143aaed3b8d3d6e3d38258"
  integrity sha1-F7mXvHEbMzurFDqu07jT1uPTglg=
  dependencies:
    is-svg "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-syntax@^0.36.2:
  version "0.36.2"
  resolved "https://registry.npm.taobao.org/postcss-syntax/download/postcss-syntax-0.36.2.tgz#f08578c7d95834574e5593a82dfbfa8afae3b51c"
  integrity sha1-8IV4x9lYNFdOVZOoLfv6ivrjtRw=

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz?cache=0&sync_timestamp=1610386864900&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss-unique-selectors%2Fdownload%2Fpostcss-unique-selectors-4.0.1.tgz#9446911f3289bfd64c6d680f073c03b1f9ee4bac"
  integrity sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-value-parser@^3.0.0:
  version "3.3.1"
  resolved "https://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz#9ff822547e2893213cf1c30efa51ac5fd1ba8281"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-value-parser@^4.0.2, postcss-value-parser@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz#443f6a20ced6481a2bda4fa8532a6e55d789a2cb"
  integrity sha1-RD9qIM7WSBor2k+oUypuVdeJoss=

postcss@^5.2.17:
  version "5.2.18"
  resolved "https://registry.npm.taobao.org/postcss/download/postcss-5.2.18.tgz?cache=0&sync_timestamp=1610186312013&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-5.2.18.tgz#badfa1497d46244f6390f58b319830d9107853c5"
  integrity sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U=
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^7.0.0, postcss@^7.0.1, postcss@^7.0.14, postcss@^7.0.2, postcss@^7.0.21, postcss@^7.0.26, postcss@^7.0.27, postcss@^7.0.32, postcss@^7.0.35, postcss@^7.0.5, postcss@^7.0.6:
  version "7.0.35"
  resolved "https://registry.npm.taobao.org/postcss/download/postcss-7.0.35.tgz?cache=0&sync_timestamp=1610186312013&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpostcss%2Fdownload%2Fpostcss-7.0.35.tgz#d2be00b998f7f211d8a276974079f2e92b970e24"
  integrity sha1-0r4AuZj38hHYonaXQHny6SuXDiQ=
  dependencies:
    chalk "^2.4.2"
    source-map "^0.6.1"
    supports-color "^6.1.0"

posthtml-parser@^0.2.0, posthtml-parser@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/posthtml-parser/download/posthtml-parser-0.2.1.tgz#35d530de386740c2ba24ff2eb2faf39ccdf271dd"
  integrity sha1-NdUw3jhnQMK6JP8usvrznM3ycd0=
  dependencies:
    htmlparser2 "^3.8.3"
    isobject "^2.1.0"

posthtml-rename-id@^1.0:
  version "1.0.12"
  resolved "https://registry.npm.taobao.org/posthtml-rename-id/download/posthtml-rename-id-1.0.12.tgz#cf7f6eb37146bf1afac31e68f18c6cc19ae61433"
  integrity sha1-z39us3FGvxr6wx5o8YxswZrmFDM=
  dependencies:
    escape-string-regexp "1.0.5"

posthtml-render@^1.0.5, posthtml-render@^1.0.6:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/posthtml-render/download/posthtml-render-1.4.0.tgz?cache=0&sync_timestamp=1605682833852&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fposthtml-render%2Fdownload%2Fposthtml-render-1.4.0.tgz#40114070c45881cacb93347dae3eff53afbcff13"
  integrity sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM=

posthtml-svg-mode@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/posthtml-svg-mode/download/posthtml-svg-mode-1.0.3.tgz#abd554face81223cab0cb367e18e4efd2a4e74b0"
  integrity sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA=
  dependencies:
    merge-options "1.0.1"
    posthtml "^0.9.2"
    posthtml-parser "^0.2.1"
    posthtml-render "^1.0.6"

posthtml@^0.9.2:
  version "0.9.2"
  resolved "https://registry.npm.taobao.org/posthtml/download/posthtml-0.9.2.tgz#f4c06db9f67b61fd17c4e256e7e3d9515bf726fd"
  integrity sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0=
  dependencies:
    posthtml-parser "^0.2.0"
    posthtml-render "^1.0.5"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier@^1.18.2:
  version "1.19.1"
  resolved "https://registry.npm.taobao.org/prettier/download/prettier-1.19.1.tgz#f7d7f5ff8a9cd872a7be4ca142095956a60797cb"
  integrity sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=

pretty-error@^2.0.2, pretty-error@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/pretty-error/download/pretty-error-2.1.2.tgz?cache=0&sync_timestamp=1609589422297&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpretty-error%2Fdownload%2Fpretty-error-2.1.2.tgz#be89f82d81b1c86ec8fdfbc385045882727f93b6"
  integrity sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=
  dependencies:
    lodash "^4.17.20"
    renderkid "^2.0.4"

process-nextick-args@~1.0.6:
  version "1.0.7"
  resolved "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-1.0.7.tgz#150e20b756590ad3f91093f25a4f2ad8bff30ba3"
  integrity sha1-FQ4gt1ZZCtP5EJPyWk8q2L/zC6M=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.npm.taobao.org/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/promise-inflight/download/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

proxy-addr@~2.0.5:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/proxy-addr/download/proxy-addr-2.0.6.tgz#fdc2336505447d3f2f2c638ed272caf614bbb2bf"
  integrity sha1-/cIzZQVEfT8vLGOO0nLK9hS7sr8=
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/prr/download/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "https://registry.npm.taobao.org/psl/download/psl-1.8.0.tgz#9326f8bcfb013adcc005fdff056acce020e51c24"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/pumpify/download/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

q@^1.1.2:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@6.7.0:
  version "6.7.0"
  resolved "https://registry.npm.taobao.org/qs/download/qs-6.7.0.tgz?cache=0&sync_timestamp=1610598229410&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fqs%2Fdownload%2Fqs-6.7.0.tgz#41dc1a015e3d581f1621776be31afb2876a9b1bc"
  integrity sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=

qs@~6.5.2:
  version "6.5.2"
  resolved "https://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz?cache=0&sync_timestamp=1610598229410&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fqs%2Fdownload%2Fqs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

query-string@^4.1.0, query-string@^4.3.2:
  version "4.3.4"
  resolved "https://registry.npm.taobao.org/query-string/download/query-string-4.3.4.tgz?cache=0&sync_timestamp=1609310782567&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fquery-string%2Fdownload%2Fquery-string-4.3.4.tgz#bbb693b9ca915c232515b228b1a02b609043dbeb"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/querystringify/download/querystringify-2.2.0.tgz?cache=0&sync_timestamp=1597687052330&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fquerystringify%2Fdownload%2Fquerystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

quick-lru@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/quick-lru/download/quick-lru-4.0.1.tgz?cache=0&sync_timestamp=1610610431807&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fquick-lru%2Fdownload%2Fquick-lru-4.0.1.tgz#5b8878f113a58217848c6482026c73e1ba57727f"
  integrity sha1-W4h48ROlgheEjGSCAmxz4bpXcn8=

raf@^3.4.0:
  version "3.4.1"
  resolved "https://registry.yarnpkg.com/raf/-/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==
  dependencies:
    performance-now "^2.1.0"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/randomfill/download/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://registry.npm.taobao.org/raw-body/download/raw-body-2.4.0.tgz#a1ce6fb9c9bc356ca52e89256ab59059e13d0332"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-7.0.1.tgz#f3a6135758459733ae2b95638056e1854e7ef507"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.1.1, read-pkg@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.1.1, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~1.0.33-1:
  version "1.0.34"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  integrity sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@~1.1.9:
  version "1.1.14"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-1.1.14.tgz#7cf4c54ef648e3813084c636dd2079e166c081d9"
  integrity sha1-fPTFTvZI44EwhMY23SB54WbAgdk=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@~2.0.0:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.0.6.tgz#8f90341e68a53ccc928788dacfcd11b36eb9b78e"
  integrity sha1-j5A0HmilPMySh4jaz80Rs265t44=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    string_decoder "~0.10.x"
    util-deprecate "~1.0.1"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/readdirp/download/readdirp-2.2.1.tgz?cache=0&sync_timestamp=1602584394621&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.5.0:
  version "3.5.0"
  resolved "https://registry.npm.taobao.org/readdirp/download/readdirp-3.5.0.tgz?cache=0&sync_timestamp=1602584394621&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Freaddirp%2Fdownload%2Freaddirp-3.5.0.tgz#9ba74c019b15d365278d2e91bb8c48d7b4d42c9e"
  integrity sha1-m6dMAZsV02UnjS6Ru4xI17TULJ4=
  dependencies:
    picomatch "^2.2.1"

redent@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/redent/download/redent-3.0.0.tgz#e557b7998316bb53c9f1f56fa626352c6963059f"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

regenerate-unicode-properties@^8.2.0:
  version "8.2.0"
  resolved "https://registry.npm.taobao.org/regenerate-unicode-properties/download/regenerate-unicode-properties-8.2.0.tgz#e5de7111d655e7ba60c057dbe9ff37c87e65cdec"
  integrity sha1-5d5xEdZV57pgwFfb6f83yH5lzew=
  dependencies:
    regenerate "^1.4.0"

regenerate@^1.4.0:
  version "1.4.2"
  resolved "https://registry.npm.taobao.org/regenerate/download/regenerate-1.4.2.tgz?cache=0&sync_timestamp=1604220147658&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerate%2Fdownload%2Fregenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.4:
  version "0.13.7"
  resolved "https://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.13.7.tgz#cac2dacc8a1ea675feaabaeb8ae833898ae46f55"
  integrity sha1-ysLazIoepnX+qrrriugziYrkb1U=

regenerator-transform@^0.14.2:
  version "0.14.5"
  resolved "https://registry.npm.taobao.org/regenerator-transform/download/regenerator-transform-0.14.5.tgz#c98da154683671c9c4dcb16ece736517e1b7feb4"
  integrity sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz?cache=0&sync_timestamp=1610725764337&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexp.prototype.flags%2Fdownload%2Fregexp.prototype.flags-1.3.1.tgz#7ef352ae8d159e758c0eadca6f8fcb4eef07be26"
  integrity sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

regexpp@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/regexpp/download/regexpp-3.1.0.tgz#206d0ad0a5648cffbdb8ae46438f3dc51c9f78e2"
  integrity sha1-IG0K0KVkjP+9uK5GQ489xRyfeOI=

regexpu-core@^4.7.1:
  version "4.7.1"
  resolved "https://registry.npm.taobao.org/regexpu-core/download/regexpu-core-4.7.1.tgz?cache=0&sync_timestamp=1600413542312&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregexpu-core%2Fdownload%2Fregexpu-core-4.7.1.tgz#2dea5a9a07233298fbf0db91fa9abc4c6e0f8ad6"
  integrity sha1-LepamgcjMpj78NuR+pq8TG4PitY=
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.2.0"
    regjsgen "^0.5.1"
    regjsparser "^0.6.4"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.2.0"

regjsgen@^0.5.1:
  version "0.5.2"
  resolved "https://registry.npm.taobao.org/regjsgen/download/regjsgen-0.5.2.tgz#92ff295fb1deecbf6ecdab2543d207e91aa33733"
  integrity sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=

regjsparser@^0.6.4:
  version "0.6.6"
  resolved "https://registry.npm.taobao.org/regjsparser/download/regjsparser-0.6.6.tgz?cache=0&sync_timestamp=1610298992031&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregjsparser%2Fdownload%2Fregjsparser-0.6.6.tgz#6d8c939d1a654f78859b08ddcc4aa777f3fa800a"
  integrity sha1-bYyTnRplT3iFmwjdzEqnd/P6gAo=
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x, relateurl@^0.2.7:
  version "0.2.7"
  resolved "https://registry.npm.taobao.org/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remark-parse@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npm.taobao.org/remark-parse/download/remark-parse-9.0.0.tgz?cache=0&sync_timestamp=1602663872721&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fremark-parse%2Fdownload%2Fremark-parse-9.0.0.tgz#4d20a299665880e4f4af5d90b7c7b8a935853640"
  integrity sha1-TSCimWZYgOT0r12Qt8e4qTWFNkA=
  dependencies:
    mdast-util-from-markdown "^0.8.0"

remark-stringify@^9.0.0:
  version "9.0.1"
  resolved "https://registry.npm.taobao.org/remark-stringify/download/remark-stringify-9.0.1.tgz?cache=0&sync_timestamp=1607537255983&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fremark-stringify%2Fdownload%2Fremark-stringify-9.0.1.tgz#576d06e910548b0a7191a71f27b33f1218862894"
  integrity sha1-V20G6RBUiwpxkacfJ7M/EhiGKJQ=
  dependencies:
    mdast-util-to-markdown "^0.6.0"

remark@^13.0.0:
  version "13.0.0"
  resolved "https://registry.npm.taobao.org/remark/download/remark-13.0.0.tgz#d15d9bf71a402f40287ebe36067b66d54868e425"
  integrity sha1-0V2b9xpAL0Aofr42Bntm1Uho5CU=
  dependencies:
    remark-parse "^9.0.0"
    remark-stringify "^9.0.0"
    unified "^9.1.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.4:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/renderkid/download/renderkid-2.0.5.tgz?cache=0&sync_timestamp=1609588553625&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frenderkid%2Fdownload%2Frenderkid-2.0.5.tgz#483b1ac59c6601ab30a7a596a5965cabccfdd0a5"
  integrity sha1-SDsaxZxmAaswp6WWpZZcq8z90KU=
  dependencies:
    css-select "^2.0.2"
    dom-converter "^0.2"
    htmlparser2 "^3.10.1"
    lodash "^4.17.20"
    strip-ansi "^3.0.0"

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.0.0, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

replace-ext@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/replace-ext/download/replace-ext-0.0.1.tgz#29bbd92078a739f0bcce2b4ee41e837953522924"
  integrity sha1-KbvZIHinOfC8zitO5B6DeVNSKSQ=

request@^2.44.0, request@^2.88.2:
  version "2.88.2"
  resolved "https://registry.npm.taobao.org/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-observer-polyfill@^1.5.0, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/resolve-cwd/download/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.10.0, resolve@^1.12.0, resolve@^1.17.0:
  version "1.19.0"
  resolved "https://registry.npm.taobao.org/resolve/download/resolve-1.19.0.tgz#1af5bf630409734a067cae29318aac7fa29a267c"
  integrity sha1-GvW/YwQJc0oGfK4pMYqsf6KaJnw=
  dependencies:
    is-core-module "^2.1.0"
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz?cache=0&sync_timestamp=1609748476869&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fret%2Fdownload%2Fret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npm.taobao.org/retry/download/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/rgb-regex/download/rgb-regex-1.0.1.tgz#c0e0d6882df0e23be254a475e8edd41915feaeb1"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/rgba-regex/download/rgba-regex-1.0.0.tgz#43374e2e2ca0968b0ef1523460b7d730ff22eeb3"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.3, rimraf@^2.7.1:
  version "2.7.1"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/ripemd160/download/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npm.taobao.org/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.1.10"
  resolved "https://registry.npm.taobao.org/run-parallel/download/run-parallel-1.1.10.tgz?cache=0&sync_timestamp=1603768269871&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frun-parallel%2Fdownload%2Frun-parallel-1.1.10.tgz#60a51b2ae836636c81377df16cb107351bcd13ef"
  integrity sha1-YKUbKug2Y2yBN33xbLEHNRvNE+8=

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.6.0:
  version "6.6.3"
  resolved "https://registry.npm.taobao.org/rxjs/download/rxjs-6.6.3.tgz?cache=0&sync_timestamp=1610927507398&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frxjs%2Fdownload%2Frxjs-6.6.3.tgz#8ca84635c4daa900c0d3967a6ee7ac60271ee552"
  integrity sha1-jKhGNcTaqQDA05Z6buesYCce5VI=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-loader@^10.0.4:
  version "10.1.1"
  resolved "https://registry.npm.taobao.org/sass-loader/download/sass-loader-10.1.1.tgz?cache=0&sync_timestamp=1610373188078&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsass-loader%2Fdownload%2Fsass-loader-10.1.1.tgz#4ddd5a3d7638e7949065dd6e9c7c04037f7e663d"
  integrity sha1-Td1aPXY455SQZd1unHwEA39+Zj0=
  dependencies:
    klona "^2.0.4"
    loader-utils "^2.0.0"
    neo-async "^2.6.2"
    schema-utils "^3.0.0"
    semver "^7.3.2"

sass-resources-loader@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/sass-resources-loader/download/sass-resources-loader-2.1.1.tgz#a231b7c4e326d9c8d141909c901233bef9453151"
  integrity sha1-ojG3xOMm2cjRQZCckBIzvvlFMVE=
  dependencies:
    async "^3.2.0"
    chalk "^4.1.0"
    glob "^7.1.6"
    loader-utils "^2.0.0"

sass@^1.28.0:
  version "1.32.5"
  resolved "https://registry.npm.taobao.org/sass/download/sass-1.32.5.tgz#2882d22ad5748c05fa9bff6c3b0ffbc4f4b9e1dc"
  integrity sha1-KILSKtV0jAX6m/9sOw/7xPS54dw=
  dependencies:
    chokidar ">=2.0.0 <4.0.0"

save-pixels@~2.3.0:
  version "2.3.4"
  resolved "https://registry.npm.taobao.org/save-pixels/download/save-pixels-2.3.4.tgz#49d349c06b8d7c0127dbf0da24b44aca5afb59fe"
  integrity sha1-SdNJwGuNfAEn2/DaJLRKylr7Wf4=
  dependencies:
    contentstream "^1.0.0"
    gif-encoder "~0.4.1"
    jpeg-js "0.0.4"
    ndarray "^1.0.18"
    ndarray-ops "^1.2.2"
    pngjs-nozlib "^1.0.0"
    through "^2.3.4"

sax@~1.2.4:
  version "1.2.4"
  resolved "https://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz?cache=0&sync_timestamp=1601922425223&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.0.0, schema-utils@^2.5.0, schema-utils@^2.6.5, schema-utils@^2.6.6, schema-utils@^2.7.0:
  version "2.7.1"
  resolved "https://registry.npm.taobao.org/schema-utils/download/schema-utils-2.7.1.tgz?cache=0&sync_timestamp=1601922425223&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.1.tgz#1ca4f32d1b24c590c203b8e7a50bf0ea4cd394d7"
  integrity sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/schema-utils/download/schema-utils-3.0.0.tgz?cache=0&sync_timestamp=1601922425223&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fschema-utils%2Fdownload%2Fschema-utils-3.0.0.tgz#67502f6aa2b66a2d4032b4279a2944978a0913ef"
  integrity sha1-Z1AvaqK2ai1AMrQnmilEl4oJE+8=
  dependencies:
    "@types/json-schema" "^7.0.6"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

screenfull@^5.0.2:
  version "5.1.0"
  resolved "https://registry.npm.taobao.org/screenfull/download/screenfull-5.1.0.tgz?cache=0&sync_timestamp=1608796373175&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fscreenfull%2Fdownload%2Fscreenfull-5.1.0.tgz#85c13c70f4ead4c1b8a935c70010dfdcd2c0e5c8"
  integrity sha1-hcE8cPTq1MG4qTXHABDf3NLA5cg=

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/select-hose/download/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

select@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/select/download/select-1.1.2.tgz#0e7350acdec80b1108528786ec1d4418d11b396d"
  integrity sha1-DnNQrN7ICxEIUoeG7B1EGNEbOW0=

selfsigned@^1.10.8:
  version "1.10.8"
  resolved "https://registry.npm.taobao.org/selfsigned/download/selfsigned-1.10.8.tgz?cache=0&sync_timestamp=1600187989135&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fselfsigned%2Fdownload%2Fselfsigned-1.10.8.tgz#0d17208b7d12c33f8eac85c41835f27fc3d81a30"
  integrity sha1-DRcgi30Swz+OrIXEGDXyf8PYGjA=
  dependencies:
    node-forge "^0.10.0"

"semver@2 || 3 || 4 || 5", semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.1"
  resolved "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1606854493763&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/semver/download/semver-7.0.0.tgz?cache=0&sync_timestamp=1606854493763&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-7.0.0.tgz#5f3ca35761e47e05b206c6daff2cf814f0316b8e"
  integrity sha1-XzyjV2HkfgWyBsba/yz4FPAxa44=

semver@^6.0.0, semver@^6.1.0, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1606854493763&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.2.1, semver@^7.3.2:
  version "7.3.4"
  resolved "https://registry.npm.taobao.org/semver/download/semver-7.3.4.tgz?cache=0&sync_timestamp=1606854493763&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-7.3.4.tgz#27aaa7d2e4ca76452f98d3add093a72c943edc97"
  integrity sha1-J6qn0uTKdkUvmNOt0JOnLJQ+3Jc=
  dependencies:
    lru-cache "^6.0.0"

semver@~5.0.3:
  version "5.0.3"
  resolved "https://registry.npm.taobao.org/semver/download/semver-5.0.3.tgz?cache=0&sync_timestamp=1606854493763&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.0.3.tgz#77466de589cd5d3c95f138aa78bc569a3cb5d27a"
  integrity sha1-d0Zt5YnNXTyV8TiqeLxWmjy10no=

send@0.17.1:
  version "0.17.1"
  resolved "https://registry.npm.taobao.org/send/download/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/serialize-javascript/download/serialize-javascript-4.0.0.tgz?cache=0&sync_timestamp=1599741180858&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-4.0.0.tgz#b525e1238489a5ecfc42afacc3fe99e666f4b1aa"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

serialize-javascript@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.taobao.org/serialize-javascript/download/serialize-javascript-5.0.1.tgz?cache=0&sync_timestamp=1599741180858&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserialize-javascript%2Fdownload%2Fserialize-javascript-5.0.1.tgz#7886ec848049a462467a97d3d918ebb2aaf934f4"
  integrity sha1-eIbshIBJpGJGepfT2Rjrsqr5NPQ=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npm.taobao.org/serve-index/download/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://registry.npm.taobao.org/serve-static/download/serve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-equal@^1.0.0:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/shallow-equal/-/shallow-equal-1.2.1.tgz#4c16abfa56043aa20d050324efa68940b0da79da"
  integrity sha512-S4vJDjHHMBaiZuT9NPb616CSmLf618jawtv3sufLl6ivK8WocjAo58cXwbRV1cgqxH0Qbv+iUt6m05eqEa2IRA==

shallowequal@^1.0.2:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/shallowequal/-/shallowequal-1.1.0.tgz#188d521de95b9087404fd4dcb68b13df0ae4e7f8"
  integrity sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.6.1:
  version "1.7.2"
  resolved "https://registry.npm.taobao.org/shell-quote/download/shell-quote-1.7.2.tgz#67a7d02c76c9da24f99d20808fcaded0e0e04be2"
  integrity sha1-Z6fQLHbJ2iT5nSCAj8re0ODgS+I=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.3.tgz?cache=0&sync_timestamp=1592843131591&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.3.tgz#a1410c2edd8f077b08b4e253c8eacfcaf057461c"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npm.taobao.org/simple-swizzle/download/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/sockjs-client/download/sockjs-client-1.5.0.tgz#2f8ff5d4b659e0d092f7aba0b7c386bd2aa20add"
  integrity sha1-L4/11LZZ4NCS96ugt8OGvSqiCt0=
  dependencies:
    debug "^3.2.6"
    eventsource "^1.0.7"
    faye-websocket "^0.11.3"
    inherits "^2.0.4"
    json3 "^3.3.3"
    url-parse "^1.4.7"

sockjs@^0.3.21:
  version "0.3.21"
  resolved "https://registry.npm.taobao.org/sockjs/download/sockjs-0.3.21.tgz#b34ffb98e796930b60a0cfa11904d6a339a7d417"
  integrity sha1-s0/7mOeWkwtgoM+hGQTWozmn1Bc=
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^3.4.0"
    websocket-driver "^0.7.4"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/sort-keys/download/sort-keys-1.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsort-keys%2Fdownload%2Fsort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.1.tgz#3993bd873bfc48479cca9ea3a547835c7c154b34"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@~0.5.12:
  version "0.5.19"
  resolved "https://registry.npm.taobao.org/source-map-support/download/source-map-support-0.5.19.tgz#a98b62f86dcaf4f67399648c085291ab9e8fed61"
  integrity sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.7"
  resolved "https://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.3"
  resolved "https://registry.npm.taobao.org/source-map/download/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.7"
  resolved "https://registry.npm.taobao.org/spdx-license-ids/download/spdx-license-ids-3.0.7.tgz?cache=0&sync_timestamp=1606610735791&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.7.tgz#e9c18a410e5ed7e12442a549fbd8afa767038d65"
  integrity sha1-6cGKQQ5e1+EkQqVJ+9ivp2cDjWU=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/spdy-transport/download/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/spdy/download/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

specificity@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/specificity/download/specificity-0.4.1.tgz#aab5e645012db08ba182e151165738d00887b019"
  integrity sha1-qrXmRQEtsIuhguFRFlc40AiHsBk=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@^1.0.3:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.1.2.tgz#da1765262bf8c0f571749f2ad6c26300207ae673"
  integrity sha1-2hdlJiv4wPVxdJ8q1sJjACB65nM=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

spritesheet-templates@^10.4.2:
  version "10.5.0"
  resolved "https://registry.npm.taobao.org/spritesheet-templates/download/spritesheet-templates-10.5.0.tgz#24a8269a37d162d4a7e5ce48338f5b3e7b5d56e3"
  integrity sha1-JKgmmjfRYtSn5c5IM49bPntdVuM=
  dependencies:
    handlebars "^4.6.0"
    handlebars-layouts "^3.1.4"
    json-content-demux "~0.1.2"
    underscore "~1.4.2"
    underscore.string "~3.3.0"

spritesmith@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npm.taobao.org/spritesmith/download/spritesmith-3.4.0.tgz#5cfa004278c9072ab4900a0cd800141c7cf296e2"
  integrity sha1-XPoAQnjJByq0kAoM2AAUHHzyluI=
  dependencies:
    concat-stream "~1.5.1"
    layout "~2.2.0"
    pixelsmith "^2.3.0"
    semver "~5.0.3"
    through2 "~2.0.0"

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz#fb661c0bef29b39db40769ee39fa70093d6f6877"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npm.taobao.org/ssri/download/ssri-6.0.1.tgz#2a3c41b28dd45b62b63676ecb74001265ae9edd8"
  integrity sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg=
  dependencies:
    figgy-pudding "^3.5.1"

ssri@^7.0.0, ssri@^7.1.0:
  version "7.1.0"
  resolved "https://registry.npm.taobao.org/ssri/download/ssri-7.1.0.tgz#92c241bf6de82365b5c7fb4bd76e975522e1294d"
  integrity sha1-ksJBv23oI2W1x/tL126XVSLhKU0=
  dependencies:
    figgy-pudding "^3.5.1"
    minipass "^3.1.1"

ssri@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npm.taobao.org/ssri/download/ssri-8.0.0.tgz#79ca74e21f8ceaeddfcb4b90143c458b8d988808"
  integrity sha1-ecp04h+M6u3fy0uQFDxFi42YiAg=
  dependencies:
    minipass "^3.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npm.taobao.org/stable/download/stable-0.1.8.tgz#836eb3c8382fe2936feaf544631017ce7d47a3cf"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stackframe@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/stackframe/download/stackframe-1.2.0.tgz#52429492d63c62eb989804c11552e3d22e779303"
  integrity sha1-UkKUktY8YuuYmATBFVLj0i53kwM=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1609654090567&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/stream-each/download/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.1.tgz#d7088281559ab2778424279b0877da3c392d5a3d"
  integrity sha1-1wiCgVWasneEJCebCHfaPDktWj0=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string-convert@^0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/string-convert/-/string-convert-0.2.1.tgz#6982cc3049fbb4cd85f8b24568b9d9bf39eeff97"
  integrity sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=

string-width@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.trimend@^1.0.1, string.prototype.trimend@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/string.prototype.trimend/download/string.prototype.trimend-1.0.3.tgz?cache=0&sync_timestamp=1606008069015&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimend%2Fdownload%2Fstring.prototype.trimend-1.0.3.tgz#a22bd53cca5c7cf44d7c9d5c732118873d6cd18b"
  integrity sha1-oivVPMpcfPRNfJ1ccyEYhz1s0Ys=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

string.prototype.trimstart@^1.0.1, string.prototype.trimstart@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/string.prototype.trimstart/download/string.prototype.trimstart-1.0.3.tgz?cache=0&sync_timestamp=1606007944322&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstring.prototype.trimstart%2Fdownload%2Fstring.prototype.trimstart-1.0.3.tgz#9b4cb590e123bb36564401d59824298de50fd5aa"
  integrity sha1-m0y1kOEjuzZWRAHVmCQpjeUP1ao=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.npm.taobao.org/string_decoder/download/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5, strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-6.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-bom-stream@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/strip-bom-stream/download/strip-bom-stream-1.0.0.tgz#e7144398577d51a6bed0fa1994fa05f43fd988ee"
  integrity sha1-5xRDmFd9Uaa+0PoZlPoF9D/ZiO4=
  dependencies:
    first-chunk-stream "^1.0.0"
    strip-bom "^2.0.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/strip-indent/download/strip-indent-2.0.0.tgz?cache=0&sync_timestamp=1602731441175&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-indent%2Fdownload%2Fstrip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/strip-indent/download/strip-indent-3.0.0.tgz?cache=0&sync_timestamp=1602731441175&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-indent%2Fdownload%2Fstrip-indent-3.0.0.tgz#c32e1cee940b6b3432c771bc2c54bcce73cd3001"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-search@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/style-search/download/style-search-0.1.0.tgz#7958c793e47e32e07d2b5cafe5c0bf8e12e77902"
  integrity sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/stylehacks/download/stylehacks-4.0.3.tgz?cache=0&sync_timestamp=1610386868786&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstylehacks%2Fdownload%2Fstylehacks-4.0.3.tgz#6718fcaf4d1e07d8a1318690881e8d96726a71d5"
  integrity sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

stylelint-codeframe-formatter@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/stylelint-codeframe-formatter/download/stylelint-codeframe-formatter-1.0.4.tgz#08580f995dab8ce6db100002fd813715abaefffc"
  integrity sha1-CFgPmV2rjObbEAAC/YE3Fauu//w=
  dependencies:
    babel-code-frame "^6.26.0"
    chalk "^2.3.0"
    log-symbols "^2.1.0"
    plur "^2.1.2"

stylelint-config-recommended-scss@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/stylelint-config-recommended-scss/download/stylelint-config-recommended-scss-4.2.0.tgz#3ad3fc858215cfd16a0f90aecf1ac0ea8a3e6971"
  integrity sha1-OtP8hYIVz9FqD5CuzxrA6oo+aXE=
  dependencies:
    stylelint-config-recommended "^3.0.0"

stylelint-config-recommended@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/stylelint-config-recommended/download/stylelint-config-recommended-3.0.0.tgz#e0e547434016c5539fe2650afd58049a2fd1d657"
  integrity sha1-4OVHQ0AWxVOf4mUK/VgEmi/R1lc=

stylelint-config-standard@^20.0.0:
  version "20.0.0"
  resolved "https://registry.npm.taobao.org/stylelint-config-standard/download/stylelint-config-standard-20.0.0.tgz#06135090c9e064befee3d594289f50e295b5e20d"
  integrity sha1-BhNQkMngZL7+49WUKJ9Q4pW14g0=
  dependencies:
    stylelint-config-recommended "^3.0.0"

stylelint-scss@^3.18.0:
  version "3.18.0"
  resolved "https://registry.npm.taobao.org/stylelint-scss/download/stylelint-scss-3.18.0.tgz#8f06371c223909bf3f62e839548af1badeed31e9"
  integrity sha1-jwY3HCI5Cb8/Yug5VIrxut7tMek=
  dependencies:
    lodash "^4.17.15"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

stylelint-webpack-plugin@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/stylelint-webpack-plugin/download/stylelint-webpack-plugin-2.1.1.tgz#1c8ae72a4e5818f7e5925e3fff10502ad34a52a5"
  integrity sha1-HIrnKk5YGPflkl4//xBQKtNKUqU=
  dependencies:
    arrify "^2.0.1"
    micromatch "^4.0.2"
    schema-utils "^3.0.0"

stylelint@^13.6.1, stylelint@^13.7.2:
  version "13.9.0"
  resolved "https://registry.npm.taobao.org/stylelint/download/stylelint-13.9.0.tgz#93921ee6e11d4556b9f31131f485dc813b68e32a"
  integrity sha1-k5Ie5uEdRVa58xEx9IXcgTto4yo=
  dependencies:
    "@stylelint/postcss-css-in-js" "^0.37.2"
    "@stylelint/postcss-markdown" "^0.36.2"
    autoprefixer "^9.8.6"
    balanced-match "^1.0.0"
    chalk "^4.1.0"
    cosmiconfig "^7.0.0"
    debug "^4.3.1"
    execall "^2.0.0"
    fast-glob "^3.2.5"
    fastest-levenshtein "^1.0.12"
    file-entry-cache "^6.0.0"
    get-stdin "^8.0.0"
    global-modules "^2.0.0"
    globby "^11.0.2"
    globjoin "^0.1.4"
    html-tags "^3.1.0"
    ignore "^5.1.8"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    known-css-properties "^0.20.0"
    lodash "^4.17.20"
    log-symbols "^4.0.0"
    mathml-tag-names "^2.1.3"
    meow "^9.0.0"
    micromatch "^4.0.2"
    normalize-selector "^0.2.0"
    postcss "^7.0.35"
    postcss-html "^0.36.0"
    postcss-less "^3.1.4"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^4.0.2"
    postcss-sass "^0.4.4"
    postcss-scss "^2.1.1"
    postcss-selector-parser "^6.0.4"
    postcss-syntax "^0.36.2"
    postcss-value-parser "^4.1.0"
    resolve-from "^5.0.0"
    slash "^3.0.0"
    specificity "^0.4.1"
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    style-search "^0.1.0"
    sugarss "^2.0.0"
    svg-tags "^1.0.0"
    table "^6.0.7"
    v8-compile-cache "^2.2.0"
    write-file-atomic "^3.0.3"

sugarss@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/sugarss/download/sugarss-2.0.0.tgz?cache=0&sync_timestamp=1604595237833&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsugarss%2Fdownload%2Fsugarss-2.0.0.tgz#ddd76e0124b297d40bf3cca31c8b22ecb43bc61d"
  integrity sha1-3dduASSyl9QL88yjHIsi7LQ7xh0=
  dependencies:
    postcss "^7.0.2"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

svg-baker-runtime@^1.4.7:
  version "1.4.7"
  resolved "https://registry.npm.taobao.org/svg-baker-runtime/download/svg-baker-runtime-1.4.7.tgz#f4720637f5b6202eef6378d81f1fead0815f8a4e"
  integrity sha1-9HIGN/W2IC7vY3jYHx/q0IFfik4=
  dependencies:
    deepmerge "1.3.2"
    mitt "1.1.2"
    svg-baker "^1.7.0"

svg-baker@^1.5.0, svg-baker@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npm.taobao.org/svg-baker/download/svg-baker-1.7.0.tgz#8367f78d875550c52fe4756f7303d5c5d7c2e9a7"
  integrity sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac=
  dependencies:
    bluebird "^3.5.0"
    clone "^2.1.1"
    he "^1.1.1"
    image-size "^0.5.1"
    loader-utils "^1.1.0"
    merge-options "1.0.1"
    micromatch "3.1.0"
    postcss "^5.2.17"
    postcss-prefix-selector "^1.6.0"
    posthtml-rename-id "^1.0"
    posthtml-svg-mode "^1.0.3"
    query-string "^4.3.2"
    traverse "^0.6.6"

svg-sprite-loader@^5.0.0:
  version "5.2.1"
  resolved "https://registry.npm.taobao.org/svg-sprite-loader/download/svg-sprite-loader-5.2.1.tgz?cache=0&sync_timestamp=1608019595598&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsvg-sprite-loader%2Fdownload%2Fsvg-sprite-loader-5.2.1.tgz#f0c37e76665ddfdf7abc8ff6ebd1da35cdd212d9"
  integrity sha1-8MN+dmZd3996vI/269HaNc3SEtk=
  dependencies:
    bluebird "^3.5.0"
    deepmerge "1.3.2"
    domready "1.0.8"
    escape-string-regexp "1.0.5"
    loader-utils "^1.1.0"
    svg-baker "^1.5.0"
    svg-baker-runtime "^1.4.7"
    url-slug "2.0.0"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/svg-tags/download/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

svgo@^1.0.0, svgo@^1.3.0:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/svgo/download/svgo-1.3.2.tgz#b6dc511c063346c9e415b81e43401145b96d4167"
  integrity sha1-ttxRHAYzRsnkFbgeQ0ARRbltQWc=
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

table@^6.0.4, table@^6.0.7:
  version "6.0.7"
  resolved "https://registry.npm.taobao.org/table/download/table-6.0.7.tgz?cache=0&sync_timestamp=1609732678448&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftable%2Fdownload%2Ftable-6.0.7.tgz#e45897ffbcc1bcf9e8a87bf420f2c9e5a7a52a34"
  integrity sha1-5FiX/7zBvPnoqHv0IPLJ5aelKjQ=
  dependencies:
    ajv "^7.0.2"
    lodash "^4.17.20"
    slice-ansi "^4.0.0"
    string-width "^4.2.0"

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/tapable/download/tapable-1.1.3.tgz?cache=0&sync_timestamp=1607088902003&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftapable%2Fdownload%2Ftapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tar@^6.0.2:
  version "6.1.0"
  resolved "https://registry.npm.taobao.org/tar/download/tar-6.1.0.tgz#d1724e9bcc04b977b18d5c573b333a2207229a83"
  integrity sha1-0XJOm8wEuXexjVxXOzM6IgcimoM=
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^3.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

terser-webpack-plugin@^1.4.3:
  version "1.4.5"
  resolved "https://registry.npm.taobao.org/terser-webpack-plugin/download/terser-webpack-plugin-1.4.5.tgz?cache=0&sync_timestamp=1610194199773&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-1.4.5.tgz#a217aefaea330e734ffacb6120ec1fa312d6040b"
  integrity sha1-oheu+uozDnNP+sthIOwfoxLWBAs=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser-webpack-plugin@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npm.taobao.org/terser-webpack-plugin/download/terser-webpack-plugin-2.3.8.tgz?cache=0&sync_timestamp=1610194199773&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-2.3.8.tgz#894764a19b0743f2f704e7c2a848c5283a696724"
  integrity sha1-iUdkoZsHQ/L3BOfCqEjFKDppZyQ=
  dependencies:
    cacache "^13.0.1"
    find-cache-dir "^3.3.1"
    jest-worker "^25.4.0"
    p-limit "^2.3.0"
    schema-utils "^2.6.6"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.6.12"
    webpack-sources "^1.4.3"

terser@^4.1.2, terser@^4.6.12, terser@^4.6.3:
  version "4.8.0"
  resolved "https://registry.npm.taobao.org/terser/download/terser-4.8.0.tgz#63056343d7c70bb29f3af665865a46fe03a0df17"
  integrity sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npm.taobao.org/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npm.taobao.org/thenify/download/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thread-loader@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npm.taobao.org/thread-loader/download/thread-loader-2.1.3.tgz?cache=0&sync_timestamp=1603809365048&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthread-loader%2Fdownload%2Fthread-loader-2.1.3.tgz#cbd2c139fc2b2de6e9d28f62286ab770c1acbdda"
  integrity sha1-y9LBOfwrLebp0o9iKGq3cMGsvdo=
  dependencies:
    loader-runner "^2.3.1"
    loader-utils "^1.1.0"
    neo-async "^2.6.0"

throttle-debounce@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/throttle-debounce/download/throttle-debounce-1.1.0.tgz?cache=0&sync_timestamp=1604313880785&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fthrottle-debounce%2Fdownload%2Fthrottle-debounce-1.1.0.tgz#51853da37be68a155cb6e827b3514a3c422e89cd"
  integrity sha1-UYU9o3vmihVctugns1FKPEIuic0=

through2@^2.0.0, through2@~2.0.0:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.4, through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npm.taobao.org/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/thunky/download/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "https://registry.npm.taobao.org/timers-browserify/download/timers-browserify-2.0.12.tgz?cache=0&sync_timestamp=1603793667345&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftimers-browserify%2Fdownload%2Ftimers-browserify-2.0.12.tgz#44a45c11fbf407f34f97bccd1577c652361b00ee"
  integrity sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/timsort/download/timsort-0.3.0.tgz#405411a8e7e6339fe64db9a234de11dc31e02bd4"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tiny-emitter@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/tiny-emitter/download/tiny-emitter-2.1.0.tgz#1d1a56edfc51c43e863cbb5382a72330e3555423"
  integrity sha1-HRpW7fxRxD6GPLtTgqcjMONVVCM=

tinycolor2@^1.4.1:
  version "1.4.2"
  resolved "https://registry.yarnpkg.com/tinycolor2/-/tinycolor2-1.4.2.tgz#3f6a4d1071ad07676d7fa472e1fac40a719d8803"
  integrity sha512-vJhccZPs965sV/L2sU4oRQVAos0pQXwsvTLkWYdqJ+a8Q5kPFzJTuOFwy7UniPli44NKQGAglksjvOcpo95aZA==

tinymce@^5.5.1:
  version "5.6.2"
  resolved "https://registry.npm.taobao.org/tinymce/download/tinymce-5.6.2.tgz#4c20c22e328c5d0baab204928dbc2b0ecbed18b4"
  integrity sha1-TCDCLjKMXQuqsgSSjbwrDsvtGLQ=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz?cache=0&sync_timestamp=1592843137359&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftmp%2Fdownload%2Ftmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.taobao.org/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/toidentifier/download/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz#2e68442d9f64ec720b8cc89e6443ac6caa950029"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

traverse@^0.6.6:
  version "0.6.6"
  resolved "https://registry.npm.taobao.org/traverse/download/traverse-0.6.6.tgz#cbdf560fd7b9af632502fed40f918c157ea97137"
  integrity sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc=

trim-newlines@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/trim-newlines/download/trim-newlines-3.0.0.tgz#79726304a6a898aa8373427298d54c2ee8b1cb30"
  integrity sha1-eXJjBKaomKqDc0JymNVMLuixyzA=

trough@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/trough/download/trough-1.0.5.tgz#b8b639cefad7d0bb2abd37d433ff8293efa5f406"
  integrity sha1-uLY5zvrX0LsqvTfUM/+Ck++l9AY=

tryer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/tryer/download/tryer-1.0.1.tgz#f2c85406800b9b0f74c9f7465b81eaad241252f8"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

ts-pnp@^1.1.6:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/ts-pnp/download/ts-pnp-1.2.0.tgz#a500ad084b0798f1c3071af391e65912c86bca92"
  integrity sha1-pQCtCEsHmPHDBxrzkeZZEshrypI=

tslib@^1.9.0:
  version "1.14.1"
  resolved "https://registry.npm.taobao.org/tslib/download/tslib-1.14.1.tgz?cache=0&sync_timestamp=1609887446826&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftslib%2Fdownload%2Ftslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.3:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/tslib/download/tslib-2.1.0.tgz?cache=0&sync_timestamp=1609887446826&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftslib%2Fdownload%2Ftslib-2.1.0.tgz#da60860f1c2ecaa5703ab7d39bc05b6bf988b97a"
  integrity sha1-2mCGDxwuyqVwOrfTm8Bba/mIuXo=

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npm.taobao.org/type-fest/download/type-fest-0.11.0.tgz?cache=0&sync_timestamp=1606468899313&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftype-fest%2Fdownload%2Ftype-fest-0.11.0.tgz#97abf0872310fed88a5c466b25681576145e33f1"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

type-fest@^0.18.0:
  version "0.18.1"
  resolved "https://registry.npm.taobao.org/type-fest/download/type-fest-0.18.1.tgz?cache=0&sync_timestamp=1606468899313&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftype-fest%2Fdownload%2Ftype-fest-0.18.1.tgz#db4bc151a4a2cf4eebf9add5db75508db6cc841f"
  integrity sha1-20vBUaSiz07r+a3V23VQjbbMhB8=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npm.taobao.org/type-fest/download/type-fest-0.6.0.tgz?cache=0&sync_timestamp=1606468899313&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftype-fest%2Fdownload%2Ftype-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npm.taobao.org/type-fest/download/type-fest-0.8.1.tgz?cache=0&sync_timestamp=1606468899313&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftype-fest%2Fdownload%2Ftype-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npm.taobao.org/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npm.taobao.org/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6, typedarray@~0.0.5:
  version "0.0.6"
  resolved "https://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://registry.npm.taobao.org/uglify-js/download/uglify-js-3.4.10.tgz?cache=0&sync_timestamp=1611099910487&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuglify-js%2Fdownload%2Fuglify-js-3.4.10.tgz#9ad9563d8eb3acdfb8d38597d2af1d815f6a755f"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

uglify-js@^3.1.4:
  version "3.12.5"
  resolved "https://registry.npm.taobao.org/uglify-js/download/uglify-js-3.12.5.tgz?cache=0&sync_timestamp=1611099910487&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuglify-js%2Fdownload%2Fuglify-js-3.12.5.tgz#83241496087c640efe9dfc934832e71725aba008"
  integrity sha1-gyQUlgh8ZA7+nfyTSDLnFyWroAg=

underscore.string@~3.3.0:
  version "3.3.5"
  resolved "https://registry.npm.taobao.org/underscore.string/download/underscore.string-3.3.5.tgz#fc2ad255b8bd309e239cbc5816fd23a9b7ea4023"
  integrity sha1-/CrSVbi9MJ4jnLxYFv0jqbfqQCM=
  dependencies:
    sprintf-js "^1.0.3"
    util-deprecate "^1.0.2"

underscore@~1.4.2:
  version "1.4.4"
  resolved "https://registry.npm.taobao.org/underscore/download/underscore-1.4.4.tgz?cache=0&sync_timestamp=1606179555203&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funderscore%2Fdownload%2Funderscore-1.4.4.tgz#61a6a32010622afa07963bf325203cf12239d604"
  integrity sha1-YaajIBBiKvoHljvzJSA88SI51gQ=

unicode-canonical-property-names-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz#2619800c4c825800efdd8343af7dd9933cbe2818"
  integrity sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=

unicode-match-property-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz#8ed2a32569961bce9227d09cd3ffbb8fed5f020c"
  integrity sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=
  dependencies:
    unicode-canonical-property-names-ecmascript "^1.0.4"
    unicode-property-aliases-ecmascript "^1.0.4"

unicode-match-property-value-ecmascript@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.2.0.tgz#0d91f600eeeb3096aa962b1d6fc88876e64ea531"
  integrity sha1-DZH2AO7rMJaqlisdb8iIduZOpTE=

unicode-property-aliases-ecmascript@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.1.0.tgz#dd57a99f6207bedff4628abefb94c50db941c8f4"
  integrity sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ=

unidecode@0.1.8:
  version "0.1.8"
  resolved "https://registry.npm.taobao.org/unidecode/download/unidecode-0.1.8.tgz#efbb301538bc45246a9ac8c559d72f015305053e"
  integrity sha1-77swFTi8RSRqmsjFWdcvAVMFBT4=

unified@^9.1.0:
  version "9.2.0"
  resolved "https://registry.npm.taobao.org/unified/download/unified-9.2.0.tgz#67a62c627c40589edebbf60f53edfd4d822027f8"
  integrity sha1-Z6YsYnxAWJ7eu/YPU+39TYIgJ/g=
  dependencies:
    bail "^1.0.0"
    extend "^3.0.0"
    is-buffer "^2.0.0"
    is-plain-obj "^2.0.0"
    trough "^1.0.0"
    vfile "^4.0.0"

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.0, uniq@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz#ffede4b36b25290696e6e165d4a59edb998e6b02"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/unique-slug/download/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

unist-util-find-all-after@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/unist-util-find-all-after/download/unist-util-find-all-after-3.0.2.tgz?cache=0&sync_timestamp=1604247236455&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funist-util-find-all-after%2Fdownload%2Funist-util-find-all-after-3.0.2.tgz#fdfecd14c5b7aea5e9ef38d5e0d5f774eeb561f6"
  integrity sha1-/f7NFMW3rqXp7zjV4NX3dO61YfY=
  dependencies:
    unist-util-is "^4.0.0"

unist-util-is@^4.0.0:
  version "4.0.4"
  resolved "https://registry.npm.taobao.org/unist-util-is/download/unist-util-is-4.0.4.tgz?cache=0&sync_timestamp=1606662976542&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funist-util-is%2Fdownload%2Funist-util-is-4.0.4.tgz#3e9e8de6af2eb0039a59f50c9b3e99698a924f50"
  integrity sha1-Pp6N5q8usAOaWfUMmz6ZaYqST1A=

unist-util-stringify-position@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/unist-util-stringify-position/download/unist-util-stringify-position-2.0.3.tgz#cce3bfa1cdf85ba7375d1d5b17bdc4cada9bd9da"
  integrity sha1-zOO/oc34W6c3XR1bF73Eytqb2do=
  dependencies:
    "@types/unist" "^2.0.2"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/universalify/download/universalify-0.1.2.tgz?cache=0&sync_timestamp=1603180042770&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funiversalify%2Fdownload%2Funiversalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/unquote/download/unquote-1.1.1.tgz#8fded7324ec6e88a0ff8b905e7c098cdc086d544"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/upath/download/upath-1.2.0.tgz?cache=0&sync_timestamp=1604768535464&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fupath%2Fdownload%2Fupath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npm.taobao.org/uri-js/download/uri-js-4.4.1.tgz?cache=0&sync_timestamp=1610237517218&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furi-js%2Fdownload%2Furi-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-loader@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/url-loader/download/url-loader-2.3.0.tgz?cache=0&sync_timestamp=1602252665628&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furl-loader%2Fdownload%2Furl-loader-2.3.0.tgz#e0e2ef658f003efb8ca41b0f3ffbf76bab88658b"
  integrity sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs=
  dependencies:
    loader-utils "^1.2.3"
    mime "^2.4.4"
    schema-utils "^2.5.0"

url-parse@^1.4.3, url-parse@^1.4.7:
  version "1.4.7"
  resolved "https://registry.npm.taobao.org/url-parse/download/url-parse-1.4.7.tgz#a8a83535e8c00a316e403a5db4ac1b9b853ae278"
  integrity sha1-qKg1NejACjFuQDpdtKwbm4U64ng=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-slug@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/url-slug/download/url-slug-2.0.0.tgz?cache=0&sync_timestamp=1609907321913&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furl-slug%2Fdownload%2Furl-slug-2.0.0.tgz#a789d5aed4995c0d95af33377ad1d5c68d4d7027"
  integrity sha1-p4nVrtSZXA2VrzM3etHVxo1NcCc=
  dependencies:
    unidecode "0.1.8"

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npm.taobao.org/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.0.tgz?cache=0&sync_timestamp=1610159866228&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.0.tgz#440f7165a459c9a16dc145eb8e72f35687097030"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.1.tgz?cache=0&sync_timestamp=1610159866228&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Futil.promisify%2Fdownload%2Futil.promisify-1.0.1.tgz#6baf7774b80eeb0f7520d8b81d07982a59abbaee"
  integrity sha1-a693dLgO6w91INi4HQeYKlmruu4=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

util@0.10.3:
  version "0.10.3"
  resolved "https://registry.npm.taobao.org/util/download/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npm.taobao.org/util/download/util-0.11.1.tgz#3236733720ec64bb27f6e26f421aaa2e1b588d61"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/utila/download/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2, uuid@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npm.taobao.org/uuid/download/uuid-3.4.0.tgz?cache=0&sync_timestamp=1607458532020&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fuuid%2Fdownload%2Fuuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v8-compile-cache@^2.0.3, v8-compile-cache@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/v8-compile-cache/download/v8-compile-cache-2.2.0.tgz?cache=0&sync_timestamp=1603909396193&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fv8-compile-cache%2Fdownload%2Fv8-compile-cache-2.2.0.tgz#9471efa3ef9128d2f7c6a7ca39c4dd6b5055b132"
  integrity sha1-lHHvo++RKNL3xqfKOcTda1BVsTI=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/vendors/download/vendors-1.0.4.tgz#e2b800a53e7a29b93506c3cf41100d16c4c4ad8e"
  integrity sha1-4rgApT56Kbk1BsPPQRANFsTErY4=

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vfile-message@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/vfile-message/download/vfile-message-2.0.4.tgz#5b43b88171d409eae58477d13f23dd41d52c371a"
  integrity sha1-W0O4gXHUCerlhHfRPyPdQdUsNxo=
  dependencies:
    "@types/unist" "^2.0.0"
    unist-util-stringify-position "^2.0.0"

vfile@^4.0.0:
  version "4.2.1"
  resolved "https://registry.npm.taobao.org/vfile/download/vfile-4.2.1.tgz?cache=0&sync_timestamp=1607256217448&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvfile%2Fdownload%2Fvfile-4.2.1.tgz#03f1dce28fc625c625bc6514350fbdb00fa9e624"
  integrity sha1-A/Hc4o/GJcYlvGUUNQ+9sA+p5iQ=
  dependencies:
    "@types/unist" "^2.0.0"
    is-buffer "^2.0.0"
    unist-util-stringify-position "^2.0.0"
    vfile-message "^2.0.0"

vinyl-file@~1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/vinyl-file/download/vinyl-file-1.3.0.tgz#aa05634d3a867ba91447bedbb34afcb26f44f6e7"
  integrity sha1-qgVjTTqGe6kUR77bs0r8sm9E9uc=
  dependencies:
    graceful-fs "^4.1.2"
    strip-bom "^2.0.0"
    strip-bom-stream "^1.0.0"
    vinyl "^1.1.0"

vinyl@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/vinyl/download/vinyl-1.2.0.tgz#5c88036cf565e5df05558bfc911f8656df218884"
  integrity sha1-XIgDbPVl5d8FVYv8kR+GVt8hiIQ=
  dependencies:
    clone "^1.0.0"
    clone-stats "^0.0.1"
    replace-ext "0.0.1"

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/vm-browserify/download/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-cli-plugin-mock@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/vue-cli-plugin-mock/download/vue-cli-plugin-mock-1.0.3.tgz#5d31732c622a241239c640569f05b8bca4c660c6"
  integrity sha1-XTFzLGIqJBI5xkBWnwW4vKTGYMY=
  dependencies:
    express-mock-restful "^1.1.3"
    memory-fs "^0.4.1"

vue-clipboard2@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npm.taobao.org/vue-clipboard2/download/vue-clipboard2-0.3.1.tgz#6e551fb7bd384889b28b0da3b12289ed6bca4894"
  integrity sha1-blUft704SImyiw2jsSKJ7WvKSJQ=
  dependencies:
    clipboard "^2.0.0"

vue-cookies@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npm.taobao.org/vue-cookies/download/vue-cookies-1.7.4.tgz#d241d0a0431da0795837651d10b4d73e7c8d3e8d"
  integrity sha1-0kHQoEMdoHlYN2UdELTXPnyNPo0=

vue-eslint-parser@^7.4.1:
  version "7.4.1"
  resolved "https://registry.npm.taobao.org/vue-eslint-parser/download/vue-eslint-parser-7.4.1.tgz?cache=0&sync_timestamp=1611232916599&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-eslint-parser%2Fdownload%2Fvue-eslint-parser-7.4.1.tgz#e4adcf7876a7379758d9056a72235af18a587f92"
  integrity sha1-5K3PeHanN5dY2QVqciNa8YpYf5I=
  dependencies:
    debug "^4.1.1"
    eslint-scope "^5.0.0"
    eslint-visitor-keys "^1.1.0"
    espree "^6.2.1"
    esquery "^1.0.1"
    lodash "^4.17.15"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "https://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz#532955cc1eb208a3d990b3a9f9a70574657e08f2"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

"vue-loader-v16@npm:vue-loader@^16.1.0":
  version "16.1.2"
  resolved "https://registry.npm.taobao.org/vue-loader/download/vue-loader-16.1.2.tgz?cache=0&sync_timestamp=1608187947155&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-loader%2Fdownload%2Fvue-loader-16.1.2.tgz#5c03b6c50d2a5f983c7ceba15c50d78ca2b298f4"
  integrity sha1-XAO2xQ0qX5g8fOuhXFDXjKKymPQ=
  dependencies:
    chalk "^4.1.0"
    hash-sum "^2.0.0"
    loader-utils "^2.0.0"

vue-loader@^15.9.2:
  version "15.9.6"
  resolved "https://registry.npm.taobao.org/vue-loader/download/vue-loader-15.9.6.tgz?cache=0&sync_timestamp=1608187947155&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-loader%2Fdownload%2Fvue-loader-15.9.6.tgz#f4bb9ae20c3a8370af3ecf09b8126d38ffdb6b8b"
  integrity sha1-9Lua4gw6g3CvPs8JuBJtOP/ba4s=
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

vue-meta@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npm.taobao.org/vue-meta/download/vue-meta-2.4.0.tgz#a419fb4b4135ce965dab32ec641d1989c2ee4845"
  integrity sha1-pBn7S0E1zpZdqzLsZB0ZicLuSEU=
  dependencies:
    deepmerge "^4.2.2"

vue-ref@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/vue-ref/-/vue-ref-2.0.0.tgz#483084d732abed11da796778a8266a3af0ea1a9c"
  integrity sha512-uKNKpFOVeWNqS2mrBZqnpLyXJo5Q+vnkex6JvpENvhXHFNBW/SJTP8vJywLuVT3DpxwXcF9N0dyIiZ4/NpTexQ==

vue-router@^3.4.8:
  version "3.5.0"
  resolved "https://registry.npm.taobao.org/vue-router/download/vue-router-3.5.0.tgz?cache=0&sync_timestamp=1611588118141&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-router%2Fdownload%2Fvue-router-3.5.0.tgz#ae49da16a2939f8d28d66d5784b14167d661192f"
  integrity sha1-rknaFqKTn40o1m1XhLFBZ9ZhGS8=

vue-style-loader@^4.1.0, vue-style-loader@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-4.1.2.tgz#dedf349806f25ceb4e64f3ad7c0a44fba735fcf8"
  integrity sha1-3t80mAbyXOtOZPOtfApE+6c1/Pg=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@^2.6.12:
  version "2.6.12"
  resolved "https://registry.npm.taobao.org/vue-template-compiler/download/vue-template-compiler-2.6.12.tgz?cache=0&sync_timestamp=1597927338574&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue-template-compiler%2Fdownload%2Fvue-template-compiler-2.6.12.tgz#947ed7196744c8a5285ebe1233fe960437fcc57e"
  integrity sha1-lH7XGWdEyKUoXr4SM/6WBDf8xX4=
  dependencies:
    de-indent "^1.0.2"
    he "^1.1.0"

vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npm.taobao.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz#1ee3bc9a16ecbf5118be334bb15f9c46f82f5825"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

vue@^2.6.12:
  version "2.6.12"
  resolved "https://registry.npm.taobao.org/vue/download/vue-2.6.12.tgz?cache=0&sync_timestamp=1609360160361&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvue%2Fdownload%2Fvue-2.6.12.tgz#f5ebd4fa6bd2869403e29a896aed4904456c9123"
  integrity sha1-9evU+mvShpQD4pqJau1JBEVskSM=

vuex@^3.5.1:
  version "3.6.0"
  resolved "https://registry.npm.taobao.org/vuex/download/vuex-3.6.0.tgz?cache=0&sync_timestamp=1606318339710&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fvuex%2Fdownload%2Fvuex-3.6.0.tgz#95efa56a58f7607c135b053350833a09e01aa813"
  integrity sha1-le+lalj3YHwTWwUzUIM6CeAaqBM=

warning@^4.0.0:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/warning/-/warning-4.0.3.tgz#16e9e077eb8a86d6af7d64aa1e05fd85b4678ca3"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz?cache=0&sync_timestamp=1604989128919&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwatchpack-chokidar2%2Fdownload%2Fwatchpack-chokidar2-2.0.1.tgz#38500072ee6ece66f3769936950ea1771be1c957"
  integrity sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.7.4:
  version "1.7.5"
  resolved "https://registry.npm.taobao.org/watchpack/download/watchpack-1.7.5.tgz?cache=0&sync_timestamp=1607687421210&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwatchpack%2Fdownload%2Fwatchpack-1.7.5.tgz#1267e6c55e0b9b5be44c2023aed5437a2c26c453"
  integrity sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npm.taobao.org/wbuf/download/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webpack-bundle-analyzer@^3.8.0:
  version "3.9.0"
  resolved "https://registry.npm.taobao.org/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.9.0.tgz?cache=0&sync_timestamp=1611221513167&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-bundle-analyzer%2Fdownload%2Fwebpack-bundle-analyzer-3.9.0.tgz#f6f94db108fb574e415ad313de41a2707d33ef3c"
  integrity sha1-9vlNsQj7V05BWtMT3kGicH0z7zw=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.19"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-chain@^6.4.0:
  version "6.5.1"
  resolved "https://registry.npm.taobao.org/webpack-chain/download/webpack-chain-6.5.1.tgz?cache=0&sync_timestamp=1595813261846&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-chain%2Fdownload%2Fwebpack-chain-6.5.1.tgz#4f27284cbbb637e3c8fbdef43eef588d4d861206"
  integrity sha1-TycoTLu2N+PI+970Pu9YjU2GEgY=
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^2.0.1"

webpack-dev-middleware@^3.7.2:
  version "3.7.3"
  resolved "https://registry.npm.taobao.org/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz?cache=0&sync_timestamp=1610718844043&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-dev-middleware%2Fdownload%2Fwebpack-dev-middleware-3.7.3.tgz#0639372b143262e2b84ab95d3b91a7597061c2c5"
  integrity sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.11.0:
  version "3.11.2"
  resolved "https://registry.npm.taobao.org/webpack-dev-server/download/webpack-dev-server-3.11.2.tgz?cache=0&sync_timestamp=1610550019218&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-dev-server%2Fdownload%2Fwebpack-dev-server-3.11.2.tgz#695ebced76a4929f0d5de7fd73fafe185fe33708"
  integrity sha1-aV687Xakkp8NXef9c/r+GF/jNwg=
  dependencies:
    ansi-html "0.0.7"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.8"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    sockjs-client "^1.5.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/webpack-log/download/webpack-log-2.0.0.tgz#5b7928e0637593f119d32f6227c1e0ac31e1b47f"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-merge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npm.taobao.org/webpack-merge/download/webpack-merge-4.2.2.tgz?cache=0&sync_timestamp=1608705461067&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-merge%2Fdownload%2Fwebpack-merge-4.2.2.tgz#a27c52ea783d1398afd2087f547d7b9d2f43634d"
  integrity sha1-onxS6ng9E5iv0gh/VH17nS9DY00=
  dependencies:
    lodash "^4.17.15"

webpack-sources@^1.1.0, webpack-sources@^1.4.0, webpack-sources@^1.4.1, webpack-sources@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npm.taobao.org/webpack-sources/download/webpack-sources-1.4.3.tgz?cache=0&sync_timestamp=1603965213388&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack-sources%2Fdownload%2Fwebpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack-spritesmith@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/webpack-spritesmith/download/webpack-spritesmith-1.1.0.tgz#a012907f19d54553facb870c762b95660f22621b"
  integrity sha1-oBKQfxnVRVP6y4cMdiuVZg8iYhs=
  dependencies:
    gaze "^1.1.3"
    loader-utils "^1.2.3"
    lodash "^4.17.15"
    mkdirp "^0.5.1"
    mz "^2.7.0"
    spritesheet-templates "^10.4.2"
    spritesmith "^3.4.0"

webpack@^4.0.0:
  version "4.46.0"
  resolved "https://registry.npm.taobao.org/webpack/download/webpack-4.46.0.tgz?cache=0&sync_timestamp=1611324259768&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack%2Fdownload%2Fwebpack-4.46.0.tgz#bf9b4404ea20a073605e0a011d188d77cb6ad542"
  integrity sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.5.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.7.4"
    webpack-sources "^1.4.1"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://registry.npm.taobao.org/websocket-driver/download/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.9, which@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/wordwrap/download/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npm.taobao.org/worker-farm/download/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/write-file-atomic/download/write-file-atomic-3.0.3.tgz#56bd5c5a5c70481cd19c571bd39ab965a5de56e8"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@^6.0.0, ws@^6.2.1:
  version "6.2.1"
  resolved "https://registry.npm.taobao.org/ws/download/ws-6.2.1.tgz#442fdf0a47ed64f59b6a5d8ff130f4748ed524fb"
  integrity sha1-RC/fCkftZPWbal2P8TD0dI7VJPs=
  dependencies:
    async-limiter "~1.0.0"

xss@^1.0.6:
  version "1.0.8"
  resolved "https://registry.npm.taobao.org/xss/download/xss-1.0.8.tgz#32feb87feb74b3dcd3d404b7a68ababf10700535"
  integrity sha1-Mv64f+t0s9zT1AS3poq6vxBwBTU=
  dependencies:
    commander "^2.20.3"
    cssfilter "0.0.10"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/y18n/download/y18n-4.0.1.tgz?cache=0&sync_timestamp=1609798693274&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-4.0.1.tgz#8db2b83c31c5d75099bb890b23f3094891e247d4"
  integrity sha1-jbK4PDHF11CZu4kLI/MJSJHiR9Q=

y18n@^5.0.5:
  version "5.0.5"
  resolved "https://registry.npm.taobao.org/y18n/download/y18n-5.0.5.tgz?cache=0&sync_timestamp=1609798693274&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-5.0.5.tgz#8769ec08d03b1ea2df2500acef561743bbb9ab18"
  integrity sha1-h2nsCNA7HqLfJQCs71YXQ7u5qxg=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/yaml/download/yaml-1.10.0.tgz#3b593add944876077d4d683fee01081bd9fff31e"
  integrity sha1-O1k63ZRIdgd9TWg/7gEIG9n/8x4=

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-13.1.2.tgz?cache=0&sync_timestamp=1604886694625&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.2, yargs-parser@^20.2.3:
  version "20.2.4"
  resolved "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-20.2.4.tgz?cache=0&sync_timestamp=1604886694625&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.4.tgz#b42890f14566796f85ae8e3a25290d205f154a54"
  integrity sha1-tCiQ8UVmeW+Fro46JSkNIF8VSlQ=

yargs@^13.3.2:
  version "13.3.2"
  resolved "https://registry.npm.taobao.org/yargs/download/yargs-13.3.2.tgz?cache=0&sync_timestamp=1610219751347&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^16.0.0:
  version "16.2.0"
  resolved "https://registry.npm.taobao.org/yargs/download/yargs-16.2.0.tgz?cache=0&sync_timestamp=1610219751347&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-16.2.0.tgz#1c82bf0f6b6a66eafce7ef30e376f49a12477f66"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yorkie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/yorkie/download/yorkie-2.0.0.tgz#92411912d435214e12c51c2ae1093e54b6bb83d9"
  integrity sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

zwitch@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/zwitch/download/zwitch-1.0.5.tgz#d11d7381ffed16b742f6af7b3f223d5cd9fe9920"
  integrity sha1-0R1zgf/tFrdC9q97PyI9XNn+mSA=
