{"ast": null, "code": "import _typeof from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/typeof.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.constructor.js\";\nimport \"core-js/modules/es.regexp.dot-all.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.sticky.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport store from '@/store/index';\nexport function deepClone(target) {\n  // 定义一个变量\n  var result;\n  // 如果当前需要深拷贝的是一个对象的话\n  if (_typeof(target) === 'object') {\n    // 如果是一个数组的话\n    if (Array.isArray(target)) {\n      result = []; // 将result赋值为一个数组，并且执行遍历\n      for (var i in target) {\n        // 递归克隆数组中的每一项\n        result.push(deepClone(target[i]));\n      }\n      // 判断如果当前的值是null的话；直接赋值为null\n    } else if (target === null) {\n      result = null;\n      // 判断如果当前的值是一个RegExp对象的话，直接赋值\n    } else if (target.constructor === RegExp) {\n      result = target;\n    } else {\n      // 否则是普通对象，直接for in循环，递归赋值对象的所有值\n      result = {};\n      for (var _i in target) {\n        result[_i] = deepClone(target[_i]);\n      }\n    }\n    // 如果不是对象的话，就是基本数据类型，那么直接赋值\n  } else {\n    result = target;\n  }\n  // 返回最终结果\n  return result;\n}\nfunction hasPermission(permission) {\n  if (store.state.settings.openPermission) {\n    return store.state.user.permissions.some(function (v) {\n      return v === permission;\n    });\n  } else {\n    return true;\n  }\n}\nexport function auth(value) {\n  var auth;\n  if (typeof value === 'string') {\n    auth = hasPermission(value);\n  } else {\n    auth = value.some(function (item) {\n      return hasPermission(item);\n    });\n  }\n  return auth;\n}\nexport function authAll(value) {\n  var auth = value.every(function (item) {\n    return hasPermission(item);\n  });\n  return auth;\n}", "map": {"version": 3, "names": ["store", "deepClone", "target", "result", "_typeof", "Array", "isArray", "i", "push", "constructor", "RegExp", "hasPermission", "permission", "state", "settings", "openPermission", "user", "permissions", "some", "v", "auth", "value", "item", "authAll", "every"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/util/index.js"], "sourcesContent": ["import store from '@/store/index'\n\nexport function deepClone(target) {\n    // 定义一个变量\n    let result\n    // 如果当前需要深拷贝的是一个对象的话\n    if (typeof target === 'object') {\n    // 如果是一个数组的话\n        if (Array.isArray(target)) {\n            result = [] // 将result赋值为一个数组，并且执行遍历\n            for (let i in target) {\n                // 递归克隆数组中的每一项\n                result.push(deepClone(target[i]))\n            }\n            // 判断如果当前的值是null的话；直接赋值为null\n        } else if (target === null) {\n            result = null\n            // 判断如果当前的值是一个RegExp对象的话，直接赋值\n        } else if (target.constructor === RegExp) {\n            result = target\n        } else {\n            // 否则是普通对象，直接for in循环，递归赋值对象的所有值\n            result = {}\n            for (let i in target) {\n                result[i] = deepClone(target[i])\n            }\n        }\n        // 如果不是对象的话，就是基本数据类型，那么直接赋值\n    } else {\n        result = target\n    }\n    // 返回最终结果\n    return result\n}\n\nfunction hasPermission(permission) {\n    if (store.state.settings.openPermission) {\n        return store.state.user.permissions.some(v => {\n            return v === permission\n        })\n    } else {\n        return true\n    }\n}\n\nexport function auth(value) {\n    let auth\n    if (typeof value === 'string') {\n        auth = hasPermission(value)\n    } else {\n        auth = value.some(item => {\n            return hasPermission(item)\n        })\n    }\n    return auth\n}\n\nexport function authAll(value) {\n    const auth = value.every(item => {\n        return hasPermission(item)\n    })\n    return auth\n}\n"], "mappings": ";;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,eAAe;AAEjC,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAC9B;EACA,IAAIC,MAAM;EACV;EACA,IAAIC,OAAA,CAAOF,MAAM,MAAK,QAAQ,EAAE;IAChC;IACI,IAAIG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;MACvBC,MAAM,GAAG,EAAE,EAAC;MACZ,KAAK,IAAII,CAAC,IAAIL,MAAM,EAAE;QAClB;QACAC,MAAM,CAACK,IAAI,CAACP,SAAS,CAACC,MAAM,CAACK,CAAC,CAAC,CAAC,CAAC;MACrC;MACA;IACJ,CAAC,MAAM,IAAIL,MAAM,KAAK,IAAI,EAAE;MACxBC,MAAM,GAAG,IAAI;MACb;IACJ,CAAC,MAAM,IAAID,MAAM,CAACO,WAAW,KAAKC,MAAM,EAAE;MACtCP,MAAM,GAAGD,MAAM;IACnB,CAAC,MAAM;MACH;MACAC,MAAM,GAAG,CAAC,CAAC;MACX,KAAK,IAAII,EAAC,IAAIL,MAAM,EAAE;QAClBC,MAAM,CAACI,EAAC,CAAC,GAAGN,SAAS,CAACC,MAAM,CAACK,EAAC,CAAC,CAAC;MACpC;IACJ;IACA;EACJ,CAAC,MAAM;IACHJ,MAAM,GAAGD,MAAM;EACnB;EACA;EACA,OAAOC,MAAM;AACjB;AAEA,SAASQ,aAAaA,CAACC,UAAU,EAAE;EAC/B,IAAIZ,KAAK,CAACa,KAAK,CAACC,QAAQ,CAACC,cAAc,EAAE;IACrC,OAAOf,KAAK,CAACa,KAAK,CAACG,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,UAAAC,CAAC,EAAI;MAC1C,OAAOA,CAAC,KAAKP,UAAU;IAC3B,CAAC,CAAC;EACN,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACJ;AAEA,OAAO,SAASQ,IAAIA,CAACC,KAAK,EAAE;EACxB,IAAID,IAAI;EACR,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC3BD,IAAI,GAAGT,aAAa,CAACU,KAAK,CAAC;EAC/B,CAAC,MAAM;IACHD,IAAI,GAAGC,KAAK,CAACH,IAAI,CAAC,UAAAI,IAAI,EAAI;MACtB,OAAOX,aAAa,CAACW,IAAI,CAAC;IAC9B,CAAC,CAAC;EACN;EACA,OAAOF,IAAI;AACf;AAEA,OAAO,SAASG,OAAOA,CAACF,KAAK,EAAE;EAC3B,IAAMD,IAAI,GAAGC,KAAK,CAACG,KAAK,CAAC,UAAAF,IAAI,EAAI;IAC7B,OAAOX,aAAa,CAACW,IAAI,CAAC;EAC9B,CAAC,CAAC;EACF,OAAOF,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}