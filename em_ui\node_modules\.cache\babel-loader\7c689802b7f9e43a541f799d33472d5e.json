{"ast": null, "code": "import { auth } from '@/util';\nexport default {\n  name: 'Auth',\n  props: {\n    value: {\n      type: [String, Array],\n      default: ''\n    }\n  },\n  methods: {\n    check: function check() {\n      return auth(this.value);\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAQA;AAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "props", "value", "type", "default", "methods", "check"], "sourceRoot": "src/components/Auth", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div>\n        <slot v-if=\"check()\" />\n        <slot v-else name=\"no-auth\" />\n    </div>\n</template>\n\n<script>\nimport { auth } from '@/util'\n\nexport default {\n    name: 'Auth',\n    props: {\n        value: {\n            type: [String, Array],\n            default: ''\n        }\n    },\n    methods: {\n        check() {\n            return auth(this.value)\n        }\n    }\n}\n</script>\n"]}, "metadata": {}, "sourceType": "module"}