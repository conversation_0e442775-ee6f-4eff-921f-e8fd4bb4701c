{"ast": null, "code": "import _interopRequireWildcard from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/interopRequireWildcard.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport Layout from '@/layout';\nexport default [{\n  path: '/rq-manager',\n  component: Layout,\n  redirect: '/rq-manager/info',\n  meta: {\n    title: '社区管理'\n  },\n  children: [{\n    path: 'info',\n    name: 'rq_info',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/rq_info.vue'));\n      });\n    },\n    meta: {\n      title: '基本信息管理'\n    }\n  }, {\n    path: 'facilities',\n    name: 'rq_facilities',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/rq_facilities.vue'));\n      });\n    },\n    meta: {\n      title: '周边设施管理'\n    }\n  }, {\n    path: 'notices',\n    name: 'rq_notices',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/rq_notices.vue'));\n      });\n    },\n    meta: {\n      title: '物业公告管理'\n    }\n  }]\n}, {\n  path: '/building-manager',\n  component: Layout,\n  redirect: '/building-manager/building',\n  meta: {\n    title: '楼盘管理'\n  },\n  children: [{\n    path: 'building',\n    name: 'rq_building',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/rq_building.vue'));\n      });\n    },\n    meta: {\n      title: '楼宇管理'\n    }\n  }, {\n    path: 'room',\n    name: 'rq_room',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/rq_room.vue'));\n      });\n    },\n    meta: {\n      title: '房间管理'\n    }\n  }]\n}, {\n  path: '/guarantee-manager',\n  component: Layout,\n  redirect: '/guarantee-manager/repair',\n  meta: {\n    title: '社区保障管理'\n  },\n  children: [{\n    path: 'repair',\n    name: 'rq_repair',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/guarantee/rq_repair_manager.vue'));\n      });\n    },\n    meta: {\n      title: '报修管理'\n    }\n  }, {\n    path: 'complaint',\n    name: 'rq_complaint',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/guarantee/rq_complaint_manager.vue'));\n      });\n    },\n    meta: {\n      title: '投诉管理'\n    }\n  }]\n}, {\n  path: '/charge-manager',\n  component: Layout,\n  redirect: '/charge-manager/type',\n  meta: {\n    title: '收费管理'\n  },\n  children: [{\n    path: 'type',\n    name: 'rq_charge_type',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/charge/rq_charge_type.vue'));\n      });\n    },\n    meta: {\n      title: '收费类型管理'\n    }\n  }\n  // {\n  //   path: 'aaas',\n  //   name: 'videosExampleVideo1',\n  //   component: () => import('@/views/videos_example/1'),\n  //   meta: {\n  //     title: '住户费用管理',\n  //   }\n  // },\n  ]\n}, {\n  path: '/user-manager',\n  component: Layout,\n  redirect: '/user-manager/estate_user',\n  meta: {\n    title: '用户管理'\n  },\n  children: [{\n    path: 'estate_user',\n    name: 'estate_user',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/user/estate_user_manager.vue'));\n      });\n    },\n    meta: {\n      title: '物业人员管理'\n    }\n  }, {\n    path: 'household',\n    name: 'user_household',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/user/user_household.vue'));\n      });\n    },\n    meta: {\n      title: '住户信息管理'\n    }\n  }\n  // {\n  //   path: 'aaas',\n  //   name: 'videosExampleVideo1',\n  //   component: () => import('@/views/videos_example/1'),\n  //   meta: {\n  //     title: '物业管理人员管理',\n  //   }\n  // },\n  ]\n}, {\n  path: '/statistics',\n  component: Layout,\n  redirect: '/statistics/dashboard',\n  meta: {\n    title: '数据统计'\n  },\n  children: [{\n    path: 'dashboard',\n    name: 'statistics_dashboard',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/statistics/dashboard.vue'));\n      });\n    },\n    meta: {\n      title: '数据概览'\n    }\n  }]\n}, {\n  path: '/system-manager',\n  component: Layout,\n  redirect: '/system-manager',\n  meta: {\n    title: '系统管理'\n  },\n  children: [{\n    path: 'automatic-task',\n    name: 'at_manager',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/system/automatic_task.vue'));\n      });\n    },\n    meta: {\n      title: '定时任务管理'\n    }\n  }, {\n    path: 'icon-demo',\n    name: 'icon_demo',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/system/icon-demo.vue'));\n      });\n    },\n    meta: {\n      title: 'PNG图标示例'\n    }\n  }\n  // {\n  //   path: 'aaas',\n  //   name: 'videosExampleVideo1',\n  //   component: () => import('@/views/videos_example/1'),\n  //   meta: {\n  //     title: '编辑管理员',\n  //   }\n  // },\n  ]\n}];", "map": {"version": 3, "names": ["Layout", "path", "component", "redirect", "meta", "title", "children", "name", "Promise", "resolve", "then", "_interopRequireWildcard", "require"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/router/modules/admin.js"], "sourcesContent": ["import Layout from '@/layout'\n\nexport default [\n  {\n    path: '/rq-manager',\n    component: Layout,\n    redirect: '/rq-manager/info',\n    meta: {\n      title: '社区管理'\n    },\n    children: [\n      {\n        path: 'info',\n        name: 'rq_info',\n        component: () => import('@/views/admin/rq/rq_info.vue'),\n        meta: {\n          title: '基本信息管理',\n        }\n      },\n      {\n        path: 'facilities',\n        name: 'rq_facilities',\n        component: () => import('@/views/admin/rq/rq_facilities.vue'),\n        meta: {\n          title: '周边设施管理',\n        }\n      },\n      {\n        path: 'notices',\n        name: 'rq_notices',\n        component: () => import('@/views/admin/rq/rq_notices.vue'),\n        meta: {\n          title: '物业公告管理',\n        }\n      },\n    ]\n  },\n  {\n    path: '/building-manager',\n    component: Layout,\n    redirect: '/building-manager/building',\n    meta: {\n      title: '楼盘管理'\n    },\n    children: [\n      {\n        path: 'building',\n        name: 'rq_building',\n        component: () => import('@/views/admin/rq/rq_building.vue'),\n        meta: {\n          title: '楼宇管理',\n        }\n      },\n      {\n        path: 'room',\n        name: 'rq_room',\n        component: () => import('@/views/admin/rq/rq_room.vue'),\n        meta: {\n          title: '房间管理',\n        }\n      }\n    ]\n  },\n  {\n    path: '/guarantee-manager',\n    component: Layout,\n    redirect: '/guarantee-manager/repair',\n    meta: {\n      title: '社区保障管理'\n    },\n    children: [\n      {\n        path: 'repair',\n        name: 'rq_repair',\n        component: () => import('@/views/admin/rq/guarantee/rq_repair_manager.vue'),\n        meta: {\n          title: '报修管理',\n        }\n      },\n      {\n        path: 'complaint',\n        name: 'rq_complaint',\n        component: () => import('@/views/admin/rq/guarantee/rq_complaint_manager.vue'),\n        meta: {\n          title: '投诉管理',\n        }\n      },\n    ]\n  },\n  {\n    path: '/charge-manager',\n    component: Layout,\n    redirect: '/charge-manager/type',\n    meta: {\n      title: '收费管理'\n    },\n    children: [\n      {\n        path: 'type',\n        name: 'rq_charge_type',\n        component: () => import('@/views/admin/rq/charge/rq_charge_type.vue'),\n        meta: {\n          title: '收费类型管理',\n        }\n      },\n      // {\n      //   path: 'aaas',\n      //   name: 'videosExampleVideo1',\n      //   component: () => import('@/views/videos_example/1'),\n      //   meta: {\n      //     title: '住户费用管理',\n      //   }\n      // },\n    ]\n  },\n  {\n    path: '/user-manager',\n    component: Layout,\n    redirect: '/user-manager/estate_user',\n    meta: {\n      title: '用户管理'\n    },\n    children: [\n      {\n        path: 'estate_user',\n        name: 'estate_user',\n        component: () => import('@/views/admin/user/estate_user_manager.vue'),\n        meta: {\n          title: '物业人员管理',\n        }\n      },\n      {\n        path: 'household',\n        name: 'user_household',\n        component: () => import('@/views/admin/user/user_household.vue'),\n        meta: {\n          title: '住户信息管理',\n        }\n      },\n      // {\n      //   path: 'aaas',\n      //   name: 'videosExampleVideo1',\n      //   component: () => import('@/views/videos_example/1'),\n      //   meta: {\n      //     title: '物业管理人员管理',\n      //   }\n      // },\n    ]\n  },\n  {\n    path: '/statistics',\n    component: Layout,\n    redirect: '/statistics/dashboard',\n    meta: {\n      title: '数据统计'\n    },\n    children: [\n      {\n        path: 'dashboard',\n        name: 'statistics_dashboard',\n        component: () => import('@/views/admin/statistics/dashboard.vue'),\n        meta: {\n          title: '数据概览',\n        }\n      }\n    ]\n  },\n  {\n    path: '/system-manager',\n    component: Layout,\n    redirect: '/system-manager',\n    meta: {\n      title: '系统管理'\n    },\n    children: [\n      {\n        path: 'automatic-task',\n        name: 'at_manager',\n        component: () => import('@/views/admin/system/automatic_task.vue'),\n        meta: {\n          title: '定时任务管理',\n        }\n      },\n      {\n        path: 'icon-demo',\n        name: 'icon_demo',\n        component: () => import('@/views/admin/system/icon-demo.vue'),\n        meta: {\n          title: 'PNG图标示例',\n        }\n      },\n      // {\n      //   path: 'aaas',\n      //   name: 'videosExampleVideo1',\n      //   component: () => import('@/views/videos_example/1'),\n      //   meta: {\n      //     title: '编辑管理员',\n      //   }\n      // },\n    ]\n  },\n]\n"], "mappings": ";;;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,eAAe,CACb;EACEC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,kBAAkB;EAC5BC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,MAAM;IACZM,IAAI,EAAE,SAAS;IACfL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,YAAY;IAClBM,IAAI,EAAE,eAAe;IACrBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oCAAoC;MAAA;IAAA,CAAC;IAC7DR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,SAAS;IACfM,IAAI,EAAE,YAAY;IAClBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC,EACD;EACEJ,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,4BAA4B;EACtCC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,UAAU;IAChBM,IAAI,EAAE,aAAa;IACnBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kCAAkC;MAAA;IAAA,CAAC;IAC3DR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZM,IAAI,EAAE,SAAS;IACfL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC,EACD;EACEJ,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,2BAA2B;EACrCC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,QAAQ;IACdM,IAAI,EAAE,WAAW;IACjBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kDAAkD;MAAA;IAAA,CAAC;IAC3ER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,WAAW;IACjBM,IAAI,EAAE,cAAc;IACpBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qDAAqD;MAAA;IAAA,CAAC;IAC9ER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC,EACD;EACEJ,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,sBAAsB;EAChCC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,MAAM;IACZM,IAAI,EAAE,gBAAgB;IACtBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,4CAA4C;MAAA;IAAA,CAAC;IACrER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ,CAAC,EACD;EACEJ,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,2BAA2B;EACrCC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,aAAa;IACnBM,IAAI,EAAE,aAAa;IACnBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,4CAA4C;MAAA;IAAA,CAAC;IACrER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,WAAW;IACjBM,IAAI,EAAE,gBAAgB;IACtBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uCAAuC;MAAA;IAAA,CAAC;IAChER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ,CAAC,EACD;EACEJ,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,uBAAuB;EACjCC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,WAAW;IACjBM,IAAI,EAAE,sBAAsB;IAC5BL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,wCAAwC;MAAA;IAAA,CAAC;IACjER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAEL,CAAC,EACD;EACEJ,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,gBAAgB;IACtBM,IAAI,EAAE,YAAY;IAClBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,yCAAyC;MAAA;IAAA,CAAC;IAClER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,WAAW;IACjBM,IAAI,EAAE,WAAW;IACjBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oCAAoC;MAAA;IAAA,CAAC;IAC7DR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}