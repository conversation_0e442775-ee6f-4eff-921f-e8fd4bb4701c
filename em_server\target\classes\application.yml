# 设置Session过期时间
server:
  servlet:
    session:
      timeout: 1D
  port: 8082
#MybatisPlus开启日志
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
spring:
  application:
    name: em_server
  main:
    allow-circular-references: true
    #注意查看下方的数据库配置，请修改为自己本地数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: root
    password: root
    name: defaultDataSource
  # 邮件发送
  mail:
    username: 写你的
    password: 写你的
    host: 写你的
