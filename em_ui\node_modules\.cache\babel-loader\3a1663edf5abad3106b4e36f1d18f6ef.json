{"ast": null, "code": "export default {\n  name: 'ExampleNotice',\n  data: function data() {\n    return {\n      show: false,\n      content: ''\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.show = true;\n    setTimeout(function () {\n      _this.show = false;\n    }, 2000);\n  }\n};", "map": {"version": 3, "mappings": "AASA;EACAA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAC;IACA;EACA;AACA", "names": ["name", "data", "show", "content", "mounted", "setTimeout", "_this"], "sourceRoot": "src/components/ExampleNotice", "sources": ["main.vue"], "sourcesContent": ["<template>\n    <transition name=\"notice\">\n        <div v-if=\"show\" class=\"notice\">\n            {{ content }}\n        </div>\n    </transition>\n</template>\n\n<script>\nexport default {\n    name: 'ExampleNotice',\n    data() {\n        return {\n            show: false,\n            content: ''\n        }\n    },\n    mounted() {\n        this.show = true\n        setTimeout(() => {\n            this.show = false\n        }, 2000)\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.notice {\n    padding: 10px;\n    background-color: #eee;\n    border-radius: 10px;\n    @include position-center(xy);\n}\n.notice-leave-active,\n.notice-enter-active {\n    transition: all 0.3s;\n}\n.notice-enter,\n.notice-leave-to {\n    opacity: 0;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}