{"name": "fantastic-admin", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode test --dest dist-test", "build": "vue-cli-service build", "build:example": "vue-cli-service build --mode example --dest dist-example", "lint": "vue-cli-service lint", "stylelint": "vue-cli-service lint:style", "svgo": "svgo -f src/assets/icons"}, "dependencies": {"@tinymce/tinymce-vue": "^4.0.4", "ant-design-vue": "^1.7.8", "axios": "^1.6.2", "core-js": "^3.33.3", "dayjs": "^1.11.10", "element-ui": "^2.15.14", "hotkeys-js": "^3.12.0", "mavon-editor": "^2.10.4", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "screenfull": "^6.0.2", "tinymce": "^6.8.2", "vue": "^2.7.16", "vue-clipboard2": "^0.3.3", "vue-cookies": "^1.8.3", "vue-meta": "^2.4.0", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-service": "~5.0.8", "@winner-fed/vue-cli-plugin-stylelint": "^1.0.4", "babel-eslint": "^10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-import": "^1.13.5", "compression-webpack-plugin": "^10.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "html-webpack-plugin": "^5.5.4", "mockjs": "^1.1.0", "path-browserify": "^1.0.1", "sass": "^1.69.5", "sass-loader": "^13.3.2", "sass-resources-loader": "^2.2.5", "stylelint": "^15.11.0", "stylelint-config-recommended-scss": "^13.1.0", "stylelint-config-standard": "^34.0.0", "stylelint-scss": "^5.3.1", "svg-sprite-loader": "^6.0.11", "svgo": "^3.0.5", "vue-cli-plugin-mock": "^1.0.2", "vue-template-compiler": "^2.7.16", "webpack-spritesmith": "^1.1.0"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}