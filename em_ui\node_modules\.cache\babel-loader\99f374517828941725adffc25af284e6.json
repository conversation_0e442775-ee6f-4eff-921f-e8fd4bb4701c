{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getChargeType, saveChargeType, deleteChargeType } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      rTime: rTime,\n      loading: false,\n      labelCol: {\n        span: 7\n      },\n      wrapperCol: {\n        span: 7\n      },\n      table_selectedRowKeys: [],\n      chargeType_query_type: 'chargeName',\n      chargeType_query_buttonTitle: '搜索',\n      chargeType_query_text: '',\n      chargeType_save_title: '新增收费类型',\n      chargeType_save_modalVisible: false,\n      chargeType_form_data: {},\n      chargeType_data_list: [],\n      rules: {\n        chargeName: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        chargeMoney: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.Get_chargeTypeDataList();\n  },\n  watch: {\n    chargeType_save_modalVisible: function chargeType_save_modalVisible(val) {\n      if (!val) {\n        this.chargeType_form_data = {};\n      }\n    }\n  },\n  methods: {\n    Get_chargeTypeDataList: function Get_chargeTypeDataList() {\n      var _this = this;\n      getChargeType().then(function (res) {\n        _this.chargeType_query_buttonTitle = '搜索';\n        _this.chargeType_data_list = res.data;\n        _this.chargeType_save_title = '新增收费类型';\n      });\n    },\n    Query_chargeTypeDataList: function Query_chargeTypeDataList() {\n      var _this2 = this;\n      var text = this.chargeType_query_text;\n      var temp_list = [];\n      this.chargeType_data_list.forEach(function (item) {\n        if (item[_this2.chargeType_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.chargeType_query_buttonTitle = '返回';\n      this.chargeType_data_list = temp_list;\n    },\n    Edit_chargeTypeData: function Edit_chargeTypeData(form) {\n      this.chargeType_save_title = '编辑收费类型';\n      this.chargeType_form_data = JSON.parse(JSON.stringify(form));\n      this.chargeType_save_modalVisible = true;\n    },\n    Del_chargeTypeData: function Del_chargeTypeData(id) {\n      var _this3 = this;\n      deleteChargeType(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_chargeTypeDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_chargeTypeData(_this4.chargeType_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_chargeTypeData: function Save_chargeTypeData() {\n      var _this5 = this;\n      saveChargeType(this.chargeType_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.chargeType_save_modalVisible = false;\n        _this5.Get_chargeTypeDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AAyFA;AACA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;UAAAC;UAAAC;UAAAC;QAAA;QACAC;UAAAH;UAAAC;UAAAC;QAAA;MACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACAV;MACA;QACA;MACA;IACA;EACA;EACAW;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;EACA;AACA", "names": ["data", "rTime", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "chargeType_query_type", "chargeType_query_buttonTitle", "chargeType_query_text", "chargeType_save_title", "chargeType_save_modalVisible", "chargeType_form_data", "chargeType_data_list", "rules", "chargeName", "required", "message", "trigger", "chargeMoney", "created", "watch", "methods", "Get_chargeTypeDataList", "getChargeType", "_this", "Query_chargeTypeDataList", "temp_list", "Edit_chargeTypeData", "Del_chargeTypeData", "deleteChargeType", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_chargeTypeData", "saveChargeType", "_this5", "Table_selectChange"], "sourceRoot": "src/views/admin/rq/charge", "sources": ["rq_charge_type.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"社区收费类型管理\">\n      <div class=\"head\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"收费类型名称\"\n          v-model=\"chargeType_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"chargeName\">收费类型名称</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"chargeType_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"chargeType_query_buttonTitle\"\n          size=\"large\"\n          @search=\"chargeType_query_buttonTitle == '搜索' ? Query_chargeTypeDataList() : Get_chargeTypeDataList()\"\n        />\n        <a-button\n          type=\"primary\"\n          style=\"height: 40px;\"\n          @click=\"chargeType_save_modalVisible = true\"\n        >添加收费类型</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「收费类型」</a-button>\n      </div>\n      <a-table\n        :data-source=\"chargeType_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"id\" title=\"编号\" data-index=\"id\" />\n        <a-table-column key=\"chargeName\" title=\"收费类型名称\" data-index=\"chargeName\" />\n        <a-table-column key=\"chargeMoney\" title=\"收费金额(元)\" data-index=\"chargeMoney\" />\n        <a-table-column key=\"createTime\" title=\"创建时间\" data-index=\"createTime\">\n          <!-- rTime -->\n          <template slot-scope=\"text, record\">\n            <span>{{rTime(record.createTime)}}</span>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"updateTime\" title=\"修改时间\" data-index=\"updateTime\">\n          <!-- rTime -->\n          <template slot-scope=\"text, record\">\n            <span>{{rTime(record.updateTime)}}</span>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_chargeTypeData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_chargeTypeData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"chargeType_save_modalVisible\"\n      :title=\"chargeType_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_chargeTypeData\"\n    >\n      <a-form-model\n        :model=\"chargeType_form_data\"\n        :rules=\"rules\"\n        :label-col=\"labelCol\"\n        :wrapper-col=\"wrapperCol\"\n      >\n        <a-form-model-item label=\"收费名称\" prop=\"chargeName\">\n          <a-input v-model=\"chargeType_form_data.chargeName\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"收费金额(月)\" prop=\"chargeName\">\n          <a-input v-model=\"chargeType_form_data.chargeMoney\" />\n        </a-form-model-item>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getChargeType, saveChargeType, deleteChargeType } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\n\nexport default {\n  data () {\n    return {\n      rTime,\n      loading: false,\n      labelCol: { span: 7 },\n      wrapperCol: { span: 7 },\n      table_selectedRowKeys: [],\n      chargeType_query_type: 'chargeName',\n      chargeType_query_buttonTitle: '搜索',\n      chargeType_query_text: '',\n      chargeType_save_title: '新增收费类型',\n      chargeType_save_modalVisible: false,\n      chargeType_form_data: {},\n      chargeType_data_list: [],\n      rules: {\n        chargeName: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        chargeMoney: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n      },\n    }\n  },\n  created () {\n    this.Get_chargeTypeDataList()\n  },\n  watch: {\n    chargeType_save_modalVisible (val) {\n      if (!val) {\n        this.chargeType_form_data = {}\n      }\n    }\n  },\n  methods: {\n    Get_chargeTypeDataList () {\n      getChargeType().then(res => {\n        this.chargeType_query_buttonTitle = '搜索'\n        this.chargeType_data_list = res.data\n        this.chargeType_save_title = '新增收费类型'\n      })\n    },\n    Query_chargeTypeDataList () {\n      let text = this.chargeType_query_text\n      let temp_list = []\n      this.chargeType_data_list.forEach(item => {\n        if (item[this.chargeType_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.chargeType_query_buttonTitle = '返回'\n      this.chargeType_data_list = temp_list\n    },\n    Edit_chargeTypeData (form) {\n      this.chargeType_save_title = '编辑收费类型'\n      this.chargeType_form_data = JSON.parse(JSON.stringify(form))\n      this.chargeType_save_modalVisible = true\n    },\n    Del_chargeTypeData (id) {\n      deleteChargeType(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_chargeTypeDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_chargeTypeData(this.chargeType_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    Save_chargeTypeData () {\n      saveChargeType(this.chargeType_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.chargeType_save_modalVisible = false\n        this.Get_chargeTypeDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n.ant-modal-content {\n    width: 24vw;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}