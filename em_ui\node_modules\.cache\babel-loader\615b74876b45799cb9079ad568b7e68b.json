{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport { getOverview, getRecentRepairs, getRecentComplaints } from '@/api/requests/statistics';\nexport default {\n  name: 'Dashboard',\n  data: function data() {\n    return {\n      stats: {\n        buildingCount: 0,\n        roomCount: 0,\n        userCount: 0,\n        repairCount: 0\n      },\n      recentRepairs: [],\n      recentComplaints: [],\n      loading: false\n    };\n  },\n  mounted: function mounted() {\n    this.loadStats();\n    this.loadRecentData();\n  },\n  methods: {\n    loadStats: function loadStats() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var response, _t;\n        return _regeneratorRuntime().wrap(function (_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _this.loading = true;\n              _context.next = 1;\n              return getOverview();\n            case 1:\n              response = _context.sent;\n              if (response.code === 200) {\n                _this.stats = response.data;\n              } else {\n                // 如果API调用失败，使用模拟数据\n                _this.stats = {\n                  buildingCount: 12,\n                  roomCount: 368,\n                  userCount: 892,\n                  repairCount: 23\n                };\n              }\n              _context.next = 3;\n              break;\n            case 2:\n              _context.prev = 2;\n              _t = _context[\"catch\"](0);\n              console.error('加载统计数据失败:', _t);\n              // 使用模拟数据作为后备\n              _this.stats = {\n                buildingCount: 12,\n                roomCount: 368,\n                userCount: 892,\n                repairCount: 23\n              };\n            case 3:\n              _context.prev = 3;\n              _this.loading = false;\n              return _context.finish(3);\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 2, 3, 4]]);\n      }))();\n    },\n    loadRecentData: function loadRecentData() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var repairResponse, complaintResponse, _t2;\n        return _regeneratorRuntime().wrap(function (_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 1;\n              return getRecentRepairs();\n            case 1:\n              repairResponse = _context2.sent;\n              if (repairResponse.code === 200) {\n                _this2.recentRepairs = repairResponse.data.slice(0, 5); // 只显示最新5条\n              }\n\n              // 加载最新投诉数据\n              _context2.next = 2;\n              return getRecentComplaints();\n            case 2:\n              complaintResponse = _context2.sent;\n              if (complaintResponse.code === 200) {\n                _this2.recentComplaints = complaintResponse.data.slice(0, 5); // 只显示最新5条\n              }\n              _context2.next = 4;\n              break;\n            case 3:\n              _context2.prev = 3;\n              _t2 = _context2[\"catch\"](0);\n              console.error('加载最新数据失败:', _t2);\n              // 使用模拟数据作为后备\n              _this2.recentRepairs = [{\n                title: '水管漏水',\n                userName: '张三',\n                status: '处理中',\n                createTime: '2024-01-15 10:30'\n              }, {\n                title: '电梯故障',\n                userName: '李四',\n                status: '已完成',\n                createTime: '2024-01-15 09:15'\n              }, {\n                title: '门锁损坏',\n                userName: '王五',\n                status: '待处理',\n                createTime: '2024-01-15 08:45'\n              }];\n              _this2.recentComplaints = [{\n                title: '噪音扰民',\n                userName: '赵六',\n                status: '处理中',\n                createTime: '2024-01-15 11:20'\n              }, {\n                title: '垃圾清理不及时',\n                userName: '钱七',\n                status: '已完成',\n                createTime: '2024-01-15 10:10'\n              }];\n            case 4:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 3]]);\n      }))();\n    },\n    getStatusType: function getStatusType(status) {\n      var statusMap = {\n        '待处理': 'warning',\n        '处理中': 'primary',\n        '已完成': 'success'\n      };\n      return statusMap[status] || 'info';\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;AAoIA;AAEA;EACAA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAAC;cAEAC;cAAAD;cAAA,OACAE;YAAA;cAAAC;cACA;gBACAF;cACA;gBACA;gBACAA;kBACAX;kBACAC;kBACAC;kBACAC;gBACA;cACA;cAAAO;cAAA;YAAA;cAAAA;cAAAI;cAEAC;cACA;cACAJ;gBACAX;gBACAC;gBACAC;gBACAC;cACA;YAAA;cAAAO;cAEAC;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAAC;cAAAA;cAAA,OAGAC;YAAA;cAAAC;cACA;gBACAC;cACA;;cAEA;cAAAH;cAAA,OACAI;YAAA;cAAAC;cACA;gBACAF;cACA;cAAAH;cAAA;YAAA;cAAAA;cAAAM;cAEAR;cACA;cACAK,wBACA;gBAAAI;gBAAAC;gBAAAC;gBAAAC;cAAA,GACA;gBAAAH;gBAAAC;gBAAAC;gBAAAC;cAAA,GACA;gBAAAH;gBAAAC;gBAAAC;gBAAAC;cAAA,EACA;cAEAP,2BACA;gBAAAI;gBAAAC;gBAAAC;gBAAAC;cAAA,GACA;gBAAAH;gBAAAC;gBAAAC;gBAAAC;cAAA,EACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "stats", "buildingCount", "roomCount", "userCount", "repairCount", "recentRepairs", "recentComplaints", "loading", "mounted", "methods", "loadStats", "_context", "_this", "getOverview", "response", "_t", "console", "loadRecentData", "_context2", "getRecentRepairs", "repairResponse", "_this2", "getRecentComplaints", "complaintResponse", "_t2", "title", "userName", "status", "createTime", "getStatusType"], "sourceRoot": "src/views/admin/statistics", "sources": ["dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <page-header title=\"数据统计\">\n      <template #content>\n        <div>物业管理系统数据概览</div>\n      </template>\n    </page-header>\n    \n    <page-main>\n      <!-- 统计卡片 -->\n      <el-row :gutter=\"20\" class=\"stats-cards\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon building\">\n                <png-icon name=\"building\" size=\"24\" />\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ stats.buildingCount }}</div>\n                <div class=\"stat-label\">楼宇总数</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon room\">\n                <png-icon name=\"home\" size=\"24\" />\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ stats.roomCount }}</div>\n                <div class=\"stat-label\">房间总数</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon user\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ stats.userCount }}</div>\n                <div class=\"stat-label\">住户总数</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon repair\">\n                <png-icon name=\"repair\" size=\"24\" />\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ stats.repairCount }}</div>\n                <div class=\"stat-label\">本月报修</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n      \n      <!-- 图表区域 -->\n      <el-row :gutter=\"20\" class=\"charts-section\">\n        <el-col :span=\"12\">\n          <el-card>\n            <div slot=\"header\">\n              <span>月度报修趋势</span>\n            </div>\n            <div class=\"chart-container\" id=\"repairChart\"></div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-card>\n            <div slot=\"header\">\n              <span>费用收缴情况</span>\n            </div>\n            <div class=\"chart-container\" id=\"paymentChart\"></div>\n          </el-card>\n        </el-col>\n      </el-row>\n      \n      <!-- 最新动态 -->\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-card>\n            <div slot=\"header\">\n              <span>最新报修</span>\n            </div>\n            <el-table :data=\"recentRepairs\" style=\"width: 100%\">\n              <el-table-column prop=\"title\" label=\"报修内容\" width=\"200\"></el-table-column>\n              <el-table-column prop=\"userName\" label=\"报修人\" width=\"100\"></el-table-column>\n              <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-tag :type=\"getStatusType(scope.row.status)\">{{ scope.row.status }}</el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"createTime\" label=\"时间\"></el-table-column>\n            </el-table>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-card>\n            <div slot=\"header\">\n              <span>最新投诉</span>\n            </div>\n            <el-table :data=\"recentComplaints\" style=\"width: 100%\">\n              <el-table-column prop=\"title\" label=\"投诉内容\" width=\"200\"></el-table-column>\n              <el-table-column prop=\"userName\" label=\"投诉人\" width=\"100\"></el-table-column>\n              <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-tag :type=\"getStatusType(scope.row.status)\">{{ scope.row.status }}</el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"createTime\" label=\"时间\"></el-table-column>\n            </el-table>\n          </el-card>\n        </el-col>\n      </el-row>\n    </page-main>\n  </div>\n</template>\n\n<script>\nimport { getOverview, getRecentRepairs, getRecentComplaints } from '@/api/requests/statistics'\n\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      stats: {\n        buildingCount: 0,\n        roomCount: 0,\n        userCount: 0,\n        repairCount: 0\n      },\n      recentRepairs: [],\n      recentComplaints: [],\n      loading: false\n    }\n  },\n  mounted() {\n    this.loadStats()\n    this.loadRecentData()\n  },\n  methods: {\n    async loadStats() {\n      try {\n        this.loading = true\n        const response = await getOverview()\n        if (response.code === 200) {\n          this.stats = response.data\n        } else {\n          // 如果API调用失败，使用模拟数据\n          this.stats = {\n            buildingCount: 12,\n            roomCount: 368,\n            userCount: 892,\n            repairCount: 23\n          }\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error)\n        // 使用模拟数据作为后备\n        this.stats = {\n          buildingCount: 12,\n          roomCount: 368,\n          userCount: 892,\n          repairCount: 23\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadRecentData() {\n      try {\n        // 加载最新报修数据\n        const repairResponse = await getRecentRepairs()\n        if (repairResponse.code === 200) {\n          this.recentRepairs = repairResponse.data.slice(0, 5) // 只显示最新5条\n        }\n\n        // 加载最新投诉数据\n        const complaintResponse = await getRecentComplaints()\n        if (complaintResponse.code === 200) {\n          this.recentComplaints = complaintResponse.data.slice(0, 5) // 只显示最新5条\n        }\n      } catch (error) {\n        console.error('加载最新数据失败:', error)\n        // 使用模拟数据作为后备\n        this.recentRepairs = [\n          { title: '水管漏水', userName: '张三', status: '处理中', createTime: '2024-01-15 10:30' },\n          { title: '电梯故障', userName: '李四', status: '已完成', createTime: '2024-01-15 09:15' },\n          { title: '门锁损坏', userName: '王五', status: '待处理', createTime: '2024-01-15 08:45' }\n        ]\n\n        this.recentComplaints = [\n          { title: '噪音扰民', userName: '赵六', status: '处理中', createTime: '2024-01-15 11:20' },\n          { title: '垃圾清理不及时', userName: '钱七', status: '已完成', createTime: '2024-01-15 10:10' }\n        ]\n      }\n    },\n\n    getStatusType(status) {\n      const statusMap = {\n        '待处理': 'warning',\n        '处理中': 'primary',\n        '已完成': 'success'\n      }\n      return statusMap[status] || 'info'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard {\n  .stats-cards {\n    margin-bottom: 20px;\n    \n    .stat-card {\n      .stat-content {\n        display: flex;\n        align-items: center;\n        \n        .stat-icon {\n          width: 60px;\n          height: 60px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-right: 15px;\n          \n          i {\n            font-size: 24px;\n            color: white;\n          }\n\n          .png-icon {\n            filter: brightness(0) invert(1);\n          }\n          \n          &.building {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          }\n          \n          &.room {\n            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n          }\n          \n          &.user {\n            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n          }\n          \n          &.repair {\n            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n          }\n        }\n        \n        .stat-info {\n          .stat-number {\n            font-size: 28px;\n            font-weight: bold;\n            color: #303133;\n            line-height: 1;\n          }\n          \n          .stat-label {\n            font-size: 14px;\n            color: #909399;\n            margin-top: 5px;\n          }\n        }\n      }\n    }\n  }\n  \n  .charts-section {\n    margin-bottom: 20px;\n    \n    .chart-container {\n      height: 300px;\n    }\n  }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}