{"ast": null, "code": "import \"core-js/modules/es.number.constructor.js\";\nimport Vue from 'vue';\nimport merge from 'element-ui/src/utils/merge';\nimport PopupManager from 'element-ui/src/utils/popup/popup-manager';\nimport getScrollBarWidth from '../scrollbar-width';\nimport { getStyle, addClass, removeClass, hasClass } from '../dom';\nvar idSeed = 1;\nvar scrollBarWidth;\nexport default {\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    openDelay: {},\n    closeDelay: {},\n    zIndex: {},\n    modal: {\n      type: Boolean,\n      default: false\n    },\n    modalFade: {\n      type: Boolean,\n      default: true\n    },\n    modalClass: {},\n    modalAppendToBody: {\n      type: Boolean,\n      default: false\n    },\n    lockScroll: {\n      type: Boolean,\n      default: true\n    },\n    closeOnPressEscape: {\n      type: Boolean,\n      default: false\n    },\n    closeOnClickModal: {\n      type: Boolean,\n      default: false\n    }\n  },\n  beforeMount: function beforeMount() {\n    this._popupId = 'popup-' + idSeed++;\n    PopupManager.register(this._popupId, this);\n  },\n  beforeDestroy: function beforeDestroy() {\n    PopupManager.deregister(this._popupId);\n    PopupManager.closeModal(this._popupId);\n    this.restoreBodyStyle();\n  },\n  data: function data() {\n    return {\n      opened: false,\n      bodyPaddingRight: null,\n      computedBodyPaddingRight: 0,\n      withoutHiddenClass: true,\n      rendered: false\n    };\n  },\n  watch: {\n    visible: function visible(val) {\n      var _this = this;\n      if (val) {\n        if (this._opening) return;\n        if (!this.rendered) {\n          this.rendered = true;\n          Vue.nextTick(function () {\n            _this.open();\n          });\n        } else {\n          this.open();\n        }\n      } else {\n        this.close();\n      }\n    }\n  },\n  methods: {\n    open: function open(options) {\n      var _this2 = this;\n      if (!this.rendered) {\n        this.rendered = true;\n      }\n      var props = merge({}, this.$props || this, options);\n      if (this._closeTimer) {\n        clearTimeout(this._closeTimer);\n        this._closeTimer = null;\n      }\n      clearTimeout(this._openTimer);\n      var openDelay = Number(props.openDelay);\n      if (openDelay > 0) {\n        this._openTimer = setTimeout(function () {\n          _this2._openTimer = null;\n          _this2.doOpen(props);\n        }, openDelay);\n      } else {\n        this.doOpen(props);\n      }\n    },\n    doOpen: function doOpen(props) {\n      if (this.$isServer) return;\n      if (this.willOpen && !this.willOpen()) return;\n      if (this.opened) return;\n      this._opening = true;\n      var dom = this.$el;\n      var modal = props.modal;\n      var zIndex = props.zIndex;\n      if (zIndex) {\n        PopupManager.zIndex = zIndex;\n      }\n      if (modal) {\n        if (this._closing) {\n          PopupManager.closeModal(this._popupId);\n          this._closing = false;\n        }\n        PopupManager.openModal(this._popupId, PopupManager.nextZIndex(), this.modalAppendToBody ? undefined : dom, props.modalClass, props.modalFade);\n        if (props.lockScroll) {\n          this.withoutHiddenClass = !hasClass(document.body, 'el-popup-parent--hidden');\n          if (this.withoutHiddenClass) {\n            this.bodyPaddingRight = document.body.style.paddingRight;\n            this.computedBodyPaddingRight = parseInt(getStyle(document.body, 'paddingRight'), 10);\n          }\n          scrollBarWidth = getScrollBarWidth();\n          var bodyHasOverflow = document.documentElement.clientHeight < document.body.scrollHeight;\n          var bodyOverflowY = getStyle(document.body, 'overflowY');\n          if (scrollBarWidth > 0 && (bodyHasOverflow || bodyOverflowY === 'scroll') && this.withoutHiddenClass) {\n            document.body.style.paddingRight = this.computedBodyPaddingRight + scrollBarWidth + 'px';\n          }\n          addClass(document.body, 'el-popup-parent--hidden');\n        }\n      }\n      if (getComputedStyle(dom).position === 'static') {\n        dom.style.position = 'absolute';\n      }\n      dom.style.zIndex = PopupManager.nextZIndex();\n      this.opened = true;\n      this.onOpen && this.onOpen();\n      this.doAfterOpen();\n    },\n    doAfterOpen: function doAfterOpen() {\n      this._opening = false;\n    },\n    close: function close() {\n      var _this3 = this;\n      if (this.willClose && !this.willClose()) return;\n      if (this._openTimer !== null) {\n        clearTimeout(this._openTimer);\n        this._openTimer = null;\n      }\n      clearTimeout(this._closeTimer);\n      var closeDelay = Number(this.closeDelay);\n      if (closeDelay > 0) {\n        this._closeTimer = setTimeout(function () {\n          _this3._closeTimer = null;\n          _this3.doClose();\n        }, closeDelay);\n      } else {\n        this.doClose();\n      }\n    },\n    doClose: function doClose() {\n      this._closing = true;\n      this.onClose && this.onClose();\n      if (this.lockScroll) {\n        setTimeout(this.restoreBodyStyle, 200);\n      }\n      this.opened = false;\n      this.doAfterClose();\n    },\n    doAfterClose: function doAfterClose() {\n      PopupManager.closeModal(this._popupId);\n      this._closing = false;\n    },\n    restoreBodyStyle: function restoreBodyStyle() {\n      if (this.modal && this.withoutHiddenClass) {\n        document.body.style.paddingRight = this.bodyPaddingRight;\n        removeClass(document.body, 'el-popup-parent--hidden');\n      }\n      this.withoutHiddenClass = true;\n    }\n  }\n};\nexport { PopupManager };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "merge", "PopupManager", "getScrollBarWidth", "getStyle", "addClass", "removeClass", "hasClass", "idSeed", "scrollBarWidth", "props", "visible", "type", "Boolean", "default", "openDelay", "close<PERSON><PERSON><PERSON>", "zIndex", "modal", "modalFade", "modalClass", "modalAppendToBody", "lockScroll", "closeOnPressEscape", "closeOnClickModal", "beforeMount", "_popupId", "register", "<PERSON><PERSON><PERSON><PERSON>", "deregister", "closeModal", "restoreBodyStyle", "data", "opened", "bodyPaddingRight", "computedBodyPaddingRight", "withoutHiddenClass", "rendered", "watch", "val", "_this", "_opening", "nextTick", "open", "close", "methods", "options", "_this2", "$props", "_closeTimer", "clearTimeout", "_openTimer", "Number", "setTimeout", "doOpen", "$isServer", "<PERSON><PERSON><PERSON>", "dom", "$el", "_closing", "openModal", "nextZIndex", "undefined", "document", "body", "style", "paddingRight", "parseInt", "bodyHasOverflow", "documentElement", "clientHeight", "scrollHeight", "bodyOverflowY", "getComputedStyle", "position", "onOpen", "doAfterOpen", "_this3", "willClose", "doClose", "onClose", "doAfterClose"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/src/utils/popup/index.js"], "sourcesContent": ["import Vue from 'vue';\nimport merge from 'element-ui/src/utils/merge';\nimport PopupManager from 'element-ui/src/utils/popup/popup-manager';\nimport getScrollBarWidth from '../scrollbar-width';\nimport { getStyle, addClass, removeClass, hasClass } from '../dom';\n\nlet idSeed = 1;\n\nlet scrollBarWidth;\n\nexport default {\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    openDelay: {},\n    closeDelay: {},\n    zIndex: {},\n    modal: {\n      type: Boolean,\n      default: false\n    },\n    modalFade: {\n      type: Boolean,\n      default: true\n    },\n    modalClass: {},\n    modalAppendToBody: {\n      type: Boolean,\n      default: false\n    },\n    lockScroll: {\n      type: Boolean,\n      default: true\n    },\n    closeOnPressEscape: {\n      type: Boolean,\n      default: false\n    },\n    closeOnClickModal: {\n      type: Boolean,\n      default: false\n    }\n  },\n\n  beforeMount() {\n    this._popupId = 'popup-' + idSeed++;\n    PopupManager.register(this._popupId, this);\n  },\n\n  beforeDestroy() {\n    PopupManager.deregister(this._popupId);\n    PopupManager.closeModal(this._popupId);\n\n    this.restoreBodyStyle();\n  },\n\n  data() {\n    return {\n      opened: false,\n      bodyPaddingRight: null,\n      computedBodyPaddingRight: 0,\n      withoutHiddenClass: true,\n      rendered: false\n    };\n  },\n\n  watch: {\n    visible(val) {\n      if (val) {\n        if (this._opening) return;\n        if (!this.rendered) {\n          this.rendered = true;\n          Vue.nextTick(() => {\n            this.open();\n          });\n        } else {\n          this.open();\n        }\n      } else {\n        this.close();\n      }\n    }\n  },\n\n  methods: {\n    open(options) {\n      if (!this.rendered) {\n        this.rendered = true;\n      }\n\n      const props = merge({}, this.$props || this, options);\n\n      if (this._closeTimer) {\n        clearTimeout(this._closeTimer);\n        this._closeTimer = null;\n      }\n      clearTimeout(this._openTimer);\n\n      const openDelay = Number(props.openDelay);\n      if (openDelay > 0) {\n        this._openTimer = setTimeout(() => {\n          this._openTimer = null;\n          this.doOpen(props);\n        }, openDelay);\n      } else {\n        this.doOpen(props);\n      }\n    },\n\n    doOpen(props) {\n      if (this.$isServer) return;\n      if (this.willOpen && !this.willOpen()) return;\n      if (this.opened) return;\n\n      this._opening = true;\n\n      const dom = this.$el;\n\n      const modal = props.modal;\n\n      const zIndex = props.zIndex;\n      if (zIndex) {\n        PopupManager.zIndex = zIndex;\n      }\n\n      if (modal) {\n        if (this._closing) {\n          PopupManager.closeModal(this._popupId);\n          this._closing = false;\n        }\n        PopupManager.openModal(this._popupId, PopupManager.nextZIndex(), this.modalAppendToBody ? undefined : dom, props.modalClass, props.modalFade);\n        if (props.lockScroll) {\n          this.withoutHiddenClass = !hasClass(document.body, 'el-popup-parent--hidden');\n          if (this.withoutHiddenClass) {\n            this.bodyPaddingRight = document.body.style.paddingRight;\n            this.computedBodyPaddingRight = parseInt(getStyle(document.body, 'paddingRight'), 10);\n          }\n          scrollBarWidth = getScrollBarWidth();\n          let bodyHasOverflow = document.documentElement.clientHeight < document.body.scrollHeight;\n          let bodyOverflowY = getStyle(document.body, 'overflowY');\n          if (scrollBarWidth > 0 && (bodyHasOverflow || bodyOverflowY === 'scroll') && this.withoutHiddenClass) {\n            document.body.style.paddingRight = this.computedBodyPaddingRight + scrollBarWidth + 'px';\n          }\n          addClass(document.body, 'el-popup-parent--hidden');\n        }\n      }\n\n      if (getComputedStyle(dom).position === 'static') {\n        dom.style.position = 'absolute';\n      }\n\n      dom.style.zIndex = PopupManager.nextZIndex();\n      this.opened = true;\n\n      this.onOpen && this.onOpen();\n\n      this.doAfterOpen();\n    },\n\n    doAfterOpen() {\n      this._opening = false;\n    },\n\n    close() {\n      if (this.willClose && !this.willClose()) return;\n\n      if (this._openTimer !== null) {\n        clearTimeout(this._openTimer);\n        this._openTimer = null;\n      }\n      clearTimeout(this._closeTimer);\n\n      const closeDelay = Number(this.closeDelay);\n\n      if (closeDelay > 0) {\n        this._closeTimer = setTimeout(() => {\n          this._closeTimer = null;\n          this.doClose();\n        }, closeDelay);\n      } else {\n        this.doClose();\n      }\n    },\n\n    doClose() {\n      this._closing = true;\n\n      this.onClose && this.onClose();\n\n      if (this.lockScroll) {\n        setTimeout(this.restoreBodyStyle, 200);\n      }\n\n      this.opened = false;\n\n      this.doAfterClose();\n    },\n\n    doAfterClose() {\n      PopupManager.closeModal(this._popupId);\n      this._closing = false;\n    },\n\n    restoreBodyStyle() {\n      if (this.modal && this.withoutHiddenClass) {\n        document.body.style.paddingRight = this.bodyPaddingRight;\n        removeClass(document.body, 'el-popup-parent--hidden');\n      }\n      this.withoutHiddenClass = true;\n    }\n  }\n};\n\nexport {\n  PopupManager\n};\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,YAAY,MAAM,0CAA0C;AACnE,OAAOC,iBAAiB,MAAM,oBAAoB;AAClD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,QAAQ;AAElE,IAAIC,MAAM,GAAG,CAAC;AAEd,IAAIC,cAAc;AAElB,eAAe;EACbC,KAAK,EAAE;IACLC,OAAO,EAAE;MACPC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE,CAAC,CAAC;IACbC,UAAU,EAAE,CAAC,CAAC;IACdC,MAAM,EAAE,CAAC,CAAC;IACVC,KAAK,EAAE;MACLN,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDK,SAAS,EAAE;MACTP,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDM,UAAU,EAAE,CAAC,CAAC;IACdC,iBAAiB,EAAE;MACjBT,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDQ,UAAU,EAAE;MACVV,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDS,kBAAkB,EAAE;MAClBX,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDU,iBAAiB,EAAE;MACjBZ,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EAEDW,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,QAAQ,GAAGlB,MAAM,EAAE;IACnCN,YAAY,CAACyB,QAAQ,CAAC,IAAI,CAACD,QAAQ,EAAE,IAAI,CAAC;EAC5C,CAAC;EAEDE,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd1B,YAAY,CAAC2B,UAAU,CAAC,IAAI,CAACH,QAAQ,CAAC;IACtCxB,YAAY,CAAC4B,UAAU,CAAC,IAAI,CAACJ,QAAQ,CAAC;IAEtC,IAAI,CAACK,gBAAgB,CAAC,CAAC;EACzB,CAAC;EAEDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAE,KAAK;MACbC,gBAAgB,EAAE,IAAI;MACtBC,wBAAwB,EAAE,CAAC;MAC3BC,kBAAkB,EAAE,IAAI;MACxBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAEDC,KAAK,EAAE;IACL3B,OAAO,WAAPA,OAAOA,CAAC4B,GAAG,EAAE;MAAA,IAAAC,KAAA;MACX,IAAID,GAAG,EAAE;QACP,IAAI,IAAI,CAACE,QAAQ,EAAE;QACnB,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;UAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;UACpBrC,GAAG,CAAC0C,QAAQ,CAAC,YAAM;YACjBF,KAAI,CAACG,IAAI,CAAC,CAAC;UACb,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACA,IAAI,CAAC,CAAC;QACb;MACF,CAAC,MAAM;QACL,IAAI,CAACC,KAAK,CAAC,CAAC;MACd;IACF;EACF,CAAC;EAEDC,OAAO,EAAE;IACPF,IAAI,WAAJA,IAAIA,CAACG,OAAO,EAAE;MAAA,IAAAC,MAAA;MACZ,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE;QAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACtB;MAEA,IAAM3B,KAAK,GAAGT,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC+C,MAAM,IAAI,IAAI,EAAEF,OAAO,CAAC;MAErD,IAAI,IAAI,CAACG,WAAW,EAAE;QACpBC,YAAY,CAAC,IAAI,CAACD,WAAW,CAAC;QAC9B,IAAI,CAACA,WAAW,GAAG,IAAI;MACzB;MACAC,YAAY,CAAC,IAAI,CAACC,UAAU,CAAC;MAE7B,IAAMpC,SAAS,GAAGqC,MAAM,CAAC1C,KAAK,CAACK,SAAS,CAAC;MACzC,IAAIA,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACoC,UAAU,GAAGE,UAAU,CAAC,YAAM;UACjCN,MAAI,CAACI,UAAU,GAAG,IAAI;UACtBJ,MAAI,CAACO,MAAM,CAAC5C,KAAK,CAAC;QACpB,CAAC,EAAEK,SAAS,CAAC;MACf,CAAC,MAAM;QACL,IAAI,CAACuC,MAAM,CAAC5C,KAAK,CAAC;MACpB;IACF,CAAC;IAED4C,MAAM,WAANA,MAAMA,CAAC5C,KAAK,EAAE;MACZ,IAAI,IAAI,CAAC6C,SAAS,EAAE;MACpB,IAAI,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACA,QAAQ,CAAC,CAAC,EAAE;MACvC,IAAI,IAAI,CAACvB,MAAM,EAAE;MAEjB,IAAI,CAACQ,QAAQ,GAAG,IAAI;MAEpB,IAAMgB,GAAG,GAAG,IAAI,CAACC,GAAG;MAEpB,IAAMxC,KAAK,GAAGR,KAAK,CAACQ,KAAK;MAEzB,IAAMD,MAAM,GAAGP,KAAK,CAACO,MAAM;MAC3B,IAAIA,MAAM,EAAE;QACVf,YAAY,CAACe,MAAM,GAAGA,MAAM;MAC9B;MAEA,IAAIC,KAAK,EAAE;QACT,IAAI,IAAI,CAACyC,QAAQ,EAAE;UACjBzD,YAAY,CAAC4B,UAAU,CAAC,IAAI,CAACJ,QAAQ,CAAC;UACtC,IAAI,CAACiC,QAAQ,GAAG,KAAK;QACvB;QACAzD,YAAY,CAAC0D,SAAS,CAAC,IAAI,CAAClC,QAAQ,EAAExB,YAAY,CAAC2D,UAAU,CAAC,CAAC,EAAE,IAAI,CAACxC,iBAAiB,GAAGyC,SAAS,GAAGL,GAAG,EAAE/C,KAAK,CAACU,UAAU,EAAEV,KAAK,CAACS,SAAS,CAAC;QAC7I,IAAIT,KAAK,CAACY,UAAU,EAAE;UACpB,IAAI,CAACc,kBAAkB,GAAG,CAAC7B,QAAQ,CAACwD,QAAQ,CAACC,IAAI,EAAE,yBAAyB,CAAC;UAC7E,IAAI,IAAI,CAAC5B,kBAAkB,EAAE;YAC3B,IAAI,CAACF,gBAAgB,GAAG6B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,YAAY;YACxD,IAAI,CAAC/B,wBAAwB,GAAGgC,QAAQ,CAAC/D,QAAQ,CAAC2D,QAAQ,CAACC,IAAI,EAAE,cAAc,CAAC,EAAE,EAAE,CAAC;UACvF;UACAvD,cAAc,GAAGN,iBAAiB,CAAC,CAAC;UACpC,IAAIiE,eAAe,GAAGL,QAAQ,CAACM,eAAe,CAACC,YAAY,GAAGP,QAAQ,CAACC,IAAI,CAACO,YAAY;UACxF,IAAIC,aAAa,GAAGpE,QAAQ,CAAC2D,QAAQ,CAACC,IAAI,EAAE,WAAW,CAAC;UACxD,IAAIvD,cAAc,GAAG,CAAC,KAAK2D,eAAe,IAAII,aAAa,KAAK,QAAQ,CAAC,IAAI,IAAI,CAACpC,kBAAkB,EAAE;YACpG2B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,YAAY,GAAG,IAAI,CAAC/B,wBAAwB,GAAG1B,cAAc,GAAG,IAAI;UAC1F;UACAJ,QAAQ,CAAC0D,QAAQ,CAACC,IAAI,EAAE,yBAAyB,CAAC;QACpD;MACF;MAEA,IAAIS,gBAAgB,CAAChB,GAAG,CAAC,CAACiB,QAAQ,KAAK,QAAQ,EAAE;QAC/CjB,GAAG,CAACQ,KAAK,CAACS,QAAQ,GAAG,UAAU;MACjC;MAEAjB,GAAG,CAACQ,KAAK,CAAChD,MAAM,GAAGf,YAAY,CAAC2D,UAAU,CAAC,CAAC;MAC5C,IAAI,CAAC5B,MAAM,GAAG,IAAI;MAElB,IAAI,CAAC0C,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC,CAAC;MAE5B,IAAI,CAACC,WAAW,CAAC,CAAC;IACpB,CAAC;IAEDA,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACnC,QAAQ,GAAG,KAAK;IACvB,CAAC;IAEDG,KAAK,WAALA,KAAKA,CAAA,EAAG;MAAA,IAAAiC,MAAA;MACN,IAAI,IAAI,CAACC,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC,CAAC,EAAE;MAEzC,IAAI,IAAI,CAAC3B,UAAU,KAAK,IAAI,EAAE;QAC5BD,YAAY,CAAC,IAAI,CAACC,UAAU,CAAC;QAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;MACxB;MACAD,YAAY,CAAC,IAAI,CAACD,WAAW,CAAC;MAE9B,IAAMjC,UAAU,GAAGoC,MAAM,CAAC,IAAI,CAACpC,UAAU,CAAC;MAE1C,IAAIA,UAAU,GAAG,CAAC,EAAE;QAClB,IAAI,CAACiC,WAAW,GAAGI,UAAU,CAAC,YAAM;UAClCwB,MAAI,CAAC5B,WAAW,GAAG,IAAI;UACvB4B,MAAI,CAACE,OAAO,CAAC,CAAC;QAChB,CAAC,EAAE/D,UAAU,CAAC;MAChB,CAAC,MAAM;QACL,IAAI,CAAC+D,OAAO,CAAC,CAAC;MAChB;IACF,CAAC;IAEDA,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,IAAI,CAACpB,QAAQ,GAAG,IAAI;MAEpB,IAAI,CAACqB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,CAAC;MAE9B,IAAI,IAAI,CAAC1D,UAAU,EAAE;QACnB+B,UAAU,CAAC,IAAI,CAACtB,gBAAgB,EAAE,GAAG,CAAC;MACxC;MAEA,IAAI,CAACE,MAAM,GAAG,KAAK;MAEnB,IAAI,CAACgD,YAAY,CAAC,CAAC;IACrB,CAAC;IAEDA,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb/E,YAAY,CAAC4B,UAAU,CAAC,IAAI,CAACJ,QAAQ,CAAC;MACtC,IAAI,CAACiC,QAAQ,GAAG,KAAK;IACvB,CAAC;IAED5B,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACb,KAAK,IAAI,IAAI,CAACkB,kBAAkB,EAAE;QACzC2B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,YAAY,GAAG,IAAI,CAAChC,gBAAgB;QACxD5B,WAAW,CAACyD,QAAQ,CAACC,IAAI,EAAE,yBAAyB,CAAC;MACvD;MACA,IAAI,CAAC5B,kBAAkB,GAAG,IAAI;IAChC;EACF;AACF,CAAC;AAED,SACElC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}