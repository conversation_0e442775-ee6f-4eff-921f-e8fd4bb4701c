{"ast": null, "code": "import _objectSpread from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/es.number.to-fixed.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.values.js\";\nimport { on, off } from 'element-ui/src/utils/dom';\nimport { rafThrottle, isFirefox } from 'element-ui/src/utils/util';\nimport { PopupManager } from 'element-ui/src/utils/popup';\nvar Mode = {\n  CONTAIN: {\n    name: 'contain',\n    icon: 'el-icon-full-screen'\n  },\n  ORIGINAL: {\n    name: 'original',\n    icon: 'el-icon-c-scale-to-original'\n  }\n};\nvar mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';\nexport default {\n  name: 'elImageViewer',\n  props: {\n    urlList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    zIndex: {\n      type: Number,\n      default: 2000\n    },\n    onSwitch: {\n      type: Function,\n      default: function _default() {}\n    },\n    onClose: {\n      type: Function,\n      default: function _default() {}\n    },\n    initialIndex: {\n      type: Number,\n      default: 0\n    },\n    appendToBody: {\n      type: Boolean,\n      default: true\n    },\n    maskClosable: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data: function data() {\n    return {\n      index: this.initialIndex,\n      isShow: false,\n      infinite: true,\n      loading: false,\n      mode: Mode.CONTAIN,\n      transform: {\n        scale: 1,\n        deg: 0,\n        offsetX: 0,\n        offsetY: 0,\n        enableTransition: false\n      }\n    };\n  },\n  computed: {\n    isSingle: function isSingle() {\n      return this.urlList.length <= 1;\n    },\n    isFirst: function isFirst() {\n      return this.index === 0;\n    },\n    isLast: function isLast() {\n      return this.index === this.urlList.length - 1;\n    },\n    currentImg: function currentImg() {\n      return this.urlList[this.index];\n    },\n    imgStyle: function imgStyle() {\n      var _this$transform = this.transform,\n        scale = _this$transform.scale,\n        deg = _this$transform.deg,\n        offsetX = _this$transform.offsetX,\n        offsetY = _this$transform.offsetY,\n        enableTransition = _this$transform.enableTransition;\n      var style = {\n        transform: \"scale(\".concat(scale, \") rotate(\").concat(deg, \"deg)\"),\n        transition: enableTransition ? 'transform .3s' : '',\n        'margin-left': \"\".concat(offsetX, \"px\"),\n        'margin-top': \"\".concat(offsetY, \"px\")\n      };\n      if (this.mode === Mode.CONTAIN) {\n        style.maxWidth = style.maxHeight = '100%';\n      }\n      return style;\n    },\n    viewerZIndex: function viewerZIndex() {\n      var nextZIndex = PopupManager.nextZIndex();\n      return this.zIndex > nextZIndex ? this.zIndex : nextZIndex;\n    }\n  },\n  watch: {\n    index: {\n      handler: function handler(val) {\n        this.reset();\n        this.onSwitch(val);\n      }\n    },\n    currentImg: function currentImg(val) {\n      var _this = this;\n      this.$nextTick(function (_) {\n        var $img = _this.$refs.img[0];\n        if (!$img.complete) {\n          _this.loading = true;\n        }\n      });\n    }\n  },\n  methods: {\n    hide: function hide() {\n      this.deviceSupportUninstall();\n      this.onClose();\n    },\n    deviceSupportInstall: function deviceSupportInstall() {\n      var _this2 = this;\n      this._keyDownHandler = function (e) {\n        e.stopPropagation();\n        var keyCode = e.keyCode;\n        switch (keyCode) {\n          // ESC\n          case 27:\n            _this2.hide();\n            break;\n          // SPACE\n          case 32:\n            _this2.toggleMode();\n            break;\n          // LEFT_ARROW\n          case 37:\n            _this2.prev();\n            break;\n          // UP_ARROW\n          case 38:\n            _this2.handleActions('zoomIn');\n            break;\n          // RIGHT_ARROW\n          case 39:\n            _this2.next();\n            break;\n          // DOWN_ARROW\n          case 40:\n            _this2.handleActions('zoomOut');\n            break;\n        }\n      };\n      this._mouseWheelHandler = rafThrottle(function (e) {\n        var delta = e.wheelDelta ? e.wheelDelta : -e.detail;\n        if (delta > 0) {\n          _this2.handleActions('zoomIn', {\n            zoomRate: 0.015,\n            enableTransition: false\n          });\n        } else {\n          _this2.handleActions('zoomOut', {\n            zoomRate: 0.015,\n            enableTransition: false\n          });\n        }\n      });\n      on(document, 'keydown', this._keyDownHandler);\n      on(document, mousewheelEventName, this._mouseWheelHandler);\n    },\n    deviceSupportUninstall: function deviceSupportUninstall() {\n      off(document, 'keydown', this._keyDownHandler);\n      off(document, mousewheelEventName, this._mouseWheelHandler);\n      this._keyDownHandler = null;\n      this._mouseWheelHandler = null;\n    },\n    handleImgLoad: function handleImgLoad(e) {\n      this.loading = false;\n    },\n    handleImgError: function handleImgError(e) {\n      this.loading = false;\n      e.target.alt = '加载失败';\n    },\n    handleMouseDown: function handleMouseDown(e) {\n      var _this3 = this;\n      if (this.loading || e.button !== 0) return;\n      var _this$transform2 = this.transform,\n        offsetX = _this$transform2.offsetX,\n        offsetY = _this$transform2.offsetY;\n      var startX = e.pageX;\n      var startY = e.pageY;\n      this._dragHandler = rafThrottle(function (ev) {\n        _this3.transform.offsetX = offsetX + ev.pageX - startX;\n        _this3.transform.offsetY = offsetY + ev.pageY - startY;\n      });\n      on(document, 'mousemove', this._dragHandler);\n      on(document, 'mouseup', function (ev) {\n        off(document, 'mousemove', _this3._dragHandler);\n      });\n      e.preventDefault();\n    },\n    handleMaskClick: function handleMaskClick() {\n      if (this.maskClosable) {\n        this.hide();\n      }\n    },\n    reset: function reset() {\n      this.transform = {\n        scale: 1,\n        deg: 0,\n        offsetX: 0,\n        offsetY: 0,\n        enableTransition: false\n      };\n    },\n    toggleMode: function toggleMode() {\n      if (this.loading) return;\n      var modeNames = Object.keys(Mode);\n      var modeValues = Object.values(Mode);\n      var index = modeValues.indexOf(this.mode);\n      var nextIndex = (index + 1) % modeNames.length;\n      this.mode = Mode[modeNames[nextIndex]];\n      this.reset();\n    },\n    prev: function prev() {\n      if (this.isFirst && !this.infinite) return;\n      var len = this.urlList.length;\n      this.index = (this.index - 1 + len) % len;\n    },\n    next: function next() {\n      if (this.isLast && !this.infinite) return;\n      var len = this.urlList.length;\n      this.index = (this.index + 1) % len;\n    },\n    handleActions: function handleActions(action) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (this.loading) return;\n      var _zoomRate$rotateDeg$e = _objectSpread({\n          zoomRate: 0.2,\n          rotateDeg: 90,\n          enableTransition: true\n        }, options),\n        zoomRate = _zoomRate$rotateDeg$e.zoomRate,\n        rotateDeg = _zoomRate$rotateDeg$e.rotateDeg,\n        enableTransition = _zoomRate$rotateDeg$e.enableTransition;\n      var transform = this.transform;\n      switch (action) {\n        case 'zoomOut':\n          if (transform.scale > 0.2) {\n            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3));\n          }\n          break;\n        case 'zoomIn':\n          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));\n          break;\n        case 'clocelise':\n          transform.deg += rotateDeg;\n          break;\n        case 'anticlocelise':\n          transform.deg -= rotateDeg;\n          break;\n      }\n      transform.enableTransition = enableTransition;\n    }\n  },\n  mounted: function mounted() {\n    this.deviceSupportInstall();\n    if (this.appendToBody) {\n      document.body.appendChild(this.$el);\n    }\n    // add tabindex then wrapper can be focusable via Javascript\n    // focus wrapper so arrow key can't cause inner scroll behavior underneath\n    this.$refs['el-image-viewer__wrapper'].focus();\n  },\n  destroyed: function destroyed() {\n    // if appendToBody is true, remove DOM node after destroy\n    if (this.appendToBody && this.$el && this.$el.parentNode) {\n      this.$el.parentNode.removeChild(this.$el);\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;AAsDA;AACA;AACA;AAEA;EACAA;IACAC;IACAC;EACA;EACAC;IACAF;IACAC;EACA;AACA;AAEA;AAEA;EACAD;EAEAG;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;EACA;EAEAO;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAAV;QAAAC;QAAAC;QAAAC;QAAAC;MACA;QACAL;QACAY;QACA;QACA;MACA;MACA;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACApB;MACAqB;QACA;QACA;MACA;IACA;IACAN;MAAA;MACA;QACA;QACA;UACAO;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACA;QACA;UACA;UACA;YACAC;YACA;UACA;UACA;YACAA;YACA;UACA;UACA;YACAA;YACA;UACA;UACA;YACAA;YACA;UACA;UACA;YACAA;YACA;UACA;UACA;YACAA;YACA;QACA;MACA;MACA;QACA;QACA;UACAA;YACAC;YACAlB;UACA;QACA;UACAiB;YACAC;YACAlB;UACA;QACA;MACA;MACAmB;MACAA;IACA;IACAC;MACAC;MACAA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAP;IACA;IACAQ;MAAA;MACA;MAEA;QAAA1B;QAAAC;MACA;MACA;MACA;QACA0B;QACAA;MACA;MACAN;MACAA;QACAE;MACA;MAEAL;IACA;IACAU;MACA;QACA;MACA;IACA;IACAC;MACA;QACA/B;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACA4B;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;UACAb;UACAc;UACAhC;QAAA,GACAiC;QAJAf;QAAAc;QAAAhC;MAMA;MACA;QACA;UACA;YACAL;UACA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;MACA;MACAA;IACA;EACA;EACAuC;IACA;IACA;MACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;AACA", "names": ["CONTAIN", "name", "icon", "ORIGINAL", "props", "urlList", "type", "default", "zIndex", "onSwitch", "onClose", "initialIndex", "appendToBody", "maskClosable", "data", "index", "isShow", "infinite", "loading", "mode", "transform", "scale", "deg", "offsetX", "offsetY", "enableTransition", "computed", "isSingle", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "currentImg", "imgStyle", "transition", "style", "viewerZIndex", "watch", "handler", "_this", "methods", "hide", "deviceSupportInstall", "e", "_this2", "zoomRate", "on", "deviceSupportUninstall", "off", "handleImgLoad", "handleImgError", "handleMouseDown", "_this3", "handleMaskClick", "reset", "toggleMode", "prev", "next", "handleActions", "rotateDeg", "options", "mounted", "document", "destroyed"], "sourceRoot": "node_modules/element-ui/packages/image/src", "sources": ["image-viewer.vue"], "sourcesContent": ["<template>\n  <transition name=\"viewer-fade\">\n    <div tabindex=\"-1\" ref=\"el-image-viewer__wrapper\" class=\"el-image-viewer__wrapper\" :style=\"{ 'z-index': viewerZIndex }\">\n      <div class=\"el-image-viewer__mask\" @click.self=\"handleMaskClick\"></div>\n      <!-- CLOSE -->\n      <span class=\"el-image-viewer__btn el-image-viewer__close\" @click=\"hide\">\n        <i class=\"el-icon-close\"></i>\n      </span>\n      <!-- ARROW -->\n      <template v-if=\"!isSingle\">\n        <span\n          class=\"el-image-viewer__btn el-image-viewer__prev\"\n          :class=\"{ 'is-disabled': !infinite && isFirst }\"\n          @click=\"prev\">\n          <i class=\"el-icon-arrow-left\"/>\n        </span>\n        <span\n          class=\"el-image-viewer__btn el-image-viewer__next\"\n          :class=\"{ 'is-disabled': !infinite && isLast }\"\n          @click=\"next\">\n          <i class=\"el-icon-arrow-right\"/>\n        </span>\n      </template>\n      <!-- ACTIONS -->\n      <div class=\"el-image-viewer__btn el-image-viewer__actions\">\n        <div class=\"el-image-viewer__actions__inner\">\n          <i class=\"el-icon-zoom-out\" @click=\"handleActions('zoomOut')\"></i>\n          <i class=\"el-icon-zoom-in\" @click=\"handleActions('zoomIn')\"></i>\n          <i class=\"el-image-viewer__actions__divider\"></i>\n          <i :class=\"mode.icon\" @click=\"toggleMode\"></i>\n          <i class=\"el-image-viewer__actions__divider\"></i>\n          <i class=\"el-icon-refresh-left\" @click=\"handleActions('anticlocelise')\"></i>\n          <i class=\"el-icon-refresh-right\" @click=\"handleActions('clocelise')\"></i>\n        </div>\n      </div>\n      <!-- CANVAS -->\n      <div class=\"el-image-viewer__canvas\">\n        <img\n          v-for=\"(url, i) in urlList\"\n          v-if=\"i === index\"\n          ref=\"img\"\n          class=\"el-image-viewer__img\"\n          :key=\"url\"\n          :src=\"currentImg\"\n          :style=\"imgStyle\"\n          @load=\"handleImgLoad\"\n          @error=\"handleImgError\"\n          @mousedown=\"handleMouseDown\">\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script>\nimport { on, off } from 'element-ui/src/utils/dom';\nimport { rafThrottle, isFirefox } from 'element-ui/src/utils/util';\nimport { PopupManager } from 'element-ui/src/utils/popup';\n\nconst Mode = {\n  CONTAIN: {\n    name: 'contain',\n    icon: 'el-icon-full-screen'\n  },\n  ORIGINAL: {\n    name: 'original',\n    icon: 'el-icon-c-scale-to-original'\n  }\n};\n\nconst mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';\n\nexport default {\n  name: 'elImageViewer',\n\n  props: {\n    urlList: {\n      type: Array,\n      default: () => []\n    },\n    zIndex: {\n      type: Number,\n      default: 2000\n    },\n    onSwitch: {\n      type: Function,\n      default: () => {}\n    },\n    onClose: {\n      type: Function,\n      default: () => {}\n    },\n    initialIndex: {\n      type: Number,\n      default: 0\n    },\n    appendToBody: {\n      type: Boolean,\n      default: true\n    },\n    maskClosable: {\n      type: Boolean,\n      default: true\n    }\n  },\n\n  data() {\n    return {\n      index: this.initialIndex,\n      isShow: false,\n      infinite: true,\n      loading: false,\n      mode: Mode.CONTAIN,\n      transform: {\n        scale: 1,\n        deg: 0,\n        offsetX: 0,\n        offsetY: 0,\n        enableTransition: false\n      }\n    };\n  },\n  computed: {\n    isSingle() {\n      return this.urlList.length <= 1;\n    },\n    isFirst() {\n      return this.index === 0;\n    },\n    isLast() {\n      return this.index === this.urlList.length - 1;\n    },\n    currentImg() {\n      return this.urlList[this.index];\n    },\n    imgStyle() {\n      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;\n      const style = {\n        transform: `scale(${scale}) rotate(${deg}deg)`,\n        transition: enableTransition ? 'transform .3s' : '',\n        'margin-left': `${offsetX}px`,\n        'margin-top': `${offsetY}px`\n      };\n      if (this.mode === Mode.CONTAIN) {\n        style.maxWidth = style.maxHeight = '100%';\n      }\n      return style;\n    },\n    viewerZIndex() {\n      const nextZIndex = PopupManager.nextZIndex();\n      return this.zIndex > nextZIndex ? this.zIndex : nextZIndex;\n    }\n  },\n  watch: {\n    index: {\n      handler: function(val) {\n        this.reset();\n        this.onSwitch(val);\n      }\n    },\n    currentImg(val) {\n      this.$nextTick(_ => {\n        const $img = this.$refs.img[0];\n        if (!$img.complete) {\n          this.loading = true;\n        }\n      });\n    }\n  },\n  methods: {\n    hide() {\n      this.deviceSupportUninstall();\n      this.onClose();\n    },\n    deviceSupportInstall() {\n      this._keyDownHandler = e => {\n        e.stopPropagation();\n        const keyCode = e.keyCode;\n        switch (keyCode) {\n          // ESC\n          case 27:\n            this.hide();\n            break;\n          // SPACE\n          case 32:\n            this.toggleMode();\n            break;\n          // LEFT_ARROW\n          case 37:\n            this.prev();\n            break;\n          // UP_ARROW\n          case 38:\n            this.handleActions('zoomIn');\n            break;\n          // RIGHT_ARROW\n          case 39:\n            this.next();\n            break;\n          // DOWN_ARROW\n          case 40:\n            this.handleActions('zoomOut');\n            break;\n        }\n      };\n      this._mouseWheelHandler = rafThrottle(e => {\n        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;\n        if (delta > 0) {\n          this.handleActions('zoomIn', {\n            zoomRate: 0.015,\n            enableTransition: false\n          });\n        } else {\n          this.handleActions('zoomOut', {\n            zoomRate: 0.015,\n            enableTransition: false\n          });\n        }\n      });\n      on(document, 'keydown', this._keyDownHandler);\n      on(document, mousewheelEventName, this._mouseWheelHandler);\n    },\n    deviceSupportUninstall() {\n      off(document, 'keydown', this._keyDownHandler);\n      off(document, mousewheelEventName, this._mouseWheelHandler);\n      this._keyDownHandler = null;\n      this._mouseWheelHandler = null;\n    },\n    handleImgLoad(e) {\n      this.loading = false;\n    },\n    handleImgError(e) {\n      this.loading = false;\n      e.target.alt = '加载失败';\n    },\n    handleMouseDown(e) {\n      if (this.loading || e.button !== 0) return;\n\n      const { offsetX, offsetY } = this.transform;\n      const startX = e.pageX;\n      const startY = e.pageY;\n      this._dragHandler = rafThrottle(ev => {\n        this.transform.offsetX = offsetX + ev.pageX - startX;\n        this.transform.offsetY = offsetY + ev.pageY - startY;\n      });\n      on(document, 'mousemove', this._dragHandler);\n      on(document, 'mouseup', ev => {\n        off(document, 'mousemove', this._dragHandler);\n      });\n\n      e.preventDefault();\n    },\n    handleMaskClick() {\n      if (this.maskClosable) {\n        this.hide();\n      }\n    },\n    reset() {\n      this.transform = {\n        scale: 1,\n        deg: 0,\n        offsetX: 0,\n        offsetY: 0,\n        enableTransition: false\n      };\n    },\n    toggleMode() {\n      if (this.loading) return;\n\n      const modeNames = Object.keys(Mode);\n      const modeValues = Object.values(Mode);\n      const index = modeValues.indexOf(this.mode);\n      const nextIndex = (index + 1) % modeNames.length;\n      this.mode = Mode[modeNames[nextIndex]];\n      this.reset();\n    },\n    prev() {\n      if (this.isFirst && !this.infinite) return;\n      const len = this.urlList.length;\n      this.index = (this.index - 1 + len) % len;\n    },\n    next() {\n      if (this.isLast && !this.infinite) return;\n      const len = this.urlList.length;\n      this.index = (this.index + 1) % len;\n    },\n    handleActions(action, options = {}) {\n      if (this.loading) return;\n      const { zoomRate, rotateDeg, enableTransition } = {\n        zoomRate: 0.2,\n        rotateDeg: 90,\n        enableTransition: true,\n        ...options\n      };\n      const { transform } = this;\n      switch (action) {\n        case 'zoomOut':\n          if (transform.scale > 0.2) {\n            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3));\n          }\n          break;\n        case 'zoomIn':\n          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));\n          break;\n        case 'clocelise':\n          transform.deg += rotateDeg;\n          break;\n        case 'anticlocelise':\n          transform.deg -= rotateDeg;\n          break;\n      }\n      transform.enableTransition = enableTransition;\n    }\n  },\n  mounted() {\n    this.deviceSupportInstall();\n    if (this.appendToBody) {\n      document.body.appendChild(this.$el);\n    }\n    // add tabindex then wrapper can be focusable via Javascript\n    // focus wrapper so arrow key can't cause inner scroll behavior underneath\n    this.$refs['el-image-viewer__wrapper'].focus();\n  },\n  destroyed() {\n    // if appendToBody is true, remove DOM node after destroy\n    if (this.appendToBody && this.$el && this.$el.parentNode) {\n      this.$el.parentNode.removeChild(this.$el);\n    }\n  }\n};\n</script>\n"]}, "metadata": {}, "sourceType": "module"}