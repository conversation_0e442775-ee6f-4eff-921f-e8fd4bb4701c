package com.kum.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version V1.0
 * @Package com.kum.domain.entity
 * @auhter SunGuangJie
 * @date 2021/3/20-9:21 AM
 */

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_user_role")
public class SysUserRole {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 角色ID
     */
    private Integer roleId;
}
