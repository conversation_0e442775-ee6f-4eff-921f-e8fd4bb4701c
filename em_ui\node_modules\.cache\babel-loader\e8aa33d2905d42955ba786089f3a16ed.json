{"ast": null, "code": "export default {\n  name: 'ThemeSetting',\n  props: {},\n  data: function data() {\n    return {\n      isShow: false\n    };\n  },\n  computed: {\n    showHeader: {\n      get: function get() {\n        return this.$store.state.settings.showHeader;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'showHeader': newValue\n        });\n      }\n    },\n    enableSidebarCollapse: {\n      get: function get() {\n        return this.$store.state.settings.enableSidebarCollapse;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'enableSidebarCollapse': newValue\n        });\n      }\n    },\n    sidebarCollapse: {\n      get: function get() {\n        return this.$store.state.settings.sidebarCollapse;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'sidebarCollapse': newValue\n        });\n      }\n    },\n    showCopyright: {\n      get: function get() {\n        return this.$store.state.settings.showCopyright;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'showCopyright': newValue\n        });\n      }\n    },\n    enableNavSearch: {\n      get: function get() {\n        return this.$store.state.settings.enableNavSearch;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'enableNavSearch': newValue\n        });\n      }\n    },\n    enableFullscreen: {\n      get: function get() {\n        return this.$store.state.settings.enableFullscreen;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'enableFullscreen': newValue\n        });\n      }\n    },\n    enablePageReload: {\n      get: function get() {\n        return this.$store.state.settings.enablePageReload;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'enablePageReload': newValue\n        });\n      }\n    },\n    enableProgress: {\n      get: function get() {\n        return this.$store.state.settings.enableProgress;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'enableProgress': newValue\n        });\n      }\n    },\n    enableDynamicTitle: {\n      get: function get() {\n        return this.$store.state.settings.enableDynamicTitle;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'enableDynamicTitle': newValue\n        });\n      }\n    },\n    enableDashboard: {\n      get: function get() {\n        return this.$store.state.settings.enableDashboard;\n      },\n      set: function set(newValue) {\n        this.$store.commit('settings/updateThemeSetting', {\n          'enableDashboard': newValue\n        });\n      }\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.$eventBus.$on('global-theme-toggle', function () {\n      _this.isShow = !_this.isShow;\n    });\n  },\n  methods: {}\n};", "map": {"version": 3, "mappings": "AA8EA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAC;MACAF;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAE;MACAH;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAG;MACAJ;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAI;MACAL;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAK;MACAN;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAM;MACAP;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAO;MACAR;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAQ;MACAT;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;IACAS;MACAV;QACA;MACA;MACAC;QACA;UACA;QACA;MACA;IACA;EACA;EACAU;IAAA;IACA;MACAC;IACA;EACA;EACAC;AACA", "names": ["name", "props", "data", "isShow", "computed", "showHeader", "get", "set", "enableSidebarCollapse", "sidebarCollapse", "show<PERSON>opyright", "enableNavSearch", "enableFullscreen", "enablePageReload", "enableProgress", "enableDynamicTitle", "enableDashboard", "mounted", "_this", "methods"], "sourceRoot": "src/layout/components/ThemeSetting", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div>\n        <el-drawer title=\"主题配置\" :visible.sync=\"isShow\" direction=\"rtl\" :size=\"$store.state.settings.mode == 'pc' ? '500px' : '300px'\">\n            <el-alert title=\"主题配置可实时预览效果，更多设置请在 src/setting.js 中进行设置，建议在生产环境隐藏主题配置功能\" type=\"error\" :closable=\"false\" />\n            <el-form ref=\"form\" :label-position=\"$store.state.settings.mode == 'pc' ? 'right' : 'top'\" label-width=\"100px\" size=\"small\">\n                <el-form-item v-if=\"$store.state.settings.mode == 'pc'\" label=\"头部\">\n                    <el-radio-group v-model=\"showHeader\">\n                        <el-radio-button :label=\"true\">显示</el-radio-button>\n                        <el-radio-button :label=\"false\">隐藏</el-radio-button>\n                    </el-radio-group>\n                </el-form-item>\n                <el-form-item v-if=\"$store.state.settings.mode == 'pc'\" label=\"侧边栏切换\">\n                    <el-radio-group v-model=\"enableSidebarCollapse\">\n                        <el-radio-button :label=\"true\">启用</el-radio-button>\n                        <el-radio-button :label=\"false\">关闭</el-radio-button>\n                    </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"侧边栏导航\">\n                    <el-radio-group v-model=\"sidebarCollapse\">\n                        <el-radio-button :label=\"true\">收起</el-radio-button>\n                        <el-radio-button :label=\"false\">展开</el-radio-button>\n                    </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"底部版权\">\n                    <el-radio-group v-model=\"showCopyright\">\n                        <el-radio-button :label=\"true\">显示</el-radio-button>\n                        <el-radio-button :label=\"false\">隐藏</el-radio-button>\n                    </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"导航搜索\">\n                    <el-radio-group v-model=\"enableNavSearch\">\n                        <el-radio-button :label=\"true\">开启</el-radio-button>\n                        <el-radio-button :label=\"false\">关闭</el-radio-button>\n                    </el-radio-group>\n                    <el-alert title=\"该功能为页面右上角的搜索按钮，可对侧边栏导航进行快捷搜索\" type=\"info\" :closable=\"false\" />\n                </el-form-item>\n                <el-form-item v-if=\"$store.state.settings.mode == 'pc'\" label=\"全屏\">\n                    <el-radio-group v-model=\"enableFullscreen\">\n                        <el-radio-button :label=\"true\">开启</el-radio-button>\n                        <el-radio-button :label=\"false\">关闭</el-radio-button>\n                    </el-radio-group>\n                    <el-alert title=\"该功能为页面右上角的全屏按钮\" type=\"info\" :closable=\"false\" />\n                    <el-alert title=\"不建议开启，该功能使用场景极少，用户习惯于通过窗口“最大化”功能来扩大显示区域，以显示更多内容，并且使用 F11 键也可以进入全屏效果\" type=\"warning\" :closable=\"false\" />\n                </el-form-item>\n                <el-form-item label=\"页面刷新\">\n                    <el-radio-group v-model=\"enablePageReload\">\n                        <el-radio-button :label=\"true\">开启</el-radio-button>\n                        <el-radio-button :label=\"false\">关闭</el-radio-button>\n                    </el-radio-group>\n                    <el-alert title=\"该功能为页面右上角的刷新按钮，开启时会阻止 F5 键原刷新功能，并采用框架提供的刷新模式进行页面刷新\" type=\"info\" :closable=\"false\" />\n                </el-form-item>\n                <el-form-item label=\"加载进度条\">\n                    <el-radio-group v-model=\"enableProgress\">\n                        <el-radio-button :label=\"true\">开启</el-radio-button>\n                        <el-radio-button :label=\"false\">关闭</el-radio-button>\n                    </el-radio-group>\n                    <el-alert title=\"该功能开启时，跳转路由会看到页面顶部有条蓝色的进度条\" type=\"info\" :closable=\"false\" />\n                </el-form-item>\n                <el-form-item label=\"动态标题\">\n                    <el-radio-group v-model=\"enableDynamicTitle\">\n                        <el-radio-button :label=\"true\">开启</el-radio-button>\n                        <el-radio-button :label=\"false\">关闭</el-radio-button>\n                    </el-radio-group>\n                    <el-alert title=\"该功能开启时，页面标题会显示当前路由标题，格式为“页面标题 - 网站名称”；关闭时则显示网站名称，网站名称在项目根目录下 .env.* 文件里配置\" type=\"info\" :closable=\"false\" />\n                </el-form-item>\n                <el-form-item label=\"控制台\">\n                    <el-radio-group v-model=\"enableDashboard\">\n                        <el-radio-button :label=\"true\">开启</el-radio-button>\n                        <el-radio-button :label=\"false\">关闭</el-radio-button>\n                    </el-radio-group>\n                    <el-alert title=\"控制台即欢迎页，该功能开启时，登录成功默认进入控制台；关闭时则默认进入侧边栏导航第一个导航页面\" type=\"info\" :closable=\"false\" />\n                </el-form-item>\n            </el-form>\n        </el-drawer>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'ThemeSetting',\n    props: {},\n    data() {\n        return {\n            isShow: false\n        }\n    },\n    computed: {\n        showHeader: {\n            get: function() {\n                return this.$store.state.settings.showHeader\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'showHeader': newValue\n                })\n            }\n        },\n        enableSidebarCollapse: {\n            get: function() {\n                return this.$store.state.settings.enableSidebarCollapse\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'enableSidebarCollapse': newValue\n                })\n            }\n        },\n        sidebarCollapse: {\n            get: function() {\n                return this.$store.state.settings.sidebarCollapse\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'sidebarCollapse': newValue\n                })\n            }\n        },\n        showCopyright: {\n            get: function() {\n                return this.$store.state.settings.showCopyright\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'showCopyright': newValue\n                })\n            }\n        },\n        enableNavSearch: {\n            get: function() {\n                return this.$store.state.settings.enableNavSearch\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'enableNavSearch': newValue\n                })\n            }\n        },\n        enableFullscreen: {\n            get: function() {\n                return this.$store.state.settings.enableFullscreen\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'enableFullscreen': newValue\n                })\n            }\n        },\n        enablePageReload: {\n            get: function() {\n                return this.$store.state.settings.enablePageReload\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'enablePageReload': newValue\n                })\n            }\n        },\n        enableProgress: {\n            get: function() {\n                return this.$store.state.settings.enableProgress\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'enableProgress': newValue\n                })\n            }\n        },\n        enableDynamicTitle: {\n            get: function() {\n                return this.$store.state.settings.enableDynamicTitle\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'enableDynamicTitle': newValue\n                })\n            }\n        },\n        enableDashboard: {\n            get: function() {\n                return this.$store.state.settings.enableDashboard\n            },\n            set: function(newValue) {\n                this.$store.commit('settings/updateThemeSetting', {\n                    'enableDashboard': newValue\n                })\n            }\n        }\n    },\n    mounted() {\n        this.$eventBus.$on('global-theme-toggle', () => {\n            this.isShow = !this.isShow\n        })\n    },\n    methods: {}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-drawer__wrapper,\n::v-deep .el-drawer__wrapper * {\n    outline: none !important;\n}\n::v-deep .el-drawer__body {\n    padding: 0 20px 20px;\n    overflow: auto;\n}\n.el-form {\n    margin-top: 20px;\n    .el-alert {\n        margin-top: 10px;\n        line-height: initial;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}