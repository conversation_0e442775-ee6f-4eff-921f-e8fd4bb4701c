-- 数据库初始化脚本
-- 注意：此脚本应该在主数据库结构创建完成后执行

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 检查并创建默认管理员用户
INSERT IGNORE INTO sys_user (id, user_name, full_name, password, status, phone, login_ip, login_date)
VALUES ('admin', 'admin', '系统管理员', '$2a$10$7JB720yubVSOfvVMe6/b.eRh4QfVDEm/A5HxtLxeNa/Y/RtxJjrOm', '0', '13800138000', '127.0.0.1', NOW());

-- 检查并创建默认测试用户
INSERT IGNORE INTO sys_user (id, user_name, full_name, password, status, phone, login_ip, login_date)
VALUES ('test', 'test', '测试用户', '$2a$10$7JB720yubVSOfvVMe6/b.eRh4QfVDEm/A5HxtLxeNa/Y/RtxJjrOm', '0', '13800138001', '127.0.0.1', NOW());

-- 检查并创建默认角色权限
INSERT IGNORE INTO sys_user_role (user_id, role_id) VALUES ('admin', 0);
INSERT IGNORE INTO sys_user_role (user_id, role_id) VALUES ('test', 1);

-- 检查并创建默认系统配置
INSERT IGNORE INTO sys_options (id, text) VALUES
('rq_info', '{"rq_name":"小区物业管理系统","rq_address":"北京市朝阳区","rq_phone":"************","rq_email":"<EMAIL>"}'),
('rq_repair_info', '{"rq_repair_info_contact_phone":"13800138000"}');

SET FOREIGN_KEY_CHECKS = 1;
