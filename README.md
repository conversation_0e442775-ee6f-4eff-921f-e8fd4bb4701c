# 小区物业管理系统

基于SpringBoot + Vue实现的现代化小区物业管理系统，提供完整的物业管理功能。

## 技术栈

### 后端
- **Java**: JDK 11
- **框架**: Spring Boot 2.7.18
- **数据库**: MySQL 5.7+
- **ORM**: MyBatis Plus 3.5.4
- **安全**: Spring Security
- **其他**: FastJSON2, EasyExcel, Quartz

### 前端
- **框架**: Vue 2.7.16
- **UI组件**: Element UI 2.15.14 + Ant Design Vue 1.7.8
- **构建工具**: Vue CLI 5.0.8
- **其他**: Axios, Vuex, Vue Router

## 功能特性

### 🏠 基础管理
- 楼宇管理：楼栋、单元、房间信息管理
- 住户管理：住户信息、入住登记、搬迁管理
- 用户管理：系统用户、角色权限管理

### 🔧 服务管理
- 报修管理：在线报修、维修派单、进度跟踪
- 投诉管理：投诉受理、处理流程、满意度评价
- 公告管理：社区公告、通知发布

### 💰 费用管理
- 收费管理：物业费、水电费等各类费用
- 缴费记录：缴费历史、欠费提醒
- 财务报表：收支统计、财务分析

### 🏢 设施管理
- 公共设施：设施登记、维护记录
- 设备管理：设备台账、保养计划

## 快速开始

### 环境要求
- JDK 11+
- MySQL 5.7+
- Node.js 14+
- Maven 3.6+

### 数据库配置
1. 创建数据库：
```sql
CREATE DATABASE estate_management CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

2. 导入数据：
```bash
mysql -u root -p estate_management < em_server/sql/estate_management.sql
mysql -u root -p estate_management < em_server/sql/init_database.sql
```

3. 修改数据库配置：
编辑 `em_server/src/main/resources/application.yml`
```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: root
    password: root  # 修改为你的密码
```

### 后端启动
```bash
cd em_server
mvn clean install
mvn spring-boot:run
```

### 前端启动
```bash
cd em_ui
npm install
npm run serve
```

## 默认账号
- 管理员：admin / 123456
- 普通用户：test / 123456

## 项目结构
```
├── em_server/          # 后端项目
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   └── resources/
│   │   └── test/
│   ├── sql/            # 数据库脚本
│   └── pom.xml
├── em_ui/              # 前端项目
│   ├── src/
│   │   ├── api/        # API接口
│   │   ├── components/ # 组件
│   │   ├── views/      # 页面
│   │   └── assets/     # 静态资源
│   └── package.json
└── README.md
```

## 开发说明

### API接口
- 基础路径：`http://localhost:8082`
- 接口文档：启动后访问 `/doc.html`

### 前端开发
- 开发服务器：`http://localhost:8080`
- 代理配置：自动代理到后端服务

### PNG图标使用
- 图标位置：`em_ui/src/assets/icons/`
- 使用组件：`<png-icon name="图标名" size="24" />`
- 图标示例：访问 `/system-manager/icon-demo` 查看所有可用图标
- 详细指南：参考 `PNG_ICONS_GUIDE.md`

## 部署说明

### 生产环境部署
1. 后端打包：`mvn clean package`
2. 前端打包：`npm run build`
3. 部署jar包和dist目录到服务器

## 许可证
MIT License
