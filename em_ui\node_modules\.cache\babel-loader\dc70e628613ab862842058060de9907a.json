{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nexports.default = {\n  el: {\n    colorpicker: {\n      confirm: '确定',\n      clear: '清空'\n    },\n    datepicker: {\n      now: '此刻',\n      today: '今天',\n      cancel: '取消',\n      clear: '清空',\n      confirm: '确定',\n      selectDate: '选择日期',\n      selectTime: '选择时间',\n      startDate: '开始日期',\n      startTime: '开始时间',\n      endDate: '结束日期',\n      endTime: '结束时间',\n      prevYear: '前一年',\n      nextYear: '后一年',\n      prevMonth: '上个月',\n      nextMonth: '下个月',\n      year: '年',\n      month1: '1 月',\n      month2: '2 月',\n      month3: '3 月',\n      month4: '4 月',\n      month5: '5 月',\n      month6: '6 月',\n      month7: '7 月',\n      month8: '8 月',\n      month9: '9 月',\n      month10: '10 月',\n      month11: '11 月',\n      month12: '12 月',\n      // week: '周次',\n      weeks: {\n        sun: '日',\n        mon: '一',\n        tue: '二',\n        wed: '三',\n        thu: '四',\n        fri: '五',\n        sat: '六'\n      },\n      months: {\n        jan: '一月',\n        feb: '二月',\n        mar: '三月',\n        apr: '四月',\n        may: '五月',\n        jun: '六月',\n        jul: '七月',\n        aug: '八月',\n        sep: '九月',\n        oct: '十月',\n        nov: '十一月',\n        dec: '十二月'\n      }\n    },\n    select: {\n      loading: '加载中',\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      placeholder: '请选择'\n    },\n    cascader: {\n      noMatch: '无匹配数据',\n      loading: '加载中',\n      placeholder: '请选择',\n      noData: '暂无数据'\n    },\n    pagination: {\n      goto: '前往',\n      pagesize: '条/页',\n      total: '共 {total} 条',\n      pageClassifier: '页'\n    },\n    messagebox: {\n      title: '提示',\n      confirm: '确定',\n      cancel: '取消',\n      error: '输入的数据不合法!'\n    },\n    upload: {\n      deleteTip: '按 delete 键可删除',\n      delete: '删除',\n      preview: '查看图片',\n      continue: '继续上传'\n    },\n    table: {\n      emptyText: '暂无数据',\n      confirmFilter: '筛选',\n      resetFilter: '重置',\n      clearFilter: '全部',\n      sumText: '合计'\n    },\n    tree: {\n      emptyText: '暂无数据'\n    },\n    transfer: {\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      titles: ['列表 1', '列表 2'],\n      filterPlaceholder: '请输入搜索内容',\n      noCheckedFormat: '共 {total} 项',\n      hasCheckedFormat: '已选 {checked}/{total} 项'\n    },\n    image: {\n      error: '加载失败'\n    },\n    pageHeader: {\n      title: '返回'\n    },\n    popconfirm: {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消'\n    },\n    empty: {\n      description: '暂无数据'\n    }\n  }\n};", "map": {"version": 3, "names": ["exports", "__esModule", "default", "el", "colorpicker", "confirm", "clear", "datepicker", "now", "today", "cancel", "selectDate", "selectTime", "startDate", "startTime", "endDate", "endTime", "prevYear", "nextYear", "prevMonth", "nextMonth", "year", "month1", "month2", "month3", "month4", "month5", "month6", "month7", "month8", "month9", "month10", "month11", "month12", "weeks", "sun", "mon", "tue", "wed", "thu", "fri", "sat", "months", "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec", "select", "loading", "noMatch", "noData", "placeholder", "cascader", "pagination", "goto", "pagesize", "total", "pageClassifier", "messagebox", "title", "error", "upload", "deleteTip", "delete", "preview", "continue", "table", "emptyText", "confirmFilter", "resetFilter", "clearFilter", "sumText", "tree", "transfer", "titles", "filterPlaceholder", "noCheckedFormat", "hasCheckedFormat", "image", "pageHeader", "popconfirm", "confirmButtonText", "cancelButtonText", "empty", "description"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/lib/locale/lang/zh-CN.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\nexports.default = {\n  el: {\n    colorpicker: {\n      confirm: '确定',\n      clear: '清空'\n    },\n    datepicker: {\n      now: '此刻',\n      today: '今天',\n      cancel: '取消',\n      clear: '清空',\n      confirm: '确定',\n      selectDate: '选择日期',\n      selectTime: '选择时间',\n      startDate: '开始日期',\n      startTime: '开始时间',\n      endDate: '结束日期',\n      endTime: '结束时间',\n      prevYear: '前一年',\n      nextYear: '后一年',\n      prevMonth: '上个月',\n      nextMonth: '下个月',\n      year: '年',\n      month1: '1 月',\n      month2: '2 月',\n      month3: '3 月',\n      month4: '4 月',\n      month5: '5 月',\n      month6: '6 月',\n      month7: '7 月',\n      month8: '8 月',\n      month9: '9 月',\n      month10: '10 月',\n      month11: '11 月',\n      month12: '12 月',\n      // week: '周次',\n      weeks: {\n        sun: '日',\n        mon: '一',\n        tue: '二',\n        wed: '三',\n        thu: '四',\n        fri: '五',\n        sat: '六'\n      },\n      months: {\n        jan: '一月',\n        feb: '二月',\n        mar: '三月',\n        apr: '四月',\n        may: '五月',\n        jun: '六月',\n        jul: '七月',\n        aug: '八月',\n        sep: '九月',\n        oct: '十月',\n        nov: '十一月',\n        dec: '十二月'\n      }\n    },\n    select: {\n      loading: '加载中',\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      placeholder: '请选择'\n    },\n    cascader: {\n      noMatch: '无匹配数据',\n      loading: '加载中',\n      placeholder: '请选择',\n      noData: '暂无数据'\n    },\n    pagination: {\n      goto: '前往',\n      pagesize: '条/页',\n      total: '共 {total} 条',\n      pageClassifier: '页'\n    },\n    messagebox: {\n      title: '提示',\n      confirm: '确定',\n      cancel: '取消',\n      error: '输入的数据不合法!'\n    },\n    upload: {\n      deleteTip: '按 delete 键可删除',\n      delete: '删除',\n      preview: '查看图片',\n      continue: '继续上传'\n    },\n    table: {\n      emptyText: '暂无数据',\n      confirmFilter: '筛选',\n      resetFilter: '重置',\n      clearFilter: '全部',\n      sumText: '合计'\n    },\n    tree: {\n      emptyText: '暂无数据'\n    },\n    transfer: {\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      titles: ['列表 1', '列表 2'],\n      filterPlaceholder: '请输入搜索内容',\n      noCheckedFormat: '共 {total} 项',\n      hasCheckedFormat: '已选 {checked}/{total} 项'\n    },\n    image: {\n      error: '加载失败'\n    },\n    pageHeader: {\n      title: '返回'\n    },\n    popconfirm: {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消'\n    },\n    empty: {\n      description: '暂无数据'\n    }\n  }\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG;EAChBC,EAAE,EAAE;IACFC,WAAW,EAAE;MACXC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACT,CAAC;IACDC,UAAU,EAAE;MACVC,GAAG,EAAE,IAAI;MACTC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZJ,KAAK,EAAE,IAAI;MACXD,OAAO,EAAE,IAAI;MACbM,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACf;MACAC,KAAK,EAAE;QACLC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE;MACP,CAAC;MACDC,MAAM,EAAE;QACNC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,KAAK;QACVC,GAAG,EAAE;MACP;IACF,CAAC;IACDC,MAAM,EAAE;MACNC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,KAAK;MACbC,WAAW,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACRH,OAAO,EAAE,OAAO;MAChBD,OAAO,EAAE,KAAK;MACdG,WAAW,EAAE,KAAK;MAClBD,MAAM,EAAE;IACV,CAAC;IACDG,UAAU,EAAE;MACVC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,aAAa;MACpBC,cAAc,EAAE;IAClB,CAAC;IACDC,UAAU,EAAE;MACVC,KAAK,EAAE,IAAI;MACX9D,OAAO,EAAE,IAAI;MACbK,MAAM,EAAE,IAAI;MACZ0D,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNC,SAAS,EAAE,eAAe;MAC1BC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE;MACLC,SAAS,EAAE,MAAM;MACjBC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC;IACDC,IAAI,EAAE;MACJL,SAAS,EAAE;IACb,CAAC;IACDM,QAAQ,EAAE;MACRxB,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,KAAK;MACbwB,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MACxBC,iBAAiB,EAAE,SAAS;MAC5BC,eAAe,EAAE,aAAa;MAC9BC,gBAAgB,EAAE;IACpB,CAAC;IACDC,KAAK,EAAE;MACLlB,KAAK,EAAE;IACT,CAAC;IACDmB,UAAU,EAAE;MACVpB,KAAK,EAAE;IACT,CAAC;IACDqB,UAAU,EAAE;MACVC,iBAAiB,EAAE,IAAI;MACvBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,KAAK,EAAE;MACLC,WAAW,EAAE;IACf;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}