<template>
  <div class="dashboard">
    <page-header title="数据统计">
      <template #content>
        <div>物业管理系统数据概览</div>
      </template>
    </page-header>
    
    <page-main>
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon building">
                <png-icon name="building" size="24" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.buildingCount }}</div>
                <div class="stat-label">楼宇总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon room">
                <png-icon name="home" size="24" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.roomCount }}</div>
                <div class="stat-label">房间总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon user">
                <i class="el-icon-user"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.userCount }}</div>
                <div class="stat-label">住户总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon repair">
                <png-icon name="repair" size="24" />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.repairCount }}</div>
                <div class="stat-label">本月报修</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-section">
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>月度报修趋势</span>
            </div>
            <div class="chart-container" id="repairChart"></div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>费用收缴情况</span>
            </div>
            <div class="chart-container" id="paymentChart"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 最新动态 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>最新报修</span>
            </div>
            <el-table :data="recentRepairs" style="width: 100%">
              <el-table-column prop="title" label="报修内容" width="200"></el-table-column>
              <el-table-column prop="userName" label="报修人" width="100"></el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="时间"></el-table-column>
            </el-table>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>最新投诉</span>
            </div>
            <el-table :data="recentComplaints" style="width: 100%">
              <el-table-column prop="title" label="投诉内容" width="200"></el-table-column>
              <el-table-column prop="userName" label="投诉人" width="100"></el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="时间"></el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </page-main>
  </div>
</template>

<script>
import { getOverview, getRecentRepairs, getRecentComplaints } from '@/api/requests/statistics'

export default {
  name: 'Dashboard',
  data() {
    return {
      stats: {
        buildingCount: 0,
        roomCount: 0,
        userCount: 0,
        repairCount: 0
      },
      recentRepairs: [],
      recentComplaints: [],
      loading: false
    }
  },
  mounted() {
    this.loadStats()
    this.loadRecentData()
  },
  methods: {
    async loadStats() {
      try {
        this.loading = true
        const response = await getOverview()
        if (response.code === 200) {
          this.stats = response.data
        } else {
          // 如果API调用失败，使用模拟数据
          this.stats = {
            buildingCount: 12,
            roomCount: 368,
            userCount: 892,
            repairCount: 23
          }
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 使用模拟数据作为后备
        this.stats = {
          buildingCount: 12,
          roomCount: 368,
          userCount: 892,
          repairCount: 23
        }
      } finally {
        this.loading = false
      }
    },

    async loadRecentData() {
      try {
        // 加载最新报修数据
        const repairResponse = await getRecentRepairs()
        if (repairResponse.code === 200) {
          this.recentRepairs = repairResponse.data.slice(0, 5) // 只显示最新5条
        }

        // 加载最新投诉数据
        const complaintResponse = await getRecentComplaints()
        if (complaintResponse.code === 200) {
          this.recentComplaints = complaintResponse.data.slice(0, 5) // 只显示最新5条
        }
      } catch (error) {
        console.error('加载最新数据失败:', error)
        // 使用模拟数据作为后备
        this.recentRepairs = [
          { title: '水管漏水', userName: '张三', status: '处理中', createTime: '2024-01-15 10:30' },
          { title: '电梯故障', userName: '李四', status: '已完成', createTime: '2024-01-15 09:15' },
          { title: '门锁损坏', userName: '王五', status: '待处理', createTime: '2024-01-15 08:45' }
        ]

        this.recentComplaints = [
          { title: '噪音扰民', userName: '赵六', status: '处理中', createTime: '2024-01-15 11:20' },
          { title: '垃圾清理不及时', userName: '钱七', status: '已完成', createTime: '2024-01-15 10:10' }
        ]
      }
    },

    getStatusType(status) {
      const statusMap = {
        '待处理': 'warning',
        '处理中': 'primary',
        '已完成': 'success'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          
          i {
            font-size: 24px;
            color: white;
          }

          .png-icon {
            filter: brightness(0) invert(1);
          }
          
          &.building {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.room {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.user {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.repair {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .stat-info {
          .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 5px;
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
    
    .chart-container {
      height: 300px;
    }
  }
}
</style>
