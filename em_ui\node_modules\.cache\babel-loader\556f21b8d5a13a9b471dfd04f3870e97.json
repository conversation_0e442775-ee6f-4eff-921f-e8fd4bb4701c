{"ast": null, "code": "function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };", "map": {"version": 3, "names": ["_arrayWithHoles", "r", "Array", "isArray", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js"], "sourcesContent": ["function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAE;EAC1B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOA,CAAC;AAChC;AACA,SAASD,eAAe,IAAII,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}