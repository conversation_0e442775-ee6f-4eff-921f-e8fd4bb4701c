{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  props: ['title', 'num', 'tip', 'type', 'icon'],\n  data: function data() {\n    return {};\n  },\n  methods: {\n    openRouter: function openRouter() {\n      this.$router.push(this.title);\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAmBA;EACAA;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA", "names": ["props", "data", "methods", "openRouter"], "sourceRoot": "src/views/component_extend_example", "sources": ["card.vue"], "sourcesContent": ["<template>\n  <div\n    class=\"el-card mini-card is-hover-shadow\"\n    style=\"background: linear-gradient(76deg, rgb(132, 60, 246), rgb(117, 155, 255));\"\n  >\n    <div :class=\"type\">\n      <!-- <div class=\"el-card__header\">\n        <div>{{title}}</div>\n      </div>-->\n      <div class=\"el-card__body\">\n        <el-button class=\"num\" type=\"text\" @click=\"openRouter()\">{{num}}</el-button>\n        <div class=\"tip\">{{tip}}</div>\n        <i :class=\"icon\"></i>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  props: ['title', 'num', 'tip', 'type', 'icon'],\n  data () {\n    return {\n\n    }\n  },\n  methods: {\n    openRouter () {\n      this.$router.push(this.title)\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.violet {\n    background: linear-gradient(76deg, rgb(45, 34, 65), rgb(117, 155, 255));\n}\n.aaa {\n    background: linear-gradient(76deg, rgb(202, 21, 30), rgb(233, 163, 71));\n}\n.pink {\n    background: linear-gradient(50deg, rgb(251, 170, 162), rgb(252, 82, 134));\n}\n.orange {\n    background: linear-gradient(50deg, rgb(255, 118, 59), rgb(255, 196, 128));\n}\n.blue {\n    background: linear-gradient(50deg, rgb(106, 142, 255), rgb(14, 76, 253));\n}\ni {\n    font-size: 120px;\n    position: absolute;\n    right: -30px;\n    top: -10px;\n    -webkit-transform: rotate(15deg);\n    transform: rotate(15deg);\n}\n.mini-card {\n    width: 255px;\n    position: relative;\n    color: #fff;\n    text-shadow: 0 0 2px #000;\n}\n.mini-card .num {\n    color: #fff;\n    font-size: 36px;\n}\n.mini-card .tip {\n    margin-top: 10px;\n    font-size: 14px;\n    color: #eee;\n}\n.el-card__header {\n    border-bottom: 0 solid #ebeef5;\n}\n</style>"]}, "metadata": {}, "sourceType": "module"}