{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getComplaint, examineComplaint, deleteComplaint, getComplaintByUserId } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      isUser: true,\n      loading: false,\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      },\n      table_selectedRowKeys: [],\n      complaint_query_type: 'title',\n      complaint_query_buttonTitle: '搜索',\n      complaint_query_text: '',\n      complaint_save_title: '投诉申请单审核',\n      complaint_save_modalVisible: false,\n      complaint_see_modalVisible: false,\n      complaint_form_data: {},\n      complaint_data_list: []\n    };\n  },\n  created: function created() {\n    this.isUser = this.$route.path.indexOf('user') != -1;\n    this.Get_complaintDataList();\n  },\n  watch: {\n    complaint_save_modalVisible: function complaint_save_modalVisible(val) {\n      if (!val) {\n        this.complaint_form_data = {};\n      }\n    }\n  },\n  methods: {\n    Get_complaintDataList: function Get_complaintDataList() {\n      var _this = this;\n      var method = getComplaintByUserId();\n      console.log(localStorage.getItem('isAdmin'));\n      if (localStorage.getItem('isAdmin') == 'true') {\n        method = getComplaint();\n      }\n      method.then(function (res) {\n        _this.complaint_query_buttonTitle = '搜索';\n        _this.complaint_data_list = res.data;\n      });\n    },\n    Query_complaintDataList: function Query_complaintDataList() {\n      var _this2 = this;\n      var text = this.complaint_query_text;\n      var temp_list = [];\n      this.complaint_data_list.forEach(function (item) {\n        if (item[_this2.complaint_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.complaint_query_buttonTitle = '返回';\n      this.complaint_data_list = temp_list;\n    },\n    Edit_complaintData: function Edit_complaintData(form) {\n      this.complaint_form_data = JSON.parse(JSON.stringify(form));\n      this.complaint_save_modalVisible = true;\n    },\n    Del_complaintData: function Del_complaintData(id) {\n      var _this3 = this;\n      deleteComplaint(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_complaintDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_complaintData(_this4.complaint_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_complaintData: function Save_complaintData() {\n      var _this5 = this;\n      examineComplaint(this.complaint_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.complaint_save_modalVisible = false;\n        _this5.Get_complaintDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    See_complaintDateModal: function See_complaintDateModal(form) {\n      this.complaint_form_data = JSON.parse(JSON.stringify(form));\n      this.complaint_see_modalVisible = true;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AA2KA;AACA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAL;MACA;QACA;MACA;IACA;EACA;EACAM;IACAC;MAAA;MACA;MACAC;MACA;QACAC;MACA;MACAA;QACAC;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA", "names": ["data", "isUser", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "complaint_query_type", "complaint_query_buttonTitle", "complaint_query_text", "complaint_save_title", "complaint_save_modalVisible", "complaint_see_modalVisible", "complaint_form_data", "complaint_data_list", "created", "watch", "methods", "Get_complaintDataList", "console", "method", "_this", "Query_complaintDataList", "temp_list", "Edit_complaintData", "Del_complaintData", "deleteComplaint", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_complaintData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this5", "Table_selectChange", "See_complaintDateModal"], "sourceRoot": "src/views/admin/rq/guarantee", "sources": ["rq_complaint_manager.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"投诉管理\">\n      <div class=\"head\" v-if=\"!isUser\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"保修单标题\"\n          v-model=\"complaint_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"title\">投诉原因</a-select-option>\n          <a-select-option value=\"userName\">用户名称</a-select-option>\n          <a-select-option value=\"address\">用户住址</a-select-option>\n          <a-select-option value=\"phone\">电话</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"complaint_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"complaint_query_buttonTitle\"\n          size=\"large\"\n          @search=\"complaint_query_buttonTitle == '搜索' ? Query_complaintDataList() : Get_complaintDataList()\"\n        />\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「投诉单」</a-button>\n      </div>\n      <a-table\n        :data-source=\"complaint_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"id\" title=\"投诉编号\" data-index=\"id\" />\n        <a-table-column key=\"date\" title=\"发起时间\" data-index=\"date\">\n          <!-- rTime -->\n          <template slot-scope=\"text, record\">\n            <span>{{String(record.date).substr(0,10)}}</span>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"userName\" title=\"投诉人\" data-index=\"userName\" />\n        <a-table-column key=\"title\" title=\"投诉原因\" data-index=\"title\" />\n        <a-table-column key=\"phone\" title=\"联系方式\" data-index=\"phone\" />\n        <a-table-column key=\"address\" title=\"用户住址\" data-index=\"address\" />\n        <a-table-column key=\"isExamine\" title=\"是否处理\" data-index=\"isExamine\">\n          <template slot-scope=\"text, record\">\n                <a-tooltip placement=\"top\">\n        <template slot=\"title\">\n          <span>点我查看详情</span>\n        </template>\n        <div @click=\"See_complaintDateModal(record)\">\n              <a-tag :color=\"record.isExamine == 1 ? 'red' : 'blue'\" >\n              {{\n              record.isExamine == 1 ? '已处理' : '未处理'\n              }}\n            </a-tag>\n            </div>\n\n      </a-tooltip>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"action\" title=\"操作\" v-if=\"!isUser\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button\n                :disabled=\"record.isExamine == 1\"\n                type=\"primary\"\n                @click=\"Edit_complaintData(record)\"\n              >审核</a-button>\n              <a-button type=\"danger\" @click=\"Del_complaintData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"complaint_save_modalVisible\"\n      :title=\"complaint_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_complaintData\"\n    >\n      <a-form-model :model=\"complaint_form_data\" :label-col=\"labelCol\" :wrapper-col=\"wrapperCol\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"申请人\">\n              <span>{{complaint_form_data.userName}}</span>\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"联系方式\">\n              <span>{{complaint_form_data.phone}}</span>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"保修单标题\">\n              <span>{{complaint_form_data.title}}</span>\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"用户住址\">\n              <span>{{complaint_form_data.address}}</span>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <div>\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">投诉详情:</span>\n          <a-input\n            v-model=\"complaint_form_data.text\"\n            type=\"textarea\"\n            disabled\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n        <div>\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">审核投诉:</span>\n          <a-input\n            v-model=\"complaint_form_data.examineData\"\n            type=\"textarea\"\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n      </a-form-model>\n    </a-modal>\n\n\n    <a-modal\n      v-model=\"complaint_see_modalVisible\"\n      title=\"详细信息\"\n      ok-text=\"确认\"\n      @ok=\"complaint_see_modalVisible = false\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n    >\n        <div style=\"margin-top: 10px;\">\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">报修详情:</span>\n          <a-input\n            v-model=\"complaint_form_data.text\"\n            type=\"textarea\"\n            disabled\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n        <div style=\"margin-top: 10px;\">\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">审核报修:</span>\n          <a-input\n            v-model=\"complaint_form_data.examineData\"\n            type=\"textarea\"\n            disabled\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n      </a-form-model>\n    </a-modal>\n\n    \n  </page-main>\n</template>\n\n<script>\nimport { getComplaint, examineComplaint ,deleteComplaint,getComplaintByUserId} from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\n\nexport default {\n  data () {\n    return {\n      isUser:true,\n      loading: false,\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n      table_selectedRowKeys: [],\n      complaint_query_type: 'title',\n      complaint_query_buttonTitle: '搜索',\n      complaint_query_text: '',\n      complaint_save_title: '投诉申请单审核',\n      complaint_save_modalVisible: false,\n      complaint_see_modalVisible:false,\n      complaint_form_data: {},\n      complaint_data_list: [],\n    }\n  },\n  created () {\n    this.isUser = this.$route.path.indexOf('user') != -1\n    this.Get_complaintDataList()\n  },\n  watch: {\n    complaint_save_modalVisible (val) {\n      if (!val) {\n        this.complaint_form_data = {}\n      }\n    }\n  },\n  methods: {\n    Get_complaintDataList () {\n      let method = getComplaintByUserId()\n      console.log(localStorage.getItem('isAdmin') );\n      if(localStorage.getItem('isAdmin') == 'true'){\n        method = getComplaint()\n      }\n      method.then(res => {\n        this.complaint_query_buttonTitle = '搜索'\n        this.complaint_data_list = res.data\n      })\n    },\n    Query_complaintDataList () {\n      let text = this.complaint_query_text\n      let temp_list = []\n      this.complaint_data_list.forEach(item => {\n        if (item[this.complaint_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.complaint_query_buttonTitle = '返回'\n      this.complaint_data_list = temp_list\n    },\n    Edit_complaintData (form) {\n      this.complaint_form_data = JSON.parse(JSON.stringify(form))\n      this.complaint_save_modalVisible = true\n    },\n    Del_complaintData (id) {\n      deleteComplaint(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_complaintDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_complaintData(this.complaint_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    Save_complaintData () {\n      examineComplaint(this.complaint_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.complaint_save_modalVisible = false\n        this.Get_complaintDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    See_complaintDateModal(form){\n      this.complaint_form_data = JSON.parse(JSON.stringify(form))\n      this.complaint_see_modalVisible = true\n    }\n  },\n}\n</script>\n    \n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n.ant-form-item-children span {\n    margin-left: 10px;\n}\n</style>"]}, "metadata": {}, "sourceType": "module"}