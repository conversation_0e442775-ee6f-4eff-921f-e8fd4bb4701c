version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:5.7
    container_name: estate_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: estate_management
      MYSQL_USER: estate
      MYSQL_PASSWORD: estate123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./em_server/sql:/docker-entrypoint-initdb.d
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci
    networks:
      - estate_network

  # 后端服务
  backend:
    build:
      context: ./em_server
      dockerfile: Dockerfile
    container_name: estate_backend
    restart: always
    ports:
      - "8082:8082"
    environment:
      SPRING_DATASOURCE_URL: ********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: root
    depends_on:
      - mysql
    networks:
      - estate_network

  # 前端服务
  frontend:
    build:
      context: ./em_ui
      dockerfile: Dockerfile
    container_name: estate_frontend
    restart: always
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - estate_network

volumes:
  mysql_data:

networks:
  estate_network:
    driver: bridge
