::-webkit-scrollbar {
    width: 13px;
    height: 13px;
}
::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4);
    background-clip: padding-box;
    border: 3px solid transparent;
    border-radius: 7px;
}
::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
}
::-webkit-scrollbar-track {
    background-color: transparent;
}
::-webkit-scrollbar-track:hover {
    background-color: #f8fafc;
}
html,
body {
    height: 100%;
}
body {
    margin: 0;
    color: #333;
    background-color: $g-app-bg;
    box-sizing: border-box;
    font-family: Lato, PingFang SC, Microsoft YaHei, sans-serif;
    -webkit-tap-highlight-color: transparent;
    &.hidden {
        overflow: hidden;
    }
}
* {
    box-sizing: inherit;
}
// textarea 字体跟随系统
textarea {
    font-family: inherit;
}
