{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = function (ref) {\n  return {\n    methods: {\n      focus: function focus() {\n        this.$refs[ref].focus();\n      }\n    }\n  };\n};\n;", "map": {"version": 3, "names": ["exports", "__esModule", "default", "ref", "methods", "focus", "$refs"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/lib/mixins/focus.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (ref) {\n  return {\n    methods: {\n      focus: function focus() {\n        this.$refs[ref].focus();\n      }\n    }\n  };\n};\n\n;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzBD,OAAO,CAACE,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC/B,OAAO;IACLC,OAAO,EAAE;MACPC,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACtB,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,CAACE,KAAK,CAAC,CAAC;MACzB;IACF;EACF,CAAC;AACH,CAAC;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "script"}