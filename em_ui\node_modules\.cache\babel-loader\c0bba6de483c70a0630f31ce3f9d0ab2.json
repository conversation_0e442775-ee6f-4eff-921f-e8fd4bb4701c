{"ast": null, "code": "function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };", "map": {"version": 3, "names": ["_arrayLikeToArray", "r", "a", "length", "e", "n", "Array", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js"], "sourcesContent": ["function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };"], "mappings": "AAAA,SAASA,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/B,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGD,CAAC,CAACE,MAAM,MAAMD,CAAC,GAAGD,CAAC,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,KAAK,CAACJ,CAAC,CAAC,EAAEE,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACrD,OAAOC,CAAC;AACV;AACA,SAASL,iBAAiB,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}