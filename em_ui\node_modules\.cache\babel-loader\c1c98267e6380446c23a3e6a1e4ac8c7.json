{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nvar render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"el-image\", {\n    style: \"width:\".concat(_vm.realWidth, \";height:\").concat(_vm.realHeight, \";\"),\n    attrs: {\n      src: _vm.src,\n      fit: \"cover\",\n      \"preview-src-list\": [_vm.src]\n    }\n  }, [_c(\"div\", {\n    staticClass: \"image-slot\",\n    attrs: {\n      slot: \"error\"\n    },\n    slot: \"error\"\n  }, [_c(\"png-icon\", {\n    attrs: {\n      name: \"404\",\n      size: \"48\"\n    }\n  })], 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "style", "concat", "realWidth", "realHeight", "attrs", "src", "fit", "staticClass", "slot", "name", "size", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/ImagePreview/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"el-image\",\n    {\n      style: `width:${_vm.realWidth};height:${_vm.realHeight};`,\n      attrs: { src: _vm.src, fit: \"cover\", \"preview-src-list\": [_vm.src] },\n    },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"image-slot\", attrs: { slot: \"error\" }, slot: \"error\" },\n        [_c(\"png-icon\", { attrs: { name: \"404\", size: \"48\" } })],\n        1\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,UAAU,EACV;IACEE,KAAK,WAAAC,MAAA,CAAWJ,GAAG,CAACK,SAAS,cAAAD,MAAA,CAAWJ,GAAG,CAACM,UAAU,MAAG;IACzDC,KAAK,EAAE;MAAEC,GAAG,EAAER,GAAG,CAACQ,GAAG;MAAEC,GAAG,EAAE,OAAO;MAAE,kBAAkB,EAAE,CAACT,GAAG,CAACQ,GAAG;IAAE;EACrE,CAAC,EACD,CACEP,EAAE,CACA,KAAK,EACL;IAAES,WAAW,EAAE,YAAY;IAAEH,KAAK,EAAE;MAAEI,IAAI,EAAE;IAAQ,CAAC;IAAEA,IAAI,EAAE;EAAQ,CAAC,EACtE,CAACV,EAAE,CAAC,UAAU,EAAE;IAAEM,KAAK,EAAE;MAAEK,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK;EAAE,CAAC,CAAC,CAAC,EACxD,CACF,CAAC,CAEL,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBf,MAAM,CAACgB,aAAa,GAAG,IAAI;AAE3B,SAAShB,MAAM,EAAEe,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}