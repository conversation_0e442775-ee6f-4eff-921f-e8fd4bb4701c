{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _objectSpread from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _toConsumableArray from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _interopRequireWildcard from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/interopRequireWildcard.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport store from '@/store/index';\nimport NProgress from 'nprogress';\nimport 'nprogress/nprogress.css'; // progress bar style\n\nVue.use(VueRouter);\nimport Layout from '@/layout';\nimport EmptyLayout from '@/layout/empty';\nvar constantRoutes = [{\n  path: '/login',\n  name: 'login',\n  component: function component() {\n    return Promise.resolve().then(function () {\n      return _interopRequireWildcard(require('@/views/home'));\n    });\n  },\n  meta: {\n    title: '登录'\n  }\n}, {\n  path: '/home',\n  name: 'home',\n  component: function component() {\n    return Promise.resolve().then(function () {\n      return _interopRequireWildcard(require('@/views/home'));\n    });\n  },\n  meta: {\n    title: '首页'\n  }\n}, {\n  path: '/',\n  component: Layout,\n  redirect: '/dashboard',\n  children: [{\n    path: 'dashboard',\n    name: 'dashboard',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/index'));\n      });\n    },\n    meta: {\n      title: store.state.settings.dashboardTitle\n    }\n  },\n  // {\n  //   path: 'personal',\n  //   component: EmptyLayout,\n  //   redirect: '/personal/setting',\n  //   meta: {\n  //     title: '个人中心',\n  //     breadcrumb: false\n  //   },\n  //   children: [\n  //     {\n  //       path: 'setting',\n  //       name: 'personalSetting',\n  //       component: () => import('@/views/personal/setting'),\n  //       meta: {\n  //         title: '个人设置'\n  //       }\n  //     },\n  //     {\n  //       path: 'edit/password',\n  //       name: 'personalEditPassword',\n  //       component: () => import('@/views/personal/edit.password'),\n  //       meta: {\n  //         title: '修改密码'\n  //       }\n  //     }\n  //   ]\n  // },\n  {\n    path: 'reload',\n    name: 'reload',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/reload'));\n      });\n    }\n  }]\n}];\nimport Admin from './modules/admin';\nimport User from './modules/user';\n\n// 当 children 不为空的主导航只有一项时，则隐藏\nvar asyncRoutes = [\n// {\n//   meta: {\n//     title: '默认',\n//     icon: 'sidebar-default'\n//   },\n//   children: [\n//     MultilevelMenuExample,\n//     BreadcrumbExample,\n//     KeepAliveExample,\n//     ComponentBasicExample,\n//     ComponentExample,\n//     PermissionExample,\n//   ]\n// },\n{\n  meta: {\n    title: '管理员后台',\n    auth: ['admin']\n  },\n  children: _toConsumableArray(Admin)\n}, {\n  meta: {\n    title: '用户后台'\n  },\n  children: _toConsumableArray(User)\n}];\nvar lastRoute = [{\n  path: '*',\n  component: function component() {\n    return Promise.resolve().then(function () {\n      return _interopRequireWildcard(require('@/views/404'));\n    });\n  },\n  meta: {\n    title: '404',\n    sidebar: false\n  }\n}];\nvar router = new VueRouter({\n  routes: constantRoutes\n});\n\n// 解决路由在 push/replace 了相同地址报错的问题\nvar originalPush = VueRouter.prototype.push;\nVueRouter.prototype.push = function push(location) {\n  return originalPush.call(this, location).catch(function (err) {\n    return err;\n  });\n};\nvar originalReplace = VueRouter.prototype.replace;\nVueRouter.prototype.replace = function replace(location) {\n  return originalReplace.call(this, location).catch(function (err) {\n    return err;\n  });\n};\nrouter.beforeEach(/*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(to, from, next) {\n    var accessRoutes;\n    return _regeneratorRuntime().wrap(function (_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          store.state.settings.enableProgress && NProgress.start();\n          // 已经登录，但还没根据权限动态挂载路由\n          if (!(store.getters['user/isLogin'] && !store.state.menu.isGenerate)) {\n            _context.next = 2;\n            break;\n          }\n          /**\n           * 重置 matcher\n           * https://blog.csdn.net/baidu_28647571/article/details/101711682\n           */\n          router.matcher = new VueRouter({\n            routes: constantRoutes\n          }).matcher;\n          _context.next = 1;\n          return store.dispatch('menu/generateRoutes', {\n            asyncRoutes: asyncRoutes,\n            currentPath: to.path\n          });\n        case 1:\n          accessRoutes = _context.sent;\n          accessRoutes.push.apply(accessRoutes, lastRoute);\n          accessRoutes.forEach(function (route) {\n            router.addRoute(route);\n          });\n          next(_objectSpread(_objectSpread({}, to), {}, {\n            replace: true\n          }));\n        case 2:\n          if (store.state.menu.isGenerate) {\n            store.commit('menu/setHeaderActived', to.path);\n          }\n          to.meta.title && store.commit('settings/setTitle', to.meta.title);\n          if (store.getters['user/isLogin']) {\n            if (to.name) {\n              if (to.matched.length !== 0) {\n                // 如果已登录状态下，进入登录页会强制跳转到控制台页面\n                if (to.name == 'login') {\n                  next({\n                    name: 'dashboard',\n                    replace: true\n                  });\n                } else if (!store.state.settings.enableDashboard && to.name == 'dashboard') {\n                  // 如果未开启控制台页面，则默认进入侧边栏导航第一个模块\n                  next({\n                    name: store.getters['menu/sidebarRoutes'][0].name,\n                    replace: true\n                  });\n                }\n              } else {\n                // 如果是通过 name 跳转，并且 name 对应的路由没有权限时，需要做这步处理，手动指向到 404 页面\n                next({\n                  path: '/404'\n                });\n              }\n            }\n          } else {\n            if (to.name != 'login') {\n              next({\n                name: 'login',\n                query: {\n                  redirect: to.fullPath\n                }\n              });\n            }\n          }\n          // 百度统计代码\n          if (process.env.VUE_APP_TYPE == 'example') {\n            if (window._hmt) {\n              window._hmt.push(['_trackPageview', location.pathname + '#' + to.fullPath]);\n            }\n          }\n          next();\n        case 3:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return function (_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}());\nrouter.afterEach(function () {\n  store.state.settings.enableProgress && NProgress.done();\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "store", "NProgress", "use", "Layout", "EmptyLayout", "constantRoutes", "path", "name", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "redirect", "children", "state", "settings", "dashboardTitle", "Admin", "User", "asyncRoutes", "auth", "_toConsumableArray", "lastRoute", "sidebar", "router", "routes", "originalPush", "prototype", "push", "location", "call", "catch", "err", "originalReplace", "replace", "beforeEach", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "to", "from", "next", "accessRoutes", "wrap", "_context", "prev", "enableProgress", "start", "getters", "menu", "isGenerate", "matcher", "dispatch", "currentPath", "sent", "apply", "for<PERSON>ach", "route", "addRoute", "_objectSpread", "commit", "matched", "length", "enableDashboard", "query", "fullPath", "process", "env", "VUE_APP_TYPE", "window", "_hmt", "pathname", "stop", "_x", "_x2", "_x3", "arguments", "after<PERSON>ach", "done"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport store from '@/store/index'\nimport NProgress from 'nprogress'\nimport 'nprogress/nprogress.css' // progress bar style\n\nVue.use(VueRouter)\n\nimport Layout from '@/layout'\nimport EmptyLayout from '@/layout/empty'\n\nconst constantRoutes = [\n  {\n    path: '/login',\n    name: 'login',\n    component: () => import('@/views/home'),\n    meta: {\n      title: '登录'\n    }\n  },\n  {\n    path: '/home',\n    name: 'home',\n    component: () => import('@/views/home'),\n    meta: {\n      title: '首页'\n    }\n  },\n  {\n    path: '/',\n    component: Layout,\n    redirect: '/dashboard',\n    children: [\n      {\n        path: 'dashboard',\n        name: 'dashboard',\n        component: () => import('@/views/index'),\n        meta: {\n          title: store.state.settings.dashboardTitle\n        }\n      },\n      // {\n      //   path: 'personal',\n      //   component: EmptyLayout,\n      //   redirect: '/personal/setting',\n      //   meta: {\n      //     title: '个人中心',\n      //     breadcrumb: false\n      //   },\n      //   children: [\n      //     {\n      //       path: 'setting',\n      //       name: 'personalSetting',\n      //       component: () => import('@/views/personal/setting'),\n      //       meta: {\n      //         title: '个人设置'\n      //       }\n      //     },\n      //     {\n      //       path: 'edit/password',\n      //       name: 'personalEditPassword',\n      //       component: () => import('@/views/personal/edit.password'),\n      //       meta: {\n      //         title: '修改密码'\n      //       }\n      //     }\n      //   ]\n      // },\n      {\n        path: 'reload',\n        name: 'reload',\n        component: () => import('@/views/reload')\n      }\n    ]\n  }\n]\n\n\nimport Admin from './modules/admin'\nimport User from './modules/user'\n\n// 当 children 不为空的主导航只有一项时，则隐藏\nlet asyncRoutes = [\n  // {\n  //   meta: {\n  //     title: '默认',\n  //     icon: 'sidebar-default'\n  //   },\n  //   children: [\n  //     MultilevelMenuExample,\n  //     BreadcrumbExample,\n  //     KeepAliveExample,\n  //     ComponentBasicExample,\n  //     ComponentExample,\n  //     PermissionExample,\n  //   ]\n  // },\n  {\n    meta: {\n      title: '管理员后台',\n      auth: ['admin']\n    },\n    children: [\n      ...Admin\n    ]\n  },\n  {\n    meta: {\n      title: '用户后台',\n    },\n    children: [\n      ...User\n    ]\n  },\n]\n\nconst lastRoute = [{\n  path: '*',\n  component: () => import('@/views/404'),\n  meta: {\n    title: '404',\n    sidebar: false\n  }\n}]\n\nconst router = new VueRouter({\n  routes: constantRoutes\n})\n\n// 解决路由在 push/replace 了相同地址报错的问题\nconst originalPush = VueRouter.prototype.push\nVueRouter.prototype.push = function push (location) {\n  return originalPush.call(this, location).catch(err => err)\n}\nconst originalReplace = VueRouter.prototype.replace\nVueRouter.prototype.replace = function replace (location) {\n  return originalReplace.call(this, location).catch(err => err)\n}\n\nrouter.beforeEach(async (to, from, next) => {\n  store.state.settings.enableProgress && NProgress.start()\n  // 已经登录，但还没根据权限动态挂载路由\n  if (store.getters['user/isLogin'] && !store.state.menu.isGenerate) {\n    /**\n     * 重置 matcher\n     * https://blog.csdn.net/baidu_28647571/article/details/101711682\n     */\n    router.matcher = new VueRouter({\n      routes: constantRoutes\n    }).matcher\n    const accessRoutes = await store.dispatch('menu/generateRoutes', {\n      asyncRoutes,\n      currentPath: to.path\n    })\n    accessRoutes.push(...lastRoute)\n    accessRoutes.forEach(route => {\n      router.addRoute(route)\n    })\n    next({ ...to, replace: true })\n  }\n  if (store.state.menu.isGenerate) {\n    store.commit('menu/setHeaderActived', to.path)\n  }\n  to.meta.title && store.commit('settings/setTitle', to.meta.title)\n  if (store.getters['user/isLogin']) {\n    if (to.name) {\n      if (to.matched.length !== 0) {\n        // 如果已登录状态下，进入登录页会强制跳转到控制台页面\n        if (to.name == 'login') {\n          next({\n            name: 'dashboard',\n            replace: true\n          })\n        } else if (!store.state.settings.enableDashboard && to.name == 'dashboard') {\n          // 如果未开启控制台页面，则默认进入侧边栏导航第一个模块\n          next({\n            name: store.getters['menu/sidebarRoutes'][0].name,\n            replace: true\n          })\n        }\n      } else {\n        // 如果是通过 name 跳转，并且 name 对应的路由没有权限时，需要做这步处理，手动指向到 404 页面\n        next({\n          path: '/404'\n        })\n      }\n    }\n  } else {\n    if (to.name != 'login') {\n      next({\n        name: 'login',\n        query: {\n          redirect: to.fullPath\n        }\n      })\n    }\n  }\n  // 百度统计代码\n  if (process.env.VUE_APP_TYPE == 'example') {\n    if (window._hmt) {\n      window._hmt.push(['_trackPageview', location.pathname + '#' + to.fullPath])\n    }\n  }\n  next()\n})\n\nrouter.afterEach(() => {\n  store.state.settings.enableProgress && NProgress.done()\n})\n\nexport default router\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,yBAAyB,EAAC;;AAEjCH,GAAG,CAACI,GAAG,CAACH,SAAS,CAAC;AAElB,OAAOI,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,gBAAgB;AAExC,IAAMC,cAAc,GAAG,CACrB;EACEC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,cAAc;IAAA;EAAA,CAAC;EACvCC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACET,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,cAAc;IAAA;EAAA,CAAC;EACvCC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACET,IAAI,EAAE,GAAG;EACTE,SAAS,EAAEL,MAAM;EACjBa,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,CACR;IACEX,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,eAAe;MAAA;IAAA,CAAC;IACxCC,IAAI,EAAE;MACJC,KAAK,EAAEf,KAAK,CAACkB,KAAK,CAACC,QAAQ,CAACC;IAC9B;EACF,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACEd,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,gBAAgB;MAAA;IAAA;EAC1C,CAAC;AAEL,CAAC,CACF;AAGD,OAAOQ,KAAK,MAAM,iBAAiB;AACnC,OAAOC,IAAI,MAAM,gBAAgB;;AAEjC;AACA,IAAIC,WAAW,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACET,IAAI,EAAE;IACJC,KAAK,EAAE,OAAO;IACdS,IAAI,EAAE,CAAC,OAAO;EAChB,CAAC;EACDP,QAAQ,EAAAQ,kBAAA,CACHJ,KAAK;AAEZ,CAAC,EACD;EACEP,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDE,QAAQ,EAAAQ,kBAAA,CACHH,IAAI;AAEX,CAAC,CACF;AAED,IAAMI,SAAS,GAAG,CAAC;EACjBpB,IAAI,EAAE,GAAG;EACTE,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,aAAa;IAAA;EAAA,CAAC;EACtCC,IAAI,EAAE;IACJC,KAAK,EAAE,KAAK;IACZY,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,IAAMC,MAAM,GAAG,IAAI7B,SAAS,CAAC;EAC3B8B,MAAM,EAAExB;AACV,CAAC,CAAC;;AAEF;AACA,IAAMyB,YAAY,GAAG/B,SAAS,CAACgC,SAAS,CAACC,IAAI;AAC7CjC,SAAS,CAACgC,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAAEC,QAAQ,EAAE;EAClD,OAAOH,YAAY,CAACI,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC5D,CAAC;AACD,IAAMC,eAAe,GAAGtC,SAAS,CAACgC,SAAS,CAACO,OAAO;AACnDvC,SAAS,CAACgC,SAAS,CAACO,OAAO,GAAG,SAASA,OAAOA,CAAEL,QAAQ,EAAE;EACxD,OAAOI,eAAe,CAACH,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC/D,CAAC;AAEDR,MAAM,CAACW,UAAU;EAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAC,SAAAC,QAAOC,EAAE,EAAEC,IAAI,EAAEC,IAAI;IAAA,IAAAC,YAAA;IAAA,OAAAN,mBAAA,GAAAO,IAAA,WAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAH,IAAA;QAAA;UACrC/C,KAAK,CAACkB,KAAK,CAACC,QAAQ,CAACiC,cAAc,IAAInD,SAAS,CAACoD,KAAK,CAAC,CAAC;UACxD;UAAA,MACIrD,KAAK,CAACsD,OAAO,CAAC,cAAc,CAAC,IAAI,CAACtD,KAAK,CAACkB,KAAK,CAACqC,IAAI,CAACC,UAAU;YAAAN,QAAA,CAAAH,IAAA;YAAA;UAAA;UAC/D;AACJ;AACA;AACA;UACInB,MAAM,CAAC6B,OAAO,GAAG,IAAI1D,SAAS,CAAC;YAC7B8B,MAAM,EAAExB;UACV,CAAC,CAAC,CAACoD,OAAO;UAAAP,QAAA,CAAAH,IAAA;UAAA,OACiB/C,KAAK,CAAC0D,QAAQ,CAAC,qBAAqB,EAAE;YAC/DnC,WAAW,EAAXA,WAAW;YACXoC,WAAW,EAAEd,EAAE,CAACvC;UAClB,CAAC,CAAC;QAAA;UAHI0C,YAAY,GAAAE,QAAA,CAAAU,IAAA;UAIlBZ,YAAY,CAAChB,IAAI,CAAA6B,KAAA,CAAjBb,YAAY,EAAStB,SAAS,CAAC;UAC/BsB,YAAY,CAACc,OAAO,CAAC,UAAAC,KAAK,EAAI;YAC5BnC,MAAM,CAACoC,QAAQ,CAACD,KAAK,CAAC;UACxB,CAAC,CAAC;UACFhB,IAAI,CAAAkB,aAAA,CAAAA,aAAA,KAAMpB,EAAE;YAAEP,OAAO,EAAE;UAAI,EAAE,CAAC;QAAA;UAEhC,IAAItC,KAAK,CAACkB,KAAK,CAACqC,IAAI,CAACC,UAAU,EAAE;YAC/BxD,KAAK,CAACkE,MAAM,CAAC,uBAAuB,EAAErB,EAAE,CAACvC,IAAI,CAAC;UAChD;UACAuC,EAAE,CAAC/B,IAAI,CAACC,KAAK,IAAIf,KAAK,CAACkE,MAAM,CAAC,mBAAmB,EAAErB,EAAE,CAAC/B,IAAI,CAACC,KAAK,CAAC;UACjE,IAAIf,KAAK,CAACsD,OAAO,CAAC,cAAc,CAAC,EAAE;YACjC,IAAIT,EAAE,CAACtC,IAAI,EAAE;cACX,IAAIsC,EAAE,CAACsB,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;gBAC3B;gBACA,IAAIvB,EAAE,CAACtC,IAAI,IAAI,OAAO,EAAE;kBACtBwC,IAAI,CAAC;oBACHxC,IAAI,EAAE,WAAW;oBACjB+B,OAAO,EAAE;kBACX,CAAC,CAAC;gBACJ,CAAC,MAAM,IAAI,CAACtC,KAAK,CAACkB,KAAK,CAACC,QAAQ,CAACkD,eAAe,IAAIxB,EAAE,CAACtC,IAAI,IAAI,WAAW,EAAE;kBAC1E;kBACAwC,IAAI,CAAC;oBACHxC,IAAI,EAAEP,KAAK,CAACsD,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC/C,IAAI;oBACjD+B,OAAO,EAAE;kBACX,CAAC,CAAC;gBACJ;cACF,CAAC,MAAM;gBACL;gBACAS,IAAI,CAAC;kBACHzC,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ;YACF;UACF,CAAC,MAAM;YACL,IAAIuC,EAAE,CAACtC,IAAI,IAAI,OAAO,EAAE;cACtBwC,IAAI,CAAC;gBACHxC,IAAI,EAAE,OAAO;gBACb+D,KAAK,EAAE;kBACLtD,QAAQ,EAAE6B,EAAE,CAAC0B;gBACf;cACF,CAAC,CAAC;YACJ;UACF;UACA;UACA,IAAIC,OAAO,CAACC,GAAG,CAACC,YAAY,IAAI,SAAS,EAAE;YACzC,IAAIC,MAAM,CAACC,IAAI,EAAE;cACfD,MAAM,CAACC,IAAI,CAAC5C,IAAI,CAAC,CAAC,gBAAgB,EAAEC,QAAQ,CAAC4C,QAAQ,GAAG,GAAG,GAAGhC,EAAE,CAAC0B,QAAQ,CAAC,CAAC;YAC7E;UACF;UACAxB,IAAI,CAAC,CAAC;QAAA;QAAA;UAAA,OAAAG,QAAA,CAAA4B,IAAA;MAAA;IAAA,GAAAlC,OAAA;EAAA,CACP;EAAA,iBAAAmC,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAzC,IAAA,CAAAqB,KAAA,OAAAqB,SAAA;EAAA;AAAA,IAAC;AAEFtD,MAAM,CAACuD,SAAS,CAAC,YAAM;EACrBnF,KAAK,CAACkB,KAAK,CAACC,QAAQ,CAACiC,cAAc,IAAInD,SAAS,CAACmF,IAAI,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,eAAexD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}