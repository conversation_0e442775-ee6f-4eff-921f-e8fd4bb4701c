{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"router-link\", {\n    class: {\n      title: true,\n      \"is-link\": _vm.$store.state.settings.enableDashboard\n    },\n    attrs: {\n      custom: \"\",\n      to: _vm.to,\n      title: _vm.title,\n      tag: \"div\"\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(_ref) {\n        var navigate = _ref.navigate;\n        return [_c(\"div\", {\n          on: {\n            click: navigate\n          }\n        }, [_vm.showLogo ? _c(\"img\", {\n          staticClass: \"logo\",\n          attrs: {\n            src: _vm.logo\n          }\n        }) : _vm._e(), _vm.showTitle ? _c(\"span\", [_vm._v(_vm._s(_vm.title))]) : _vm._e()])];\n      }\n    }])\n  });\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "class", "title", "$store", "state", "settings", "enableDashboard", "attrs", "custom", "to", "tag", "scopedSlots", "_u", "key", "fn", "_ref", "navigate", "on", "click", "showLogo", "staticClass", "src", "logo", "_e", "showTitle", "_v", "_s", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/layout/components/Logo/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"router-link\", {\n    class: {\n      title: true,\n      \"is-link\": _vm.$store.state.settings.enableDashboard,\n    },\n    attrs: { custom: \"\", to: _vm.to, title: _vm.title, tag: \"div\" },\n    scopedSlots: _vm._u([\n      {\n        key: \"default\",\n        fn: function ({ navigate }) {\n          return [\n            _c(\"div\", { on: { click: navigate } }, [\n              _vm.showLogo\n                ? _c(\"img\", { staticClass: \"logo\", attrs: { src: _vm.logo } })\n                : _vm._e(),\n              _vm.showTitle\n                ? _c(\"span\", [_vm._v(_vm._s(_vm.title))])\n                : _vm._e(),\n            ]),\n          ]\n        },\n      },\n    ]),\n  })\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,aAAa,EAAE;IACvBE,KAAK,EAAE;MACLC,KAAK,EAAE,IAAI;MACX,SAAS,EAAEJ,GAAG,CAACK,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACC;IACvC,CAAC;IACDC,KAAK,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,EAAE,EAAEX,GAAG,CAACW,EAAE;MAAEP,KAAK,EAAEJ,GAAG,CAACI,KAAK;MAAEQ,GAAG,EAAE;IAAM,CAAC;IAC/DC,WAAW,EAAEb,GAAG,CAACc,EAAE,CAAC,CAClB;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAAC,IAAA,EAA0B;QAAA,IAAZC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;QACtB,OAAO,CACLjB,EAAE,CAAC,KAAK,EAAE;UAAEkB,EAAE,EAAE;YAAEC,KAAK,EAAEF;UAAS;QAAE,CAAC,EAAE,CACrClB,GAAG,CAACqB,QAAQ,GACRpB,EAAE,CAAC,KAAK,EAAE;UAAEqB,WAAW,EAAE,MAAM;UAAEb,KAAK,EAAE;YAAEc,GAAG,EAAEvB,GAAG,CAACwB;UAAK;QAAE,CAAC,CAAC,GAC5DxB,GAAG,CAACyB,EAAE,CAAC,CAAC,EACZzB,GAAG,CAAC0B,SAAS,GACTzB,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAAC2B,EAAE,CAAC3B,GAAG,CAAC4B,EAAE,CAAC5B,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,GACvCJ,GAAG,CAACyB,EAAE,CAAC,CAAC,CACb,CAAC,CACH;MACH;IACF,CAAC,CACF;EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AACxB9B,MAAM,CAAC+B,aAAa,GAAG,IAAI;AAE3B,SAAS/B,MAAM,EAAE8B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}