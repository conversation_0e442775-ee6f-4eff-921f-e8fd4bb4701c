# 数据库安装配置指南

## 环境要求
- MySQL 5.7 或更高版本
- 数据库用户需要有创建数据库和表的权限

## 安装步骤

### 1. 创建数据库
```sql
CREATE DATABASE IF NOT EXISTS estate_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_general_ci;
```

### 2. 导入数据库结构和数据
按以下顺序执行SQL文件：

#### 方法一：使用命令行（推荐）
```bash
# 1. 首先导入主数据库结构
mysql -u root -p estate_management < em_server/sql/estate_management.sql

# 2. 然后导入初始化数据
mysql -u root -p estate_management < em_server/sql/init_database.sql
```

#### 方法二：使用数据库管理工具
1. 连接到MySQL数据库
2. 选择 `estate_management` 数据库
3. 先执行 `em_server/sql/estate_management.sql` 文件
4. 再执行 `em_server/sql/init_database.sql` 文件

### 3. 验证安装
执行以下SQL验证数据库是否正确安装：

```sql
-- 检查表是否创建成功
SHOW TABLES;

-- 检查默认用户是否创建
SELECT * FROM sys_user WHERE user_name IN ('admin', 'test');

-- 检查系统配置是否创建
SELECT * FROM sys_options;
```

## 默认账号信息

### 管理员账号
- 用户名：admin
- 密码：123456
- 角色：管理员

### 测试账号
- 用户名：test  
- 密码：123456
- 角色：普通用户

## 数据库配置

### 应用配置文件
确保 `em_server/src/main/resources/application.yml` 中的数据库配置正确：

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: root
    password: root  # 修改为你的数据库密码
```

## 常见问题

### 1. 表不存在错误
如果遇到 "Table doesn't exist" 错误，请确保：
- 先执行 `estate_management.sql` 创建表结构
- 再执行 `init_database.sql` 插入初始数据

### 2. 字符编码问题
确保数据库和表都使用 `utf8mb4` 字符集：
```sql
ALTER DATABASE estate_management CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

### 3. 连接问题
如果应用无法连接数据库，检查：
- MySQL服务是否启动
- 用户名密码是否正确
- 防火墙设置
- MySQL用户权限

### 4. 时区问题
如果遇到时区相关错误，在MySQL配置文件中添加：
```ini
[mysqld]
default-time-zone='+08:00'
```

## 数据库维护

### 备份数据库
```bash
mysqldump -u root -p estate_management > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 恢复数据库
```bash
mysql -u root -p estate_management < backup_file.sql
```

### 重置数据库
如果需要重新安装：
```sql
DROP DATABASE IF EXISTS estate_management;
CREATE DATABASE estate_management CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```
然后重新执行安装步骤。
