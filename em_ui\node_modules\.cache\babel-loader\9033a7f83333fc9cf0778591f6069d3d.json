{"ast": null, "code": "import _objectSpread from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\n/**\n * 存放全局公用状态\n */\nimport setting from '@/setting';\nvar state = _objectSpread(_objectSpread({}, setting), {}, {\n  // 侧边栏是否收起（用于记录 pc 模式下最后的状态）\n  sidebarCollapseLastStatus: setting.sidebarCollapse,\n  // 显示模式，支持：mobile、pc\n  mode: 'pc',\n  // 页面标题\n  title: ''\n});\nvar getters = {};\nvar actions = {};\nvar mutations = {\n  // 设置访问模式，页面宽度小于 992px 时切换为移动端展示\n  setMode: function setMode(state, width) {\n    if (width < 992) {\n      state.mode = 'mobile';\n    } else {\n      state.mode = 'pc';\n    }\n  },\n  // 设置网页标题\n  setTitle: function setTitle(state, title) {\n    state.title = title;\n  },\n  // 切换侧边栏导航展开/收起\n  toggleSidebarCollapse: function toggleSidebarCollapse(state) {\n    state.sidebarCollapse = !state.sidebarCollapse;\n    if (state.mode == 'pc') {\n      state.sidebarCollapseLastStatus = !state.sidebarCollapseLastStatus;\n    }\n  },\n  // 更新主题配置\n  updateThemeSetting: function updateThemeSetting(state, data) {\n    Object.assign(state, data);\n  }\n};\nexport default {\n  namespaced: true,\n  state: state,\n  actions: actions,\n  getters: getters,\n  mutations: mutations\n};", "map": {"version": 3, "names": ["setting", "state", "_objectSpread", "sidebarCollapseLastStatus", "sidebarCollapse", "mode", "title", "getters", "actions", "mutations", "setMode", "width", "setTitle", "toggleSidebarCollapse", "updateThemeSetting", "data", "Object", "assign", "namespaced"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/store/modules/settings.js"], "sourcesContent": ["/**\n * 存放全局公用状态\n */\nimport setting from '@/setting'\n\nconst state = {\n    ...setting,\n    // 侧边栏是否收起（用于记录 pc 模式下最后的状态）\n    sidebarCollapseLastStatus: setting.sidebarCollapse,\n    // 显示模式，支持：mobile、pc\n    mode: 'pc',\n    // 页面标题\n    title: ''\n}\n\nconst getters = {}\n\nconst actions = {}\n\nconst mutations = {\n    // 设置访问模式，页面宽度小于 992px 时切换为移动端展示\n    setMode(state, width) {\n        if (width < 992) {\n            state.mode = 'mobile'\n        } else {\n            state.mode = 'pc'\n        }\n    },\n    // 设置网页标题\n    setTitle(state, title) {\n        state.title = title\n    },\n    // 切换侧边栏导航展开/收起\n    toggleSidebarCollapse(state) {\n        state.sidebarCollapse = !state.sidebarCollapse\n        if (state.mode == 'pc') {\n            state.sidebarCollapseLastStatus = !state.sidebarCollapseLastStatus\n        }\n    },\n    // 更新主题配置\n    updateThemeSetting(state, data) {\n        Object.assign(state, data)\n    }\n}\n\nexport default {\n    namespaced: true,\n    state,\n    actions,\n    getters,\n    mutations\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,KAAK,GAAAC,aAAA,CAAAA,aAAA,KACJF,OAAO;EACV;EACAG,yBAAyB,EAAEH,OAAO,CAACI,eAAe;EAClD;EACAC,IAAI,EAAE,IAAI;EACV;EACAC,KAAK,EAAE;AAAE,EACZ;AAED,IAAMC,OAAO,GAAG,CAAC,CAAC;AAElB,IAAMC,OAAO,GAAG,CAAC,CAAC;AAElB,IAAMC,SAAS,GAAG;EACd;EACAC,OAAO,WAAPA,OAAOA,CAACT,KAAK,EAAEU,KAAK,EAAE;IAClB,IAAIA,KAAK,GAAG,GAAG,EAAE;MACbV,KAAK,CAACI,IAAI,GAAG,QAAQ;IACzB,CAAC,MAAM;MACHJ,KAAK,CAACI,IAAI,GAAG,IAAI;IACrB;EACJ,CAAC;EACD;EACAO,QAAQ,WAARA,QAAQA,CAACX,KAAK,EAAEK,KAAK,EAAE;IACnBL,KAAK,CAACK,KAAK,GAAGA,KAAK;EACvB,CAAC;EACD;EACAO,qBAAqB,WAArBA,qBAAqBA,CAACZ,KAAK,EAAE;IACzBA,KAAK,CAACG,eAAe,GAAG,CAACH,KAAK,CAACG,eAAe;IAC9C,IAAIH,KAAK,CAACI,IAAI,IAAI,IAAI,EAAE;MACpBJ,KAAK,CAACE,yBAAyB,GAAG,CAACF,KAAK,CAACE,yBAAyB;IACtE;EACJ,CAAC;EACD;EACAW,kBAAkB,WAAlBA,kBAAkBA,CAACb,KAAK,EAAEc,IAAI,EAAE;IAC5BC,MAAM,CAACC,MAAM,CAAChB,KAAK,EAAEc,IAAI,CAAC;EAC9B;AACJ,CAAC;AAED,eAAe;EACXG,UAAU,EAAE,IAAI;EAChBjB,KAAK,EAALA,KAAK;EACLO,OAAO,EAAPA,OAAO;EACPD,OAAO,EAAPA,OAAO;EACPE,SAAS,EAATA;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}