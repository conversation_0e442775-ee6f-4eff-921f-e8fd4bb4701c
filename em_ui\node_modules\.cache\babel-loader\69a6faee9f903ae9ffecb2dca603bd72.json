{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nvar render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"svg\", _vm._g({\n    staticClass: \"svg-icon\",\n    attrs: {\n      \"aria-hidden\": \"true\"\n    }\n  }, _vm.$listeners), [_c(\"use\", {\n    attrs: {\n      \"xlink:href\": \"#icon-\".concat(_vm.name)\n    }\n  })]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_g", "staticClass", "attrs", "$listeners", "concat", "name", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/SvgIcon/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"svg\",\n    _vm._g(\n      { staticClass: \"svg-icon\", attrs: { \"aria-hidden\": \"true\" } },\n      _vm.$listeners\n    ),\n    [_c(\"use\", { attrs: { \"xlink:href\": `#icon-${_vm.name}` } })]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACLD,GAAG,CAACG,EAAE,CACJ;IAAEC,WAAW,EAAE,UAAU;IAAEC,KAAK,EAAE;MAAE,aAAa,EAAE;IAAO;EAAE,CAAC,EAC7DL,GAAG,CAACM,UACN,CAAC,EACD,CAACL,EAAE,CAAC,KAAK,EAAE;IAAEI,KAAK,EAAE;MAAE,YAAY,WAAAE,MAAA,CAAWP,GAAG,CAACQ,IAAI;IAAG;EAAE,CAAC,CAAC,CAC9D,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBV,MAAM,CAACW,aAAa,GAAG,IAAI;AAE3B,SAASX,MAAM,EAAEU,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}