{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nvar render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"img\", _vm._g({\n    class: [\"png-icon\", _vm.customClass],\n    style: _vm.iconStyle,\n    attrs: {\n      src: _vm.iconSrc,\n      alt: _vm.name\n    }\n  }, _vm.$listeners));\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_g", "class", "customClass", "style", "iconStyle", "attrs", "src", "iconSrc", "alt", "name", "$listeners", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/PngIcon/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"img\",\n    _vm._g(\n      {\n        class: [\"png-icon\", _vm.customClass],\n        style: _vm.iconStyle,\n        attrs: { src: _vm.iconSrc, alt: _vm.name },\n      },\n      _vm.$listeners\n    )\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACLD,GAAG,CAACG,EAAE,CACJ;IACEC,KAAK,EAAE,CAAC,UAAU,EAAEJ,GAAG,CAACK,WAAW,CAAC;IACpCC,KAAK,EAAEN,GAAG,CAACO,SAAS;IACpBC,KAAK,EAAE;MAAEC,GAAG,EAAET,GAAG,CAACU,OAAO;MAAEC,GAAG,EAAEX,GAAG,CAACY;IAAK;EAC3C,CAAC,EACDZ,GAAG,CAACa,UACN,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBf,MAAM,CAACgB,aAAa,GAAG,IAAI;AAE3B,SAAShB,MAAM,EAAEe,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}