{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport axios from 'axios';\n// import Qs from 'qs'\nimport router from '@/router/index';\nimport store from '@/store/index';\nimport { Message } from 'element-ui';\nvar toLogin = function toLogin() {\n  console.log(1);\n  router.push({\n    path: '/home',\n    query: {\n      redirect: router.currentRoute.fullPath\n    }\n  });\n};\nvar api = axios.create({\n  baseURL: process.env.VUE_APP_API_ROOT,\n  timeout: 10000,\n  responseType: 'json',\n  withCredentials: true\n});\napi.interceptors.request.use(function (request) {\n  return request;\n});\napi.interceptors.response.use(function (response) {\n  var data = response.data;\n  if (data.code == 403) {\n    localStorage.removeItem('isLogin');\n    toLogin();\n  }\n  return Promise.resolve(data);\n}, function (error) {\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "router", "store", "Message", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "push", "path", "query", "redirect", "currentRoute", "fullPath", "api", "create", "baseURL", "process", "env", "VUE_APP_API_ROOT", "timeout", "responseType", "withCredentials", "interceptors", "request", "use", "response", "data", "code", "localStorage", "removeItem", "Promise", "resolve", "error", "reject"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/api/index.js"], "sourcesContent": ["import axios from 'axios'\n// import Qs from 'qs'\nimport router from '@/router/index'\nimport store from '@/store/index'\nimport { Message } from 'element-ui'\n\nconst toLogin = () => {\n  console.log(1);\n  router.push({\n    path: '/home',\n    query: {\n      redirect: router.currentRoute.fullPath\n    }\n  })\n}\n\nconst api = axios.create({\n  baseURL: process.env.VUE_APP_API_ROOT,\n  timeout: 10000,\n  responseType: 'json',\n  withCredentials: true\n})\n\napi.interceptors.request.use(\n  request => {\n\n    return request\n  }\n)\n\napi.interceptors.response.use(\n  response => {\n    const data = response.data\n    if (data.code == 403) {\n      localStorage.removeItem('isLogin')\n      toLogin()\n    }\n\n    return Promise.resolve(data)\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\nexport default api\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,OAAO,QAAQ,YAAY;AAEpC,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;EACpBC,OAAO,CAACC,GAAG,CAAC,CAAC,CAAC;EACdL,MAAM,CAACM,IAAI,CAAC;IACVC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;MACLC,QAAQ,EAAET,MAAM,CAACU,YAAY,CAACC;IAChC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,IAAMC,GAAG,GAAGb,KAAK,CAACc,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrCC,OAAO,EAAE,KAAK;EACdC,YAAY,EAAE,MAAM;EACpBC,eAAe,EAAE;AACnB,CAAC,CAAC;AAEFR,GAAG,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1B,UAAAD,OAAO,EAAI;EAET,OAAOA,OAAO;AAChB,CACF,CAAC;AAEDV,GAAG,CAACS,YAAY,CAACG,QAAQ,CAACD,GAAG,CAC3B,UAAAC,QAAQ,EAAI;EACV,IAAMC,IAAI,GAAGD,QAAQ,CAACC,IAAI;EAC1B,IAAIA,IAAI,CAACC,IAAI,IAAI,GAAG,EAAE;IACpBC,YAAY,CAACC,UAAU,CAAC,SAAS,CAAC;IAClCzB,OAAO,CAAC,CAAC;EACX;EAEA,OAAO0B,OAAO,CAACC,OAAO,CAACL,IAAI,CAAC;AAC9B,CAAC,EACD,UAAAM,KAAK,EAAI;EACP,OAAOF,OAAO,CAACG,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAenB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}