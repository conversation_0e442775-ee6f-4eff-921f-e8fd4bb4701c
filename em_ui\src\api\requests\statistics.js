import api from '../index'

/**
 * 获取系统统计概览数据
 */
export const getOverview = () => {
  return api.get('/system/statistics/overview')
}

/**
 * 获取最新报修记录
 */
export const getRecentRepairs = () => {
  return api.get('/system/statistics/recent/repairs')
}

/**
 * 获取最新投诉记录
 */
export const getRecentComplaints = () => {
  return api.get('/system/statistics/recent/complaints')
}

/**
 * 获取月度统计数据
 */
export const getMonthlyStats = () => {
  return api.get('/system/statistics/monthly')
}

/**
 * 获取楼宇列表
 */
export const getBuildingList = () => {
  return api.get('/system/building/list')
}

/**
 * 获取房间列表
 */
export const getRoomList = () => {
  return api.get('/system/room/list')
}

/**
 * 获取用户列表
 */
export const getUserList = () => {
  return api.get('/system/user/list')
}

/**
 * 获取报修列表
 */
export const getRepairList = () => {
  return api.get('/system/repair/list')
}

/**
 * 获取投诉列表
 */
export const getComplaintList = () => {
  return api.get('/system/complaint/list')
}
