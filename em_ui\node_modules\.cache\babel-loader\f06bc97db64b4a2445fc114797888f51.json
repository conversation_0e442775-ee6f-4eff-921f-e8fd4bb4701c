{"ast": null, "code": "export default {\n  name: 'user_reset_pwd',\n  data: function data() {\n    return {\n      current_form: {}\n    };\n  }\n};", "map": {"version": 3, "mappings": "AA4BA;EACAA;EACAC;IACA;MACAC;IACA;EACA;AAEA", "names": ["name", "data", "current_form"], "sourceRoot": "src/views/admin/user", "sources": ["user_reset_pwd.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card title=\"密码修改\">\n      <a-form-model\n        ref=\"current_form\"\n        :rules=\"rules\"\n        :model=\"current_form\"\n        style=\"width: 80vh;\"\n        :label-col=\"labelCol\"\n        :wrapper-col=\"wrapperCol\"\n      >\n        <a-form-model-item label=\"现行密码\" prop=\"password\">\n          <a-input-password v-model=\"current_form.email\" placeholder=\"您的现行密码\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"新的密码\" prop=\"newPassword\">\n          <a-input-password v-model=\"current_form.workUnit\" placeholder=\"新的密码\" />\n        </a-form-model-item>\n\n        <a-form-model-item style=\"margin-left: 33.3%;\">\n          <a-button type=\"primary\" @click=\"save_userInfo\">保存</a-button>\n          <a-button style=\"margin-left: 10px;\">取消</a-button>\n        </a-form-model-item>\n      </a-form-model>\n    </a-card>\n  </page-main>\n</template>\n\n<script>\nexport default {\n  name: 'user_reset_pwd',\n  data () {\n    return {\n      current_form: {}\n    }\n  },\n\n}\n</script>\n"]}, "metadata": {}, "sourceType": "module"}