{"ast": null, "code": "var globalSettings = {\n  /**\n   * 是否开启权限功能，权限功能提供以下鉴权支持：\n   * 1、路由鉴权\n   * 2、鉴权组件：<Auth></Auth>、<AuthAll></AuthAll>\n   * 3、鉴权指令：v-auth、v-auth-all\n   * 4、鉴权函数：this.$auth()、this.$authAll()\n   */\n  openPermission: false,\n  // 是否显示头部\n  showHeader: true,\n  // 是否开启侧边栏展开收起按钮\n  enableSidebarCollapse: false,\n  // 侧边栏是否收起\n  sidebarCollapse: false,\n  // 是否显示底部版权信息，同时在路由 meta 对象里可以单独设置某个路由是否显示底部版权信息\n  showCopyright: true,\n  // 版权信息配置，格式为：Copyright © [dates] <company>\n  copyrightDates: '2021',\n  copyrightCompany: '社区管理系统',\n  copyrightWebsite: 'https://hooray.gitee.io/fantastic-admin',\n  // 是否开启导航搜索\n  enableNavSearch: true,\n  // 是否开启全屏\n  enableFullscreen: false,\n  // 是否开启页面刷新\n  enablePageReload: false,\n  // 是否开启载入进度条\n  enableProgress: true,\n  // 是否开启动态标题\n  enableDynamicTitle: false,\n  // 是否开启控制台\n  enableDashboard: true,\n  // 是否开启扁平化路由，开启后三级以及三级以上的嵌套路由均为被处理成二级，但侧边栏导航的层级效果不变\n  enableFlatRoutes: false,\n  // 控制台名称\n  dashboardTitle: '控制台',\n  // 是否开启主题配置（建议在生产环境关闭）\n  enableThemeSetting: false\n};\n\n// 演示&开发环境开启全部功能（这部分代码可删除，仅方便作者打包演示环境）\nif (process.env.VUE_APP_TYPE == 'example' || process.env.NODE_ENV == 'development') {\n  globalSettings.openPermission = true;\n  globalSettings.enableSidebarCollapse = true;\n  globalSettings.enableFullscreen = true;\n  globalSettings.enablePageReload = true;\n  globalSettings.enableDynamicTitle = true;\n}\nexport default globalSettings;", "map": {"version": 3, "names": ["globalSettings", "openPermission", "showHeader", "enableSidebarCollapse", "sidebarCollapse", "show<PERSON>opyright", "copyrightDates", "copyrightCompany", "copyrightWebsite", "enableNavSearch", "enableFullscreen", "enablePageReload", "enableProgress", "enableDynamicTitle", "enableDashboard", "enableFlatRoutes", "dashboardTitle", "enableThemeSetting", "process", "env", "VUE_APP_TYPE", "NODE_ENV"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/setting.js"], "sourcesContent": ["let globalSettings = {\n  /**\n   * 是否开启权限功能，权限功能提供以下鉴权支持：\n   * 1、路由鉴权\n   * 2、鉴权组件：<Auth></Auth>、<AuthAll></AuthAll>\n   * 3、鉴权指令：v-auth、v-auth-all\n   * 4、鉴权函数：this.$auth()、this.$authAll()\n   */\n  openPermission: false,\n  // 是否显示头部\n  showHeader: true,\n  // 是否开启侧边栏展开收起按钮\n  enableSidebarCollapse: false,\n  // 侧边栏是否收起\n  sidebarCollapse: false,\n  // 是否显示底部版权信息，同时在路由 meta 对象里可以单独设置某个路由是否显示底部版权信息\n  showCopyright: true,\n  // 版权信息配置，格式为：Copyright © [dates] <company>\n  copyrightDates: '2021',\n  copyrightCompany: '社区管理系统',\n  copyrightWebsite: 'https://hooray.gitee.io/fantastic-admin',\n  // 是否开启导航搜索\n  enableNavSearch: true,\n  // 是否开启全屏\n  enableFullscreen: false,\n  // 是否开启页面刷新\n  enablePageReload: false,\n  // 是否开启载入进度条\n  enableProgress: true,\n  // 是否开启动态标题\n  enableDynamicTitle: false,\n  // 是否开启控制台\n  enableDashboard: true,\n  // 是否开启扁平化路由，开启后三级以及三级以上的嵌套路由均为被处理成二级，但侧边栏导航的层级效果不变\n  enableFlatRoutes: false,\n  // 控制台名称\n  dashboardTitle: '控制台',\n  // 是否开启主题配置（建议在生产环境关闭）\n  enableThemeSetting: false\n}\n\n// 演示&开发环境开启全部功能（这部分代码可删除，仅方便作者打包演示环境）\nif (process.env.VUE_APP_TYPE == 'example' || process.env.NODE_ENV == 'development') {\n  globalSettings.openPermission = true\n  globalSettings.enableSidebarCollapse = true\n  globalSettings.enableFullscreen = true\n  globalSettings.enablePageReload = true\n  globalSettings.enableDynamicTitle = true\n}\n\nexport default globalSettings\n"], "mappings": "AAAA,IAAIA,cAAc,GAAG;EACnB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,cAAc,EAAE,KAAK;EACrB;EACAC,UAAU,EAAE,IAAI;EAChB;EACAC,qBAAqB,EAAE,KAAK;EAC5B;EACAC,eAAe,EAAE,KAAK;EACtB;EACAC,aAAa,EAAE,IAAI;EACnB;EACAC,cAAc,EAAE,MAAM;EACtBC,gBAAgB,EAAE,QAAQ;EAC1BC,gBAAgB,EAAE,yCAAyC;EAC3D;EACAC,eAAe,EAAE,IAAI;EACrB;EACAC,gBAAgB,EAAE,KAAK;EACvB;EACAC,gBAAgB,EAAE,KAAK;EACvB;EACAC,cAAc,EAAE,IAAI;EACpB;EACAC,kBAAkB,EAAE,KAAK;EACzB;EACAC,eAAe,EAAE,IAAI;EACrB;EACAC,gBAAgB,EAAE,KAAK;EACvB;EACAC,cAAc,EAAE,KAAK;EACrB;EACAC,kBAAkB,EAAE;AACtB,CAAC;;AAED;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,YAAY,IAAI,SAAS,IAAIF,OAAO,CAACC,GAAG,CAACE,QAAQ,IAAI,aAAa,EAAE;EAClFrB,cAAc,CAACC,cAAc,GAAG,IAAI;EACpCD,cAAc,CAACG,qBAAqB,GAAG,IAAI;EAC3CH,cAAc,CAACU,gBAAgB,GAAG,IAAI;EACtCV,cAAc,CAACW,gBAAgB,GAAG,IAAI;EACtCX,cAAc,CAACa,kBAAkB,GAAG,IAAI;AAC1C;AAEA,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}