{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport regenerator from \"./regenerator.js\";\nimport regeneratorAsyncIterator from \"./regeneratorAsyncIterator.js\";\nfunction _regeneratorAsyncGen(r, e, t, o, n) {\n  return new regeneratorAsyncIterator(regenerator().w(r, e, t, o), n || Promise);\n}\nexport { _regeneratorAsyncGen as default };", "map": {"version": 3, "names": ["regenerator", "regeneratorAsyncIterator", "_regeneratorAsyncGen", "r", "e", "t", "o", "n", "w", "Promise", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/@babel/runtime/helpers/esm/regeneratorAsyncGen.js"], "sourcesContent": ["import regenerator from \"./regenerator.js\";\nimport regeneratorAsyncIterator from \"./regeneratorAsyncIterator.js\";\nfunction _regeneratorAsyncGen(r, e, t, o, n) {\n  return new regeneratorAsyncIterator(regenerator().w(r, e, t, o), n || Promise);\n}\nexport { _regeneratorAsyncGen as default };"], "mappings": ";AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,wBAAwB,MAAM,+BAA+B;AACpE,SAASC,oBAAoBA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3C,OAAO,IAAIN,wBAAwB,CAACD,WAAW,CAAC,CAAC,CAACQ,CAAC,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAEC,CAAC,IAAIE,OAAO,CAAC;AAChF;AACA,SAASP,oBAAoB,IAAIQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}