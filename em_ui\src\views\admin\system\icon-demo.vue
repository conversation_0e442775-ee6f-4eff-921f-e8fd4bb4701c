<template>
  <div class="icon-demo">
    <page-header title="PNG图标示例">
      <template #content>
        <div>展示项目中可用的PNG图标及其使用方法</div>
      </template>
    </page-header>
    
    <page-main>
      <el-card>
        <div slot="header">
          <span>可用图标</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="6" v-for="icon in availableIcons" :key="icon">
            <div class="icon-item">
              <div class="icon-display">
                <png-icon :name="icon" size="32" />
              </div>
              <div class="icon-name">{{ icon }}</div>
              <div class="icon-usage">
                <el-input 
                  :value="`<png-icon name='${icon}' size='24' />`"
                  readonly
                  size="mini"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <el-card style="margin-top: 20px;">
        <div slot="header">
          <span>使用示例</span>
        </div>
        
        <div class="usage-examples">
          <h4>基本用法</h4>
          <div class="example">
            <div class="demo">
              <png-icon name="home" size="24" />
              <png-icon name="building" size="32" />
              <png-icon name="repair" size="40" />
            </div>
            <div class="code">
              <pre><code>&lt;png-icon name="home" size="24" /&gt;
&lt;png-icon name="building" size="32" /&gt;
&lt;png-icon name="repair" size="40" /&gt;</code></pre>
            </div>
          </div>
          
          <h4>自定义样式</h4>
          <div class="example">
            <div class="demo">
              <png-icon name="notice" size="24" custom-class="custom-icon" />
              <png-icon name="payment" size="24" custom-class="colored-icon" />
            </div>
            <div class="code">
              <pre><code>&lt;png-icon name="notice" size="24" custom-class="custom-icon" /&gt;
&lt;png-icon name="payment" size="24" custom-class="colored-icon" /&gt;</code></pre>
            </div>
          </div>
          
          <h4>在卡片中使用</h4>
          <div class="example">
            <div class="demo">
              <div class="stat-card">
                <div class="stat-icon">
                  <png-icon name="dashboard" size="24" />
                </div>
                <div class="stat-info">
                  <div class="stat-number">123</div>
                  <div class="stat-label">数据统计</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </page-main>
  </div>
</template>

<script>
import { getAllIcons } from '@/utils/iconMap'

export default {
  name: 'IconDemo',
  data() {
    return {
      availableIcons: getAllIcons()
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-demo {
  .icon-item {
    text-align: center;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    
    .icon-display {
      margin-bottom: 10px;
    }
    
    .icon-name {
      font-weight: bold;
      margin-bottom: 10px;
      color: #303133;
    }
    
    .icon-usage {
      .el-input {
        font-family: 'Courier New', monospace;
      }
    }
  }
  
  .usage-examples {
    h4 {
      color: #303133;
      margin: 20px 0 10px 0;
    }
    
    .example {
      margin-bottom: 30px;
      
      .demo {
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 15px;
      }
      
      .code {
        background: #f5f7fa;
        padding: 15px;
        border-radius: 8px;
        
        pre {
          margin: 0;
          font-family: 'Courier New', monospace;
          font-size: 14px;
          color: #606266;
        }
      }
    }
    
    .stat-card {
      display: flex;
      align-items: center;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      color: white;
      width: 200px;
      
      .stat-icon {
        margin-right: 15px;
        
        .png-icon {
          filter: brightness(0) invert(1);
        }
      }
      
      .stat-info {
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 14px;
          opacity: 0.9;
          margin-top: 5px;
        }
      }
    }
  }
}

// 自定义图标样式示例
::v-deep .custom-icon {
  border: 2px solid #409eff;
  border-radius: 50%;
  padding: 5px;
}

::v-deep .colored-icon {
  filter: hue-rotate(120deg) saturate(2);
}
</style>
