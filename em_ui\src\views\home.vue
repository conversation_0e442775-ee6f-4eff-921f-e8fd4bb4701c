<template>
  <div class="login-container">
    <div class="login-background">
      <div class="login-overlay"></div>
    </div>

    <div class="login-content">
      <div class="login-card">
        <div class="login-header">
          <div class="logo-section">
            <div class="logo-icon">
              <png-icon name="building" size="40" />
            </div>
            <h1 class="system-title">小区物业管理系统</h1>
            <p class="system-subtitle">Property Management System</p>
          </div>
        </div>

        <div class="login-form">
          <el-form :model="login_form_data" :rules="loginRules" ref="loginForm">
            <el-form-item prop="userName">
              <el-input
                v-model="login_form_data.userName"
                placeholder="请输入用户名"
                prefix-icon="el-icon-user"
                size="large"
                class="login-input">
              </el-input>
            </el-form-item>

            <el-form-item prop="passWord">
              <el-input
                v-model="login_form_data.passWord"
                type="password"
                placeholder="请输入密码"
                prefix-icon="el-icon-lock"
                size="large"
                class="login-input"
                show-password>
              </el-input>
            </el-form-item>

            <el-form-item prop="code">
              <el-row :gutter="10">
                <el-col :span="14">
                  <el-input
                    v-model="login_form_data.code"
                    placeholder="请输入验证码"
                    prefix-icon="el-icon-key"
                    size="large"
                    class="login-input">
                  </el-input>
                </el-col>
                <el-col :span="10">
                  <img
                    @click="refreshCaptcha"
                    :src="login_img_src"
                    class="captcha-img"
                    title="点击刷新验证码"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                class="login-button"
                :loading="loginLoading"
                @click="home_login()">
                {{ loginLoading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-tips">
            <p>默认账号密码：</p>
            <p>管理员：admin / 123456</p>
            <p>普通用户：test / 123456</p>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import { login, isAdmin } from '@/api/requests/rq-manage.js'

export default {
  name: 'home',
  data () {
    return {
      login_img_src: 'http://localhost:8082/login/code',
      login_form_data: {
        userName: '',
        passWord: '',
        code: ''
      },
      loginLoading: false,
      loginRules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        passWord: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    if (localStorage.getItem("isLogin")) {

      this.$store.dispatch('user/login', this.form).then(res => {
        this.$router.push('/')
      })

    }
  },
  methods: {
    home_login () {
      login(this.login_form_data).then(res => {
        if (res.code == 200) {
          this.$success({
            title: '登录成功',
            content: (
              <div>
                <p>接下来即将跳转到后台界面...</p>
              </div>
            ),
          });
          localStorage.setItem("username", res.data.userName)
          this.$store.dispatch('user/login', this.form).then(res => {
              setTimeout(() => {
            window.location.href = '/'
          }, 1000)

          })
        } else {
          this.$error({
            title: '登录失败',
            content: res.msg,
          });
        }
      })
    },
    refreshCaptcha() {
      this.login_img_src = 'http://localhost:8082/login/code?time=' + new Date().getTime()
    }
  },
}



</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
}

.login-content {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-section {
  .logo-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      font-size: 40px;
      color: white;
    }

    .png-icon {
      filter: brightness(0) invert(1);
    }
  }

  .system-title {
    font-size: 28px;
    font-weight: bold;
    color: #2c3e50;
    margin: 0 0 10px 0;
  }

  .system-subtitle {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
  }
}

.login-form {
  .login-input {
    margin-bottom: 20px;

    ::v-deep .el-input__inner {
      height: 50px;
      border-radius: 10px;
      border: 2px solid #e9ecef;
      font-size: 16px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    ::v-deep .el-input__prefix {
      left: 15px;
      color: #667eea;
    }
  }

  .captcha-img {
    height: 50px;
    width: 100%;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
    }
  }

  .login-button {
    width: 100%;
    height: 50px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
  }
}

.login-tips {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  text-align: center;

  p {
    margin: 5px 0;
    font-size: 14px;
    color: #6c757d;

    &:first-child {
      font-weight: bold;
      color: #495057;
    }
  }
}
</style>
