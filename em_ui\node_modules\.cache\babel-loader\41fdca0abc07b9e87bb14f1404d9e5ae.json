{"ast": null, "code": "import \"core-js/modules/es.array.includes.js\";\nexport default {\n  name: 'Trend',\n  props: {\n    value: {\n      type: String,\n      required: true\n    },\n    type: {\n      type: String,\n      validator: function validator(val) {\n        return ['up', 'down'].includes(val);\n      },\n      default: 'up'\n    },\n    prefix: {\n      type: String,\n      default: ''\n    },\n    suffix: {\n      type: String,\n      default: ''\n    },\n    reverse: {\n      type: Boolean,\n      default: false\n    }\n  },\n  computed: {\n    isUp: function isUp() {\n      var isUp = this.type === 'up';\n      if (this.reverse) {\n        isUp = !isUp;\n      }\n      return isUp;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAUA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAD;MACAA;MACAE;QAAA;MAAA;MACAC;IACA;IACAC;MACAJ;MACAG;IACA;IACAE;MACAL;MACAG;IACA;IACAG;MACAN;MACAG;IACA;EACA;EACAI;IACAC;MACA;MACA;QACAA;MACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "value", "type", "required", "validator", "default", "prefix", "suffix", "reverse", "computed", "isUp"], "sourceRoot": "src/components/Trend", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div :class=\"`trend ${isUp ? 'up' : 'down'}`\">\n        <span v-if=\"prefix\" class=\"prefix\">{{ prefix }}</span>\n        <span class=\"text\">{{ value }}</span>\n        <span v-if=\"suffix\" class=\"suffix\">{{ suffix }}</span>\n        <i :class=\"`${isUp ? 'el-icon-caret-top' : 'el-icon-caret-bottom'}`\" />\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'Trend',\n    props: {\n        value: {\n            type: String,\n            required: true\n        },\n        type: {\n            type: String,\n            validator: val => ['up', 'down'].includes(val),\n            default: 'up'\n        },\n        prefix: {\n            type: String,\n            default: ''\n        },\n        suffix: {\n            type: String,\n            default: ''\n        },\n        reverse: {\n            type: Boolean,\n            default: false\n        }\n    },\n    computed: {\n        isUp() {\n            let isUp = this.type === 'up'\n            if (this.reverse) {\n                isUp = !isUp\n            }\n            return isUp\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.trend {\n    display: inline-block;\n    &.up {\n        color: #67c23a;\n    }\n    &.down {\n        color: #f56c6c;\n    }\n    i {\n        margin-left: 5px;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}