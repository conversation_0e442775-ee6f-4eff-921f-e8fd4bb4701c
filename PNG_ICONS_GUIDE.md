# PNG图标使用指南

## 概述
项目已从SVG图标切换到PNG图标格式。本指南将帮助您正确使用和管理PNG图标。

## 可用图标列表

项目中当前可用的PNG图标：

| 图标名称 | 用途 | 示例 |
|---------|------|------|
| `404` | 错误/未找到 | 404页面、错误提示 |
| `building` | 建筑/楼宇 | 楼宇管理、建筑信息 |
| `complaint` | 投诉 | 投诉管理、反馈 |
| `dashboard` | 仪表盘 | 数据统计、概览 |
| `facilities` | 设施 | 公共设施、服务 |
| `home` | 首页/房间 | 首页、房间管理 |
| `notice` | 通知 | 公告、通知 |
| `payment` | 支付 | 费用管理、支付 |
| `repair` | 维修 | 报修、维护 |

## 使用方法

### 1. 基本用法

```vue
<template>
  <!-- 基本使用 -->
  <png-icon name="home" />
  
  <!-- 指定大小 -->
  <png-icon name="building" size="24" />
  <png-icon name="repair" size="32" />
  
  <!-- 使用像素值 -->
  <png-icon name="notice" :size="40" />
  
  <!-- 自定义CSS类 -->
  <png-icon name="payment" size="24" custom-class="my-icon" />
</template>
```

### 2. 在组件中使用

```vue
<template>
  <div class="stat-card">
    <div class="icon-wrapper">
      <png-icon name="dashboard" size="24" />
    </div>
    <div class="content">
      <h3>数据统计</h3>
      <p>查看系统概览</p>
    </div>
  </div>
</template>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.icon-wrapper {
  margin-right: 15px;
}

/* 如果需要改变图标颜色（适用于单色图标） */
.icon-wrapper .png-icon {
  filter: hue-rotate(120deg);
}
</style>
```

### 3. 图标别名

系统支持图标别名，您可以使用更语义化的名称：

```vue
<!-- 这些都会显示相同的图标 -->
<png-icon name="building" />
<png-icon name="office-building" />

<png-icon name="home" />
<png-icon name="house" />
<png-icon name="room" />

<png-icon name="repair" />
<png-icon name="tools" />
<png-icon name="maintenance" />
```

## 样式定制

### 1. 改变图标颜色

对于单色图标，可以使用CSS滤镜改变颜色：

```css
/* 变为白色 */
.white-icon {
  filter: brightness(0) invert(1);
}

/* 变为蓝色调 */
.blue-icon {
  filter: hue-rotate(240deg);
}

/* 增加饱和度 */
.saturated-icon {
  filter: saturate(2);
}
```

### 2. 添加边框和背景

```css
.bordered-icon {
  border: 2px solid #409eff;
  border-radius: 50%;
  padding: 8px;
  background: #f0f9ff;
}
```

### 3. 动画效果

```css
.animated-icon {
  transition: all 0.3s ease;
}

.animated-icon:hover {
  transform: scale(1.2) rotate(10deg);
}
```

## 在不同场景中的使用

### 1. 统计卡片

```vue
<template>
  <div class="stat-card">
    <div class="stat-icon building">
      <png-icon name="building" size="24" />
    </div>
    <div class="stat-info">
      <div class="stat-number">{{ buildingCount }}</div>
      <div class="stat-label">楼宇总数</div>
    </div>
  </div>
</template>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stat-icon.building {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon .png-icon {
  filter: brightness(0) invert(1);
}
</style>
```

### 2. 导航菜单

```vue
<template>
  <el-menu>
    <el-menu-item index="1">
      <png-icon name="dashboard" size="18" />
      <span>数据统计</span>
    </el-menu-item>
    <el-menu-item index="2">
      <png-icon name="building" size="18" />
      <span>楼宇管理</span>
    </el-menu-item>
  </el-menu>
</template>
```

### 3. 按钮图标

```vue
<template>
  <el-button type="primary">
    <png-icon name="repair" size="16" />
    报修管理
  </el-button>
</template>
```

## 添加新图标

1. 将PNG图标文件放入 `src/assets/icons/` 目录
2. 确保文件名使用小写字母和连字符
3. 在 `src/utils/iconMap.js` 中添加图标名称到 `availableIcons` 数组
4. 如需要，在 `iconAliases` 中添加别名

```javascript
// src/utils/iconMap.js
export const availableIcons = [
  '404',
  'building',
  // ... 其他图标
  'new-icon' // 添加新图标
]

export const iconAliases = {
  // ... 其他别名
  'alias-name': 'new-icon' // 添加别名
}
```

## 最佳实践

1. **统一尺寸**: 在同一界面中使用相同尺寸的图标
2. **语义化命名**: 使用有意义的图标名称和别名
3. **颜色一致性**: 在同一组件中保持图标颜色一致
4. **性能考虑**: 避免使用过大的图标文件
5. **后备方案**: 系统会自动使用404图标作为未找到图标的后备

## 故障排除

### 图标不显示
1. 检查图标名称是否正确
2. 确认PNG文件是否存在于 `src/assets/icons/` 目录
3. 检查图标是否已添加到 `availableIcons` 数组

### 图标颜色不对
1. 使用CSS滤镜调整颜色
2. 确保图标是单色的PNG文件
3. 检查父元素的CSS是否影响了图标显示

### 图标模糊
1. 确保PNG文件分辨率足够高
2. 避免将小图标放大显示
3. 使用适合的图标尺寸

## 示例页面

访问 `/system-manager/icon-demo` 查看所有可用图标的实际效果和使用示例。
