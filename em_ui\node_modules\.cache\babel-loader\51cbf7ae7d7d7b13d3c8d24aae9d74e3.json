{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nexport default {\n  name: '<PERSON><PERSON>',\n  props: {\n    showLogo: {\n      type: Boolean,\n      default: true\n    },\n    showTitle: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data: function data() {\n    return {\n      title: process.env.VUE_APP_TITLE,\n      logo: require('@/assets/images/logo.png')\n    };\n  },\n  computed: {\n    to: function to() {\n      var rtn = {};\n      if (this.$store.state.settings.enableDashboard) {\n        rtn.name = 'dashboard';\n      }\n      return rtn;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAcA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;MACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "showLogo", "type", "default", "showTitle", "data", "title", "logo", "computed", "to", "rtn"], "sourceRoot": "src/layout/components/Logo", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <router-link v-slot=\"{ navigate }\" custom :to=\"to\" :class=\"{\n        'title': true,\n        'is-link': $store.state.settings.enableDashboard\n    }\" :title=\"title\" tag=\"div\"\n    >\n        <div @click=\"navigate\">\n            <img v-if=\"showLogo\" :src=\"logo\" class=\"logo\">\n            <span v-if=\"showTitle\">{{ title }}</span>\n        </div>\n    </router-link>\n</template>\n\n<script>\nexport default {\n    name: 'Logo',\n    props: {\n        showLogo: {\n            type: Boolean,\n            default: true\n        },\n        showTitle: {\n            type: Boolean,\n            default: true\n        }\n    },\n    data() {\n        return {\n            title: process.env.VUE_APP_TITLE,\n            logo: require('@/assets/images/logo.png')\n        }\n    },\n    computed: {\n        to() {\n            let rtn = {}\n            if (this.$store.state.settings.enableDashboard) {\n                rtn.name = 'dashboard'\n            }\n            return rtn\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.title {\n    position: fixed;\n    z-index: 1000;\n    top: 0;\n    width: inherit;\n    padding: 0 10px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: $g-sidebar-logo-height;\n    text-align: center;\n    overflow: hidden;\n    text-decoration: none;\n    &.is-link {\n        cursor: pointer;\n    }\n    .logo {\n        width: 30px;\n        height: 30px;\n        & + span {\n            margin-left: 10px;\n        }\n    }\n    span {\n        display: block;\n        font-weight: bold;\n        color: #fff;\n        @include text-overflow;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}