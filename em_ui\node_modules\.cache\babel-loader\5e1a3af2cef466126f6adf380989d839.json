{"ast": null, "code": "export default {\n  name: '<PERSON>B<PERSON>',\n  props: {\n    showMore: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      isOpen: false\n    };\n  },\n  methods: {\n    toggle: function toggle() {\n      this.isOpen = !this.isOpen;\n      this.$emit('toggle', this.isOpen);\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAUA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "showMore", "type", "default", "data", "isOpen", "methods", "toggle"], "sourceRoot": "src/components/SearchBar", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"search-container\">\n        <slot />\n        <div v-if=\"showMore\" class=\"more\">\n            <el-button type=\"text\" size=\"small\" :icon=\"isOpen ? 'el-icon-caret-top' : 'el-icon-caret-bottom'\" @click=\"toggle\">{{ isOpen ? '收起' : '展开' }}</el-button>\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'SearchBar',\n    props: {\n        showMore: {\n            type: Boolean,\n            default: false\n        }\n    },\n    data() {\n        return {\n            isOpen: false\n        }\n    },\n    methods: {\n        toggle() {\n            this.isOpen = !this.isOpen\n            this.$emit('toggle', this.isOpen)\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.search-container {\n    position: relative;\n    margin: 20px 0;\n    padding: 20px;\n    background-color: #f7f8fa;\n    &:first-child {\n        margin-top: 0;\n    }\n    ::v-deep .el-form {\n        margin-bottom: -18px;\n        .el-select {\n            width: 100%;\n        }\n        .el-date-editor {\n            width: 100%;\n        }\n    }\n    .more {\n        text-align: center;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}