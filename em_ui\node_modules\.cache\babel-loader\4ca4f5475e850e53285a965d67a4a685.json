{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n/**\n * 全局组件自动注册\n *\n * 全局组件各个组件按文件夹区分，文件夹名称与组件名无关联，但建议与组件名保持一致\n * 文件夹内至少保留一个文件名为 index 的组件入口，例如 index.vue\n * 普通组件必须设置 name 并保证其唯一，自动注册会将组件的 name 设为组件名，可参考 SvgIcon 组件写法\n * 如果组件是通过 js 进行调用，则确保组件入口文件为 index.js，可参考 ExampleNotice 组件\n */\n\nimport Vue from 'vue';\nvar componentsContext = require.context('./', true, /index.(vue|js)$/);\ncomponentsContext.keys().forEach(function (file_name) {\n  // 获取文件中的 default 模块\n  var componentConfig = componentsContext(file_name).default;\n  if (/.vue$/.test(file_name)) {\n    Vue.component(componentConfig.name, componentConfig);\n  } else {\n    Vue.use(componentConfig);\n  }\n});", "map": {"version": 3, "names": ["<PERSON><PERSON>", "componentsContext", "require", "context", "keys", "for<PERSON>ach", "file_name", "componentConfig", "default", "test", "component", "name", "use"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/autoRegister.js"], "sourcesContent": ["/**\n * 全局组件自动注册\n *\n * 全局组件各个组件按文件夹区分，文件夹名称与组件名无关联，但建议与组件名保持一致\n * 文件夹内至少保留一个文件名为 index 的组件入口，例如 index.vue\n * 普通组件必须设置 name 并保证其唯一，自动注册会将组件的 name 设为组件名，可参考 SvgIcon 组件写法\n * 如果组件是通过 js 进行调用，则确保组件入口文件为 index.js，可参考 ExampleNotice 组件\n */\n\nimport Vue from 'vue'\n\nconst componentsContext = require.context('./', true, /index.(vue|js)$/)\ncomponentsContext.keys().forEach(file_name => {\n    // 获取文件中的 default 模块\n    const componentConfig = componentsContext(file_name).default\n    if (/.vue$/.test(file_name)) {\n        Vue.component(componentConfig.name, componentConfig)\n    } else {\n        Vue.use(componentConfig)\n    }\n})\n"], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,GAAG,MAAM,KAAK;AAErB,IAAMC,iBAAiB,GAAGC,OAAO,CAACC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC;AACxEF,iBAAiB,CAACG,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,UAAAC,SAAS,EAAI;EAC1C;EACA,IAAMC,eAAe,GAAGN,iBAAiB,CAACK,SAAS,CAAC,CAACE,OAAO;EAC5D,IAAI,OAAO,CAACC,IAAI,CAACH,SAAS,CAAC,EAAE;IACzBN,GAAG,CAACU,SAAS,CAACH,eAAe,CAACI,IAAI,EAAEJ,eAAe,CAAC;EACxD,CAAC,MAAM;IACHP,GAAG,CAACY,GAAG,CAACL,eAAe,CAAC;EAC5B;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}