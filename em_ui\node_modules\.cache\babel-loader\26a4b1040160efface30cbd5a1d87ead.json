{"ast": null, "code": "import card from '@/views/component_extend_example/card.vue';\nexport default {\n  data: function data() {\n    return {\n      isAdmin: false\n    };\n  },\n  components: {\n    'Card': card\n  },\n  created: function created() {\n    var isAdmin = localStorage.getItem('isAdmin');\n    this.isAdmin = isAdmin == 'true' ? true : false;\n  },\n  methods: {\n    open: function open(url) {\n      window.open(url, 'top');\n    },\n    onIconClick: function onIconClick(val) {\n      this.$message({\n        message: \"\\u4F60\\u70B9\\u51FB\\u4E86\\uFF1A\".concat(val),\n        type: 'info'\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": "AA+IA;AACA;EACAA;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;EAEA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;QACAC;QACAC;MACA;IACA;EAEA;AACA", "names": ["data", "isAdmin", "components", "created", "methods", "open", "window", "onIconClick", "message", "type"], "sourceRoot": "src/views", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div>\n    <page-header title=\"欢迎使用「社区管理系统」\">\n      <template #content>\n        <div>\n          <!-- <div>注：在作者就职的公司，本框架已在电商、直播、OA、ERP等多个不同领域的中后台系统中应用并稳定运行</div> -->\n        </div>\n      </template>\n\n    </page-header>\n    <page-main>\n      <el-row :gutter=\"24\" v-show=\"isAdmin\">\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/rq-manager/info\"\n            num=\"社区管理\"\n            tip=\"主要管理社区基本内容\"\n            type=\"orange\"\n            icon=\"el-icon-office-building\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/building-manager/building\"\n            num=\"楼宇管理\"\n            tip=\"社区内所有楼宇详细记录\"\n            type=\"orange\"\n            icon=\"el-icon-school\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/building-manager/room\"\n            num=\"房间管理\"\n            tip=\"楼盘内房间管理\"\n            type=\"pink\"\n            icon=\"el-icon-house\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/rq-manager/facilities\"\n            num=\"公共设施\"\n            tip=\"社区内所有公共设施\"\n            type=\"pink\"\n            icon=\"el-icon-chat-square\"\n          />\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"24\" v-show=\"isAdmin\">\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/guarantee-manager/repair\"\n            num=\"报修管理\"\n            tip=\"及时处理业主的报修\"\n            type=\"violet\"\n            icon=\"el-icon-attract\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/guarantee-manager/complaint\"\n            num=\"投诉管理\"\n            tip=\"查看业主反映的问题/建议\"\n            type=\"violet\"\n            icon=\"el-icon-message\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card title num=\"业主管理\" tip=\"社区内业主账号的管理\" type=\"blue\" icon=\"el-icon-user\" />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card title num=\"费用管理\" tip=\"社区内业主的费用管理\" type=\"blue\" icon=\"el-icon-coin\" />\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"24\" v-show=\"isAdmin\">\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/statistics/dashboard\"\n            num=\"数据统计\"\n            tip=\"查看系统数据概览和统计信息\"\n            type=\"green\"\n            icon=\"el-icon-data-analysis\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/system-manager/automatic-task\"\n            num=\"自动任务\"\n            tip=\"系统自动执行的任务管理\"\n            type=\"aaa\"\n            icon=\"el-icon-date\"\n          />\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"24\" v-show=\"!isAdmin\">\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card title=\"/user/base/info\" num=\"个人信息\" tip=\"修改自己的信息\" type=\"blue\" icon=\"el-icon-user\" />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/user/base/reset_pwd\"\n            num=\"密码修改\"\n            tip=\"修改自己的密码\"\n            type=\"orange\"\n            icon=\"el-icon-lock\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/user/pay_record\"\n            num=\"物业收费\"\n            tip=\"缴纳本月费用\"\n            type=\"pink\"\n            icon=\"el-icon-collection-tag\"\n          />\n        </el-col>\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/user/repair/add\"\n            num=\"申请报修\"\n            tip=\"设施损坏在此报修\"\n            type=\"aaa\"\n            icon=\"el-icon-tickets\"\n          />\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"24\" v-show=\"!isAdmin\">\n        <el-col :span=\"6\" :offset=\"0\">\n          <Card\n            title=\"/user/complaint/add\"\n            num=\"发起投诉\"\n            tip=\"对物业服务进行投诉\"\n            type=\"violet\"\n            icon=\"el-icon-phone-outline\"\n          />\n        </el-col>\n      </el-row>\n    </page-main>\n  </div>\n</template>\n\n<script>\nimport card from '@/views/component_extend_example/card.vue'\nexport default {\n  data () {\n    return {\n      isAdmin: false\n    }\n  },\n  components: {\n    'Card': card,\n  },\n  created () {\n    let isAdmin = localStorage.getItem('isAdmin')\n    this.isAdmin = isAdmin == 'true' ? true : false\n\n  },\n  methods: {\n    open (url) {\n      window.open(url, 'top')\n    },\n    onIconClick (val) {\n      this.$message({\n        message: `你点击了：${val}`,\n        type: 'info'\n      })\n    },\n\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-row {\n    margin: 10px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}