{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getNotice, saveNotice, deleteNotice } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nimport { mavonEditor } from 'mavon-editor';\nimport 'mavon-editor/dist/css/index.css';\nexport default {\n  components: {\n    mavonEditor: mavonEditor\n  },\n  data: function data() {\n    return {\n      rTime: rTime,\n      loading: false,\n      labelCol: {\n        span: 2\n      },\n      wrapperCol: {\n        span: 16\n      },\n      table_selectedRowKeys: [],\n      notice_query_type: 'title',\n      notice_query_buttonTitle: '搜索',\n      notice_query_text: '',\n      notice_save_title: '新增社区物业公告',\n      notice_save_modalVisible: false,\n      notice_save_current_text: '',\n      notice_form_data: {},\n      notice_data_list: [],\n      markdownOption: {\n        bold: true,\n        // 粗体\n        italic: true,\n        // 斜体\n        header: true,\n        // 标题\n        underline: true,\n        // 下划线\n        strikethrough: true,\n        // 中划线\n        mark: true,\n        // 标记\n        superscript: true,\n        // 上角标\n        subscript: true,\n        // 下角标\n        quote: true,\n        // 引用\n        ol: true,\n        // 有序列表\n        ul: true,\n        // 无序列表\n        link: true,\n        // 链接\n        imagelink: true,\n        // 图片链接\n        code: true,\n        // code\n        table: true,\n        // 表格\n        fullscreen: true,\n        // 全屏编辑\n        htmlcode: true,\n        // 展示html源码\n        undo: true,\n        // 上一步\n        redo: true,\n        // 下一步\n        trash: true,\n        // 清空\n        navigation: true,\n        // 导航目录\n        alignleft: true,\n        // 左对齐\n        aligncenter: true,\n        // 居中\n        alignright: true,\n        // 右对齐\n        preview: true // 预览\n      }\n    };\n  },\n  created: function created() {\n    this.Get_noticeDataList();\n  },\n  watch: {\n    notice_save_modalVisible: function notice_save_modalVisible(val) {\n      if (!val) {\n        this.notice_form_data = {};\n      } else {\n        //saveModal被显示\n        this.notice_save_current_text = this.notice_form_data.text;\n      }\n    },\n    notice_save_current_text: function notice_save_current_text(val) {\n      var mdit = mavonEditor.getMarkdownIt();\n      this.notice_form_data.text = val;\n      this.notice_form_data.html = mdit.render(val);\n    }\n  },\n  methods: {\n    Get_noticeDataList: function Get_noticeDataList() {\n      var _this = this;\n      getNotice().then(function (res) {\n        _this.notice_query_buttonTitle = '搜索';\n        _this.notice_data_list = res.data;\n        _this.notice_save_title = '新增社区物业公告';\n      });\n    },\n    Query_noticeDataList: function Query_noticeDataList() {\n      var _this2 = this;\n      var text = this.notice_query_text;\n      var temp_list = [];\n      this.notice_data_list.forEach(function (item) {\n        if (item[_this2.notice_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.notice_query_buttonTitle = '返回';\n      this.notice_data_list = temp_list;\n    },\n    Edit_noticeData: function Edit_noticeData(form) {\n      this.notice_save_title = '编辑社区物业公告';\n      this.notice_form_data = JSON.parse(JSON.stringify(form));\n      this.notice_save_modalVisible = true;\n    },\n    Del_noticeData: function Del_noticeData(id) {\n      var _this3 = this;\n      deleteNotice(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_noticeDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_noticeData(_this4.notice_data_list[i].id);\n      });\n    },\n    Save_noticeData: function Save_noticeData() {\n      var _this5 = this;\n      saveNotice(this.notice_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.notice_save_modalVisible = false;\n        _this5.Get_noticeDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_date_changeHandler: function Form_date_changeHandler(date) {\n      this.notice_form_data.date = date._d;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AA6FA;AACA;AACA;AACA;AACA;AAEA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MAEA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA/B;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACA+B;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA", "names": ["components", "mavon<PERSON><PERSON><PERSON>", "data", "rTime", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "notice_query_type", "notice_query_buttonTitle", "notice_query_text", "notice_save_title", "notice_save_modalVisible", "notice_save_current_text", "notice_form_data", "notice_data_list", "markdownOption", "bold", "italic", "header", "underline", "strikethrough", "mark", "superscript", "subscript", "quote", "ol", "ul", "link", "imagelink", "code", "table", "fullscreen", "htmlcode", "undo", "redo", "trash", "navigation", "alignleft", "aligncenter", "alignright", "preview", "created", "watch", "methods", "Get_noticeDataList", "getNotice", "_this", "Query_noticeDataList", "temp_list", "Edit_noticeData", "Del_noticeData", "deleteNotice", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_noticeData", "saveNotice", "_this5", "Table_selectChange", "Form_date_changeHandler"], "sourceRoot": "src/views/admin/rq", "sources": ["rq_notices.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"社区物业公告管理\">\n      <div class=\"head\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"公告编号\"\n          v-model=\"notice_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"id\">公告编号</a-select-option>\n          <a-select-option value=\"title\">公告标题</a-select-option>\n          <a-select-option value=\"author\">发布人</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"notice_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"notice_query_buttonTitle\"\n          size=\"large\"\n          @search=\"notice_query_buttonTitle == '搜索' ? Query_noticeDataList() : Get_noticeDataList()\"\n        />\n        <a-button\n          type=\"primary\"\n          style=\"height: 38px;\"\n          @click=\"notice_save_modalVisible = true\"\n        >添加物业公告</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 40px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「物业公告」</a-button>\n      </div>\n      <a-table :data-source=\"notice_data_list\">\n        <a-table-column key=\"id\" title=\"公告编号\" data-index=\"id\" />\n        <a-table-column key=\"title\" title=\"公告标题\" data-index=\"title\" />\n        <a-table-column key=\"date\" title=\"发布时间\" data-index=\"date\">\n          <!-- rTime -->\n          <template slot-scope=\"text, record\">\n            <span>{{rTime(record.date)}}</span>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"author\" title=\"发布人\" data-index=\"author\" />\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_noticeData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_noticeData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"notice_save_modalVisible\"\n      :title=\"notice_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      @ok=\"Save_noticeData\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      layout=\"horizontal\"\n      style=\"width: 60vw;\"\n    >\n      <a-form-model :model=\"notice_form_data\" :label-col=\"labelCol\" :wrapper-col=\"wrapperCol\">\n        <a-form-model-item label=\"公告标题\">\n          <a-input v-model=\"notice_form_data.title\" />\n        </a-form-model-item>\n        <mavon-editor\n          v-model=\"notice_save_current_text\"\n          :toolbars=\"markdownOption\"\n          style=\"z-index: 9;\"\n        />\n        <el-row :gutter=\"20\" style=\"margin-top: 10px;\">\n          <el-col :span=\"5\" :offset=\"0\">\n            <a-input v-model=\"notice_form_data.author\" placeholder=\"发布人\" />\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-date-picker\n              :default-value=\"notice_form_data.date\"\n              @change=\"Form_date_changeHandler\"\n              placeholder=\"发布时间\"\n            />\n          </el-col>\n        </el-row>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getNotice, saveNotice, deleteNotice } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\nimport { mavonEditor } from 'mavon-editor'\nimport 'mavon-editor/dist/css/index.css'\n\nexport default {\n  components: {\n    mavonEditor\n  },\n  data () {\n    return {\n      rTime,\n      loading: false,\n      labelCol: { span: 2 },\n      wrapperCol: { span: 16 },\n      table_selectedRowKeys: [],\n      notice_query_type: 'title',\n      notice_query_buttonTitle: '搜索',\n      notice_query_text: '',\n      notice_save_title: '新增社区物业公告',\n      notice_save_modalVisible: false,\n      notice_save_current_text: '',\n      notice_form_data: {},\n      notice_data_list: [],\n      markdownOption: {\n        bold: true, // 粗体\n        italic: true, // 斜体\n        header: true, // 标题\n        underline: true, // 下划线\n        strikethrough: true, // 中划线\n        mark: true, // 标记\n        superscript: true, // 上角标\n        subscript: true, // 下角标\n        quote: true, // 引用\n        ol: true, // 有序列表\n        ul: true, // 无序列表\n        link: true, // 链接\n        imagelink: true, // 图片链接\n        code: true, // code\n        table: true, // 表格\n        fullscreen: true, // 全屏编辑\n        htmlcode: true, // 展示html源码\n        undo: true, // 上一步\n        redo: true, // 下一步\n        trash: true, // 清空\n        navigation: true, // 导航目录\n        alignleft: true, // 左对齐\n        aligncenter: true, // 居中\n        alignright: true, // 右对齐\n        preview: true, // 预览\n\n      }\n    }\n  },\n  created () {\n    this.Get_noticeDataList()\n  },\n  watch: {\n    notice_save_modalVisible (val) {\n      if (!val) {\n        this.notice_form_data = {}\n      } else {\n        //saveModal被显示\n        this.notice_save_current_text = this.notice_form_data.text\n      }\n    },\n    notice_save_current_text (val) {\n      const mdit = mavonEditor.getMarkdownIt()\n      this.notice_form_data.text = val\n      this.notice_form_data.html = mdit.render(val)\n    }\n  },\n  methods: {\n    Get_noticeDataList () {\n      getNotice().then(res => {\n        this.notice_query_buttonTitle = '搜索'\n        this.notice_data_list = res.data\n        this.notice_save_title = '新增社区物业公告'\n      })\n    },\n    Query_noticeDataList () {\n      let text = this.notice_query_text\n      let temp_list = []\n      this.notice_data_list.forEach(item => {\n        if (item[this.notice_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.notice_query_buttonTitle = '返回'\n      this.notice_data_list = temp_list\n    },\n    Edit_noticeData (form) {\n      this.notice_save_title = '编辑社区物业公告'\n      this.notice_form_data = JSON.parse(JSON.stringify(form))\n      this.notice_save_modalVisible = true\n    },\n    Del_noticeData (id) {\n      deleteNotice(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_noticeDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_noticeData(this.notice_data_list[i].id)\n      })\n    },\n    Save_noticeData () {\n      saveNotice(this.notice_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.notice_save_modalVisible = false\n        this.Get_noticeDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_date_changeHandler (date) {\n      this.notice_form_data.date = date._d\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n</style>\n\n<style lang=\"scss\" >\n.ant-modal-content {\n    width: 60vw;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}