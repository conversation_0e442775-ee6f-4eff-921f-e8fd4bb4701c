{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"footer\", {\n    staticClass: \"copyright\"\n  }, [_vm._v(\" Copyright © \" + _vm._s(_vm.$store.state.settings.copyrightDates) + \" \"), _c(\"component\", _vm._b({}, \"component\", _vm.linkProps(), false), [_vm._v(_vm._s(_vm.$store.state.settings.copyrightCompany))])], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "$store", "state", "settings", "copyrightDates", "_b", "linkProps", "copyrightCompany", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/Copyright/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"footer\",\n    { staticClass: \"copyright\" },\n    [\n      _vm._v(\n        \" Copyright © \" + _vm._s(_vm.$store.state.settings.copyrightDates) + \" \"\n      ),\n      _c(\"component\", _vm._b({}, \"component\", _vm.linkProps(), false), [\n        _vm._v(_vm._s(_vm.$store.state.settings.copyrightCompany)),\n      ]),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,QAAQ,EACR;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEH,GAAG,CAACI,EAAE,CACJ,eAAe,GAAGJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,GAAG,GACvE,CAAC,EACDR,EAAE,CAAC,WAAW,EAAED,GAAG,CAACU,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,EAAEV,GAAG,CAACW,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAC/DX,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACI,gBAAgB,CAAC,CAAC,CAC3D,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBd,MAAM,CAACe,aAAa,GAAG,IAAI;AAE3B,SAASf,MAAM,EAAEc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}