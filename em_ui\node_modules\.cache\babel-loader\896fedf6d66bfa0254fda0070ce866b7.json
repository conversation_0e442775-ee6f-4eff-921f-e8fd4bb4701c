{"ast": null, "code": "export default {\n  name: 'user_info',\n  data: function data() {\n    return {\n      current_form: {}\n    };\n  },\n  methods: {\n    save_userInfo: function save_userInfo() {}\n  }\n};", "map": {"version": 3, "mappings": "AAkCA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC,yCAEA;EACA;AAEA", "names": ["name", "data", "current_form", "methods", "save_userInfo"], "sourceRoot": "src/views/admin/user", "sources": ["user_info.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card title=\"个人信息\">\n      <a-form-model\n        ref=\"current_form\"\n        :rules=\"rules\"\n        :model=\"current_form\"\n        style=\"width: 80vh;\"\n        :label-col=\"labelCol\"\n        :wrapper-col=\"wrapperCol\"\n      >\n        <a-form-model-item label=\"真实姓名\" prop=\"fullName\">\n          <a-input v-model=\"current_form.userName\" placeholder=\"您的姓名\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"联系电话\" prop=\"phone\">\n          <a-input v-model=\"current_form.phone\" placeholder=\"您的联系方式\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"电子邮箱\" prop=\"email\">\n          <a-input v-model=\"current_form.email\" placeholder=\"您的电子邮箱\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"工作单位\" prop=\"workUnit\">\n          <a-input v-model=\"current_form.workUnit\" placeholder=\"您的工作单位\" />\n        </a-form-model-item>\n\n        <a-form-model-item style=\"margin-left: 33.3%;\">\n          <a-button type=\"primary\" @click=\"save_userInfo\">保存</a-button>\n          <a-button style=\"margin-left: 10px;\">取消</a-button>\n        </a-form-model-item>\n      </a-form-model>\n    </a-card>\n  </page-main>\n</template>\n\n<script>\nexport default {\n  name: 'user_info',\n  data () {\n    return {\n      current_form: {},\n    }\n  },\n  methods: {\n    save_userInfo () {\n\n    }\n  },\n\n}\n</script>"]}, "metadata": {}, "sourceType": "module"}