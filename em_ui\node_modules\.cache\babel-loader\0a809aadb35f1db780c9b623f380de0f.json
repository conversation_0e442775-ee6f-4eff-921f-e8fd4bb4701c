{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"page-main\"\n  }, [_vm.title ? _c(\"div\", {\n    staticClass: \"title-container\"\n  }, [_vm._v(_vm._s(_vm.title))]) : _vm._e(), _vm._t(\"default\")], 2);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "title", "_v", "_s", "_e", "_t", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/PageMain/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"page-main\" },\n    [\n      _vm.title\n        ? _c(\"div\", { staticClass: \"title-container\" }, [\n            _vm._v(_vm._s(_vm.title)),\n          ])\n        : _vm._e(),\n      _vm._t(\"default\"),\n    ],\n    2\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEH,GAAG,CAACI,KAAK,GACLH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACI,KAAK,CAAC,CAAC,CAC1B,CAAC,GACFJ,GAAG,CAACO,EAAE,CAAC,CAAC,EACZP,GAAG,CAACQ,EAAE,CAAC,SAAS,CAAC,CAClB,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBV,MAAM,CAACW,aAAa,GAAG,IAAI;AAE3B,SAASX,MAAM,EAAEU,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}