{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.array.concat.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.regexp.exec.js\");\nrequire(\"core-js/modules/es.regexp.test.js\");\nrequire(\"core-js/modules/web.dom-collections.iterator.js\");\nexports.__esModule = true;\nvar aria = aria || {};\naria.Utils = aria.Utils || {};\n\n/**\n * @desc Set focus on descendant nodes until the first focusable element is\n *       found.\n * @param element\n *          DOM node for which to find the first focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\naria.Utils.focusFirstDescendant = function (element) {\n  for (var i = 0; i < element.childNodes.length; i++) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusFirstDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Find the last descendant node that is focusable.\n * @param element\n *          DOM node for which to find the last focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\n\naria.Utils.focusLastDescendant = function (element) {\n  for (var i = element.childNodes.length - 1; i >= 0; i--) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusLastDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Set Attempt to set focus on the current node.\n * @param element\n *          The node to attempt to focus on.\n * @returns\n *  true if element is focused.\n */\naria.Utils.attemptFocus = function (element) {\n  if (!aria.Utils.isFocusable(element)) {\n    return false;\n  }\n  aria.Utils.IgnoreUtilFocusChanges = true;\n  try {\n    element.focus();\n  } catch (e) {}\n  aria.Utils.IgnoreUtilFocusChanges = false;\n  return document.activeElement === element;\n};\naria.Utils.isFocusable = function (element) {\n  if (element.tabIndex > 0 || element.tabIndex === 0 && element.getAttribute('tabIndex') !== null) {\n    return true;\n  }\n  if (element.disabled) {\n    return false;\n  }\n  switch (element.nodeName) {\n    case 'A':\n      return !!element.href && element.rel !== 'ignore';\n    case 'INPUT':\n      return element.type !== 'hidden' && element.type !== 'file';\n    case 'BUTTON':\n    case 'SELECT':\n    case 'TEXTAREA':\n      return true;\n    default:\n      return false;\n  }\n};\n\n/**\n * 触发一个事件\n * mouseenter, mouseleave, mouseover, keyup, change, click 等\n * @param  {Element} elm\n * @param  {String} name\n * @param  {*} opts\n */\naria.Utils.triggerEvent = function (elm, name) {\n  var eventName = void 0;\n  if (/^mouse|click/.test(name)) {\n    eventName = 'MouseEvents';\n  } else if (/^key/.test(name)) {\n    eventName = 'KeyboardEvent';\n  } else {\n    eventName = 'HTMLEvents';\n  }\n  var evt = document.createEvent(eventName);\n  for (var _len = arguments.length, opts = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    opts[_key - 2] = arguments[_key];\n  }\n  evt.initEvent.apply(evt, [name].concat(opts));\n  elm.dispatchEvent ? elm.dispatchEvent(evt) : elm.fireEvent('on' + name, evt);\n  return elm;\n};\naria.Utils.keys = {\n  tab: 9,\n  enter: 13,\n  space: 32,\n  left: 37,\n  up: 38,\n  right: 39,\n  down: 40,\n  esc: 27\n};\nexports.default = aria.Utils;", "map": {"version": 3, "names": ["require", "exports", "__esModule", "aria", "Utils", "focusFirstDescendant", "element", "i", "childNodes", "length", "child", "attemptFocus", "focusLastDescendant", "isFocusable", "IgnoreUtilFocusChanges", "focus", "e", "document", "activeElement", "tabIndex", "getAttribute", "disabled", "nodeName", "href", "rel", "type", "triggerEvent", "elm", "name", "eventName", "test", "evt", "createEvent", "_len", "arguments", "opts", "Array", "_key", "initEvent", "apply", "concat", "dispatchEvent", "fireEvent", "keys", "tab", "enter", "space", "left", "up", "right", "down", "esc", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/lib/utils/aria-utils.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\nvar aria = aria || {};\n\naria.Utils = aria.Utils || {};\n\n/**\n * @desc Set focus on descendant nodes until the first focusable element is\n *       found.\n * @param element\n *          DOM node for which to find the first focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\naria.Utils.focusFirstDescendant = function (element) {\n  for (var i = 0; i < element.childNodes.length; i++) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusFirstDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Find the last descendant node that is focusable.\n * @param element\n *          DOM node for which to find the last focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\n\naria.Utils.focusLastDescendant = function (element) {\n  for (var i = element.childNodes.length - 1; i >= 0; i--) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusLastDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Set Attempt to set focus on the current node.\n * @param element\n *          The node to attempt to focus on.\n * @returns\n *  true if element is focused.\n */\naria.Utils.attemptFocus = function (element) {\n  if (!aria.Utils.isFocusable(element)) {\n    return false;\n  }\n  aria.Utils.IgnoreUtilFocusChanges = true;\n  try {\n    element.focus();\n  } catch (e) {}\n  aria.Utils.IgnoreUtilFocusChanges = false;\n  return document.activeElement === element;\n};\n\naria.Utils.isFocusable = function (element) {\n  if (element.tabIndex > 0 || element.tabIndex === 0 && element.getAttribute('tabIndex') !== null) {\n    return true;\n  }\n\n  if (element.disabled) {\n    return false;\n  }\n\n  switch (element.nodeName) {\n    case 'A':\n      return !!element.href && element.rel !== 'ignore';\n    case 'INPUT':\n      return element.type !== 'hidden' && element.type !== 'file';\n    case 'BUTTON':\n    case 'SELECT':\n    case 'TEXTAREA':\n      return true;\n    default:\n      return false;\n  }\n};\n\n/**\n * 触发一个事件\n * mouseenter, mouseleave, mouseover, keyup, change, click 等\n * @param  {Element} elm\n * @param  {String} name\n * @param  {*} opts\n */\naria.Utils.triggerEvent = function (elm, name) {\n  var eventName = void 0;\n\n  if (/^mouse|click/.test(name)) {\n    eventName = 'MouseEvents';\n  } else if (/^key/.test(name)) {\n    eventName = 'KeyboardEvent';\n  } else {\n    eventName = 'HTMLEvents';\n  }\n  var evt = document.createEvent(eventName);\n\n  for (var _len = arguments.length, opts = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    opts[_key - 2] = arguments[_key];\n  }\n\n  evt.initEvent.apply(evt, [name].concat(opts));\n  elm.dispatchEvent ? elm.dispatchEvent(evt) : elm.fireEvent('on' + name, evt);\n\n  return elm;\n};\n\naria.Utils.keys = {\n  tab: 9,\n  enter: 13,\n  space: 32,\n  left: 37,\n  up: 38,\n  right: 39,\n  down: 40,\n  esc: 27\n};\n\nexports.default = aria.Utils;"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzB,IAAIC,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;AAErBA,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,IAAI,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAD,IAAI,CAACC,KAAK,CAACC,oBAAoB,GAAG,UAAUC,OAAO,EAAE;EACnD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACE,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAClD,IAAIG,KAAK,GAAGJ,OAAO,CAACE,UAAU,CAACD,CAAC,CAAC;IACjC,IAAIJ,IAAI,CAACC,KAAK,CAACO,YAAY,CAACD,KAAK,CAAC,IAAIP,IAAI,CAACC,KAAK,CAACC,oBAAoB,CAACK,KAAK,CAAC,EAAE;MAC5E,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAP,IAAI,CAACC,KAAK,CAACQ,mBAAmB,GAAG,UAAUN,OAAO,EAAE;EAClD,KAAK,IAAIC,CAAC,GAAGD,OAAO,CAACE,UAAU,CAACC,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvD,IAAIG,KAAK,GAAGJ,OAAO,CAACE,UAAU,CAACD,CAAC,CAAC;IACjC,IAAIJ,IAAI,CAACC,KAAK,CAACO,YAAY,CAACD,KAAK,CAAC,IAAIP,IAAI,CAACC,KAAK,CAACQ,mBAAmB,CAACF,KAAK,CAAC,EAAE;MAC3E,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAP,IAAI,CAACC,KAAK,CAACO,YAAY,GAAG,UAAUL,OAAO,EAAE;EAC3C,IAAI,CAACH,IAAI,CAACC,KAAK,CAACS,WAAW,CAACP,OAAO,CAAC,EAAE;IACpC,OAAO,KAAK;EACd;EACAH,IAAI,CAACC,KAAK,CAACU,sBAAsB,GAAG,IAAI;EACxC,IAAI;IACFR,OAAO,CAACS,KAAK,CAAC,CAAC;EACjB,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;EACbb,IAAI,CAACC,KAAK,CAACU,sBAAsB,GAAG,KAAK;EACzC,OAAOG,QAAQ,CAACC,aAAa,KAAKZ,OAAO;AAC3C,CAAC;AAEDH,IAAI,CAACC,KAAK,CAACS,WAAW,GAAG,UAAUP,OAAO,EAAE;EAC1C,IAAIA,OAAO,CAACa,QAAQ,GAAG,CAAC,IAAIb,OAAO,CAACa,QAAQ,KAAK,CAAC,IAAIb,OAAO,CAACc,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;IAC/F,OAAO,IAAI;EACb;EAEA,IAAId,OAAO,CAACe,QAAQ,EAAE;IACpB,OAAO,KAAK;EACd;EAEA,QAAQf,OAAO,CAACgB,QAAQ;IACtB,KAAK,GAAG;MACN,OAAO,CAAC,CAAChB,OAAO,CAACiB,IAAI,IAAIjB,OAAO,CAACkB,GAAG,KAAK,QAAQ;IACnD,KAAK,OAAO;MACV,OAAOlB,OAAO,CAACmB,IAAI,KAAK,QAAQ,IAAInB,OAAO,CAACmB,IAAI,KAAK,MAAM;IAC7D,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,UAAU;MACb,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAChB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAtB,IAAI,CAACC,KAAK,CAACsB,YAAY,GAAG,UAAUC,GAAG,EAAEC,IAAI,EAAE;EAC7C,IAAIC,SAAS,GAAG,KAAK,CAAC;EAEtB,IAAI,cAAc,CAACC,IAAI,CAACF,IAAI,CAAC,EAAE;IAC7BC,SAAS,GAAG,aAAa;EAC3B,CAAC,MAAM,IAAI,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,EAAE;IAC5BC,SAAS,GAAG,eAAe;EAC7B,CAAC,MAAM;IACLA,SAAS,GAAG,YAAY;EAC1B;EACA,IAAIE,GAAG,GAAGd,QAAQ,CAACe,WAAW,CAACH,SAAS,CAAC;EAEzC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACzB,MAAM,EAAE0B,IAAI,GAAGC,KAAK,CAACH,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;IACtGF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;EAClC;EAEAN,GAAG,CAACO,SAAS,CAACC,KAAK,CAACR,GAAG,EAAE,CAACH,IAAI,CAAC,CAACY,MAAM,CAACL,IAAI,CAAC,CAAC;EAC7CR,GAAG,CAACc,aAAa,GAAGd,GAAG,CAACc,aAAa,CAACV,GAAG,CAAC,GAAGJ,GAAG,CAACe,SAAS,CAAC,IAAI,GAAGd,IAAI,EAAEG,GAAG,CAAC;EAE5E,OAAOJ,GAAG;AACZ,CAAC;AAEDxB,IAAI,CAACC,KAAK,CAACuC,IAAI,GAAG;EAChBC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAE,EAAE;EACRC,EAAE,EAAE,EAAE;EACNC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAE,EAAE;EACRC,GAAG,EAAE;AACP,CAAC;AAEDlD,OAAO,CAACmD,OAAO,GAAGjD,IAAI,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}