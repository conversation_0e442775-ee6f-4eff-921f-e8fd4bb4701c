<template>
  <img 
    :src="iconSrc" 
    :alt="name"
    :class="['png-icon', customClass]"
    :style="iconStyle"
    v-on="$listeners"
  />
</template>

<script>
import { getIconPath } from '@/utils/iconMap'

export default {
  name: 'PngIcon',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: [String, Number],
      default: '1em'
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconSrc() {
      return getIconPath(this.name)
    },
    iconStyle() {
      const size = typeof this.size === 'number' ? `${this.size}px` : this.size
      return {
        width: size,
        height: size
      }
    }
  }
}
</script>

<style scoped>
.png-icon {
  vertical-align: -0.15em;
  display: inline-block;
  object-fit: contain;
}
</style>
