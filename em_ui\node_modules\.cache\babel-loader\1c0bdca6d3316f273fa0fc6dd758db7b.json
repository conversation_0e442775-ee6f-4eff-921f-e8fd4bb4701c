{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport screenfull from 'screenfull';\nexport default {\n  name: 'UserMenu',\n  inject: ['reload'],\n  data: function data() {\n    return {\n      isFullscreenEnable: screenfull.isEnabled,\n      isFullscreen: false\n    };\n  },\n  mounted: function mounted() {\n    if (screenfull.isEnabled) {\n      screenfull.on('change', this.fullscreenChange);\n    }\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (screenfull.isEnabled) {\n      screenfull.off('change', this.fullscreenChange);\n    }\n  },\n  methods: {\n    fullscreen: function fullscreen() {\n      screenfull.toggle();\n    },\n    fullscreenChange: function fullscreenChange() {\n      this.isFullscreen = screenfull.isFullscreen;\n    },\n    handleCommand: function handleCommand(command) {\n      var _this = this;\n      switch (command) {\n        case 'dashboard':\n          this.$router.push({\n            name: 'dashboard'\n          });\n          break;\n        case 'setting':\n          this.$router.push({\n            name: 'personalSetting'\n          });\n          break;\n        case 'logout':\n          this.$store.dispatch('user/logout').then(function () {\n            _this.$router.push({\n              name: 'login'\n            });\n          });\n          break;\n      }\n    },\n    pro: function pro() {\n      window.open('https://hooray.gitee.io/fantastic-admin/pro', 'top');\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AA6DA;AAEA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;MACAD;IACA;EACA;EACAE;IACAC;MACAH;IACA;IACAI;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;YACAX;UACA;UACA;QACA;UACA;YACAA;UACA;UACA;QACA;UACA;YACAY;cACAZ;YACA;UACA;UACA;MACA;IACA;IACAa;MACAC;IACA;EACA;AACA", "names": ["name", "inject", "data", "isFullscreenEnable", "isFullscreen", "mounted", "screenfull", "<PERSON><PERSON><PERSON><PERSON>", "methods", "fullscreen", "fullscreenChange", "handleCommand", "_this", "pro", "window"], "sourceRoot": "src/layout/components/UserMenu", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"user\">\n    <div class=\"tools\">\n      <el-tooltip\n        v-if=\"$store.state.settings.enableNavSearch\"\n        effect=\"dark\"\n        content=\"搜索页面\"\n        placement=\"bottom\"\n      >\n        <span class=\"item\" @click=\"$eventBus.$emit('global-search-toggle')\">\n          <svg-icon name=\"search\" />\n        </span>\n      </el-tooltip>\n      <el-tooltip\n        v-if=\"$store.state.settings.mode == 'pc' && isFullscreenEnable && $store.state.settings.enableFullscreen\"\n        effect=\"dark\"\n        content=\"全屏\"\n        placement=\"bottom\"\n      >\n        <span class=\"item\" @click=\"fullscreen\">\n          <svg-icon :name=\"isFullscreen ? 'fullscreen-exit' : 'fullscreen'\" />\n        </span>\n      </el-tooltip>\n      <el-tooltip\n        v-if=\"$store.state.settings.enablePageReload\"\n        effect=\"dark\"\n        content=\"刷新页面\"\n        placement=\"bottom\"\n      >\n        <span class=\"item\" @click=\"reload(2)\">\n          <svg-icon name=\"reload\" />\n        </span>\n      </el-tooltip>\n      <el-tooltip\n        v-if=\"$store.state.settings.enableThemeSetting\"\n        effect=\"dark\"\n        content=\"主题配置\"\n        placement=\"bottom\"\n      >\n        <span class=\"item\" @click=\"$eventBus.$emit('global-theme-toggle')\">\n          <svg-icon name=\"theme\" />\n        </span>\n      </el-tooltip>\n    </div>\n    <el-dropdown class=\"user-container\" @command=\"handleCommand\">\n      <div class=\"user-wrapper\">\n        <el-avatar size=\"medium\">\n          <i class=\"el-icon-user-solid\" />\n        </el-avatar>\n        {{ $store.state.user.account }}\n        <i class=\"el-icon-caret-bottom\" />\n      </div>\n      <el-dropdown-menu slot=\"dropdown\" class=\"user-dropdown\">\n        <el-dropdown-item v-if=\"$store.state.settings.enableDashboard\" command=\"dashboard\">控制台</el-dropdown-item>\n        <el-dropdown-item divided command=\"logout\">退出登录</el-dropdown-item>\n      </el-dropdown-menu>\n    </el-dropdown>\n  </div>\n</template>\n\n<script>\nimport screenfull from 'screenfull'\n\nexport default {\n  name: 'UserMenu',\n  inject: ['reload'],\n  data () {\n    return {\n      isFullscreenEnable: screenfull.isEnabled,\n      isFullscreen: false\n    }\n  },\n  mounted () {\n    if (screenfull.isEnabled) {\n      screenfull.on('change', this.fullscreenChange)\n    }\n  },\n  beforeDestroy () {\n    if (screenfull.isEnabled) {\n      screenfull.off('change', this.fullscreenChange)\n    }\n  },\n  methods: {\n    fullscreen () {\n      screenfull.toggle()\n    },\n    fullscreenChange () {\n      this.isFullscreen = screenfull.isFullscreen\n    },\n    handleCommand (command) {\n      switch (command) {\n        case 'dashboard':\n          this.$router.push({\n            name: 'dashboard'\n          })\n          break\n        case 'setting':\n          this.$router.push({\n            name: 'personalSetting'\n          })\n          break\n        case 'logout':\n          this.$store.dispatch('user/logout').then(() => {\n            this.$router.push({\n              name: 'login'\n            })\n          })\n          break\n      }\n    },\n    pro () {\n      window.open('https://hooray.gitee.io/fantastic-admin/pro', 'top')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.user {\n    display: flex;\n    align-items: center;\n    padding: 0 20px;\n    white-space: nowrap;\n}\n.tools {\n    margin-right: 20px;\n    .item {\n        margin-left: 5px;\n        padding: 6px 8px;\n        border-radius: 5px;\n        outline: none;\n        cursor: pointer;\n        transition: all 0.3s;\n    }\n    .item-pro {\n        display: inline-block;\n        animation: pro-text 3s ease-out infinite;\n        @keyframes pro-text {\n            0%,\n            20% {\n                transform: scale(1);\n            }\n            50%,\n            70% {\n                transform: scale(1.2) translateX(-5px) translateY(-2px);\n            }\n            100% {\n                transform: scale(1);\n            }\n        }\n        .title {\n            padding-left: 5px;\n            font-weight: bold;\n            font-size: 14px;\n            background-image: linear-gradient(to right, #ffa237, #fc455d);\n            background-clip: text;\n            -webkit-text-fill-color: transparent;\n        }\n    }\n}\n.user-container {\n    display: inline-block;\n    height: 50px;\n    line-height: 50px;\n    cursor: pointer;\n    .user-wrapper {\n        .el-avatar {\n            vertical-align: middle;\n            margin-top: -2px;\n            margin-right: 4px;\n        }\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}