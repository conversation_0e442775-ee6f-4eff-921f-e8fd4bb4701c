{"ast": null, "code": "import \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nexport default {\n  name: 'FileUpload',\n  props: {\n    action: {\n      type: String,\n      required: true\n    },\n    headers: {\n      type: Object,\n      default: function _default() {}\n    },\n    data: {\n      type: Object,\n      default: function _default() {}\n    },\n    name: {\n      type: String,\n      default: 'file'\n    },\n    size: {\n      type: Number,\n      default: 2\n    },\n    max: {\n      type: Number,\n      default: 3\n    },\n    files: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    notip: {\n      type: Boolean,\n      default: false\n    },\n    ext: {\n      type: Array,\n      default: function _default() {\n        return ['zip', 'rar'];\n      }\n    }\n  },\n  methods: {\n    beforeUpload: function beforeUpload(file) {\n      var fileName = file.name.split('.');\n      var fileExt = fileName[fileName.length - 1];\n      var isTypeOk = this.ext.indexOf(fileExt) >= 0;\n      var isSizeOk = file.size / 1024 / 1024 < this.size;\n      if (!isTypeOk) {\n        this.$message.error(\"\\u4E0A\\u4F20\\u6587\\u4EF6\\u53EA\\u652F\\u6301 \".concat(this.ext.join(' / '), \" \\u683C\\u5F0F\\uFF01\"));\n      }\n      if (!isSizeOk) {\n        this.$message.error(\"\\u4E0A\\u4F20\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u80FD\\u8D85\\u8FC7 \".concat(this.size, \"MB\\uFF01\"));\n      }\n      return isTypeOk && isSizeOk;\n    },\n    onExceed: function onExceed() {\n      this.$message.warning('文件上传超过限制');\n    },\n    onSuccess: function onSuccess(res, file) {\n      this.$emit('on-success', res, file);\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;AAyBA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;IACA;IACAN;MACAG;MACAG;IACA;IACAE;MACAL;MACAG;IACA;IACAG;MACAN;MACAG;IACA;IACAI;MACAP;MACAG;QAAA;MAAA;IACA;IACAK;MACAR;MACAG;IACA;IACAM;MACAT;MACAG;QAAA;MAAA;IACA;EACA;EACAO;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "props", "action", "type", "required", "headers", "default", "data", "size", "max", "files", "notip", "ext", "methods", "beforeUpload", "onExceed", "onSuccess"], "sourceRoot": "src/components/FileUpload", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <el-upload\n        :action=\"action\"\n        :data=\"data\"\n        :name=\"name\"\n        :before-upload=\"beforeUpload\"\n        :on-exceed=\"onExceed\"\n        :on-success=\"onSuccess\"\n        :file-list=\"files\"\n        :limit=\"max\"\n        drag\n    >\n        <div class=\"slot\">\n            <i class=\"el-icon-upload\" />\n            <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        </div>\n        <div v-if=\"!notip\" slot=\"tip\" class=\"el-upload__tip\">\n            <div style=\"display: inline-block;\">\n                <el-alert :title=\"`上传文件支持 ${ ext.join(' / ') } 格式，单个文件大小不超过 ${ size }MB，且文件数量不超过 ${ max } 个`\" type=\"info\" show-icon :closable=\"false\" />\n            </div>\n        </div>\n    </el-upload>\n</template>\n\n<script>\nexport default {\n    name: 'FileUpload',\n    props: {\n        action: {\n            type: String,\n            required: true\n        },\n        headers: {\n            type: Object,\n            default: () => {}\n        },\n        data: {\n            type: Object,\n            default: () => {}\n        },\n        name: {\n            type: String,\n            default: 'file'\n        },\n        size: {\n            type: Number,\n            default: 2\n        },\n        max: {\n            type: Number,\n            default: 3\n        },\n        files: {\n            type: Array,\n            default: () => []\n        },\n        notip: {\n            type: Boolean,\n            default: false\n        },\n        ext: {\n            type: Array,\n            default: () => ['zip', 'rar']\n        }\n    },\n    methods: {\n        beforeUpload(file) {\n            const fileName = file.name.split('.')\n            const fileExt = fileName[fileName.length - 1]\n            const isTypeOk = this.ext.indexOf(fileExt) >= 0\n            const isSizeOk = file.size / 1024 / 1024 < this.size\n            if (!isTypeOk) {\n                this.$message.error(`上传文件只支持 ${ this.ext.join(' / ') } 格式！`)\n            }\n            if (!isSizeOk) {\n                this.$message.error(`上传文件大小不能超过 ${this.size}MB！`)\n            }\n            return isTypeOk && isSizeOk\n        },\n        onExceed() {\n            this.$message.warning('文件上传超过限制')\n        },\n        onSuccess(res, file) {\n            this.$emit('on-success', res, file)\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-upload-dragger {\n    width: auto;\n    height: auto;\n    overflow: unset;\n    &.is-dragover {\n        border-width: 1px;\n    }\n}\n.slot {\n    width: 300px;\n    height: 160px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}