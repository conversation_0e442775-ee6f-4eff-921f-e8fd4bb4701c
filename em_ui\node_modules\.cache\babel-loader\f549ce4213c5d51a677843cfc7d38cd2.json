{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nexport var download = function download(name, data) {\n  var export_blob = new Blob([data]);\n  var urlObject = window.URL || window.webkitURL || window;\n  var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');\n  save_link.href = urlObject.createObjectURL(export_blob);\n  save_link.download = name;\n  save_link.click();\n};", "map": {"version": 3, "names": ["download", "name", "data", "export_blob", "Blob", "urlObject", "window", "URL", "webkitURL", "save_link", "document", "createElementNS", "href", "createObjectURL", "click"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/util/download.js"], "sourcesContent": ["export let download = (name, data) => {\n  var export_blob = new Blob([data]);\n  var urlObject = window.URL || window.webkitURL || window;\n  var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a')\n  save_link.href = urlObject.createObjectURL(export_blob);\n  save_link.download = name;\n  save_link.click();\n}"], "mappings": ";;;;;;;;;AAAA,OAAO,IAAIA,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,IAAI,EAAEC,IAAI,EAAK;EACpC,IAAIC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC;EAClC,IAAIG,SAAS,GAAGC,MAAM,CAACC,GAAG,IAAID,MAAM,CAACE,SAAS,IAAIF,MAAM;EACxD,IAAIG,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAAC,8BAA8B,EAAE,GAAG,CAAC;EAC7EF,SAAS,CAACG,IAAI,GAAGP,SAAS,CAACQ,eAAe,CAACV,WAAW,CAAC;EACvDM,SAAS,CAACT,QAAQ,GAAGC,IAAI;EACzBQ,SAAS,CAACK,KAAK,CAAC,CAAC;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}