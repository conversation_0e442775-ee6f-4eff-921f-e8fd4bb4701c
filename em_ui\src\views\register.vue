<template>
  <div class="register-container">
    <div class="bg-banner" />
    <div class="register-box">
      <div class="register-form-container">
        <div class="title-container">
          <h3 class="title">用户注册</h3>
          <p class="subtitle">创建您的账户</p>
        </div>

        <el-form
          ref="registerForm"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
          auto-complete="off"
          label-position="left"
        >
          <el-form-item prop="userName">
            <el-input
              v-model="registerForm.userName"
              placeholder="请输入用户名"
              prefix-icon="el-icon-user"
              size="large"
              class="register-input"
            />
          </el-form-item>

          <el-form-item prop="fullName">
            <el-input
              v-model="registerForm.fullName"
              placeholder="请输入真实姓名"
              prefix-icon="el-icon-user-solid"
              size="large"
              class="register-input"
            />
          </el-form-item>

          <el-form-item prop="phone">
            <el-input
              v-model="registerForm.phone"
              placeholder="请输入手机号"
              prefix-icon="el-icon-phone"
              size="large"
              class="register-input"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="el-icon-lock"
              size="large"
              class="register-input"
              show-password
            />
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请确认密码"
              prefix-icon="el-icon-lock"
              size="large"
              class="register-input"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="register-button"
              :loading="registerLoading"
              @click="handleRegister"
            >
              {{ registerLoading ? '注册中...' : '注册' }}
            </el-button>
          </el-form-item>

          <div class="login-link">
            <p>已有账户？<router-link to="/home">立即登录</router-link></p>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { registerUser } from '@/api/requests/rq-manage.js'

export default {
  name: 'Register',
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    return {
      registerForm: {
        userName: '',
        fullName: '',
        phone: '',
        password: '',
        confirmPassword: ''
      },
      registerRules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度为3到20位', trigger: 'blur' }
        ],
        fullName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' },
          { min: 2, max: 10, message: '姓名长度为2到10位', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度为6到20位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      registerLoading: false
    }
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.registerLoading = true
          
          const userData = {
            userName: this.registerForm.userName,
            fullName: this.registerForm.fullName,
            phone: this.registerForm.phone,
            password: this.registerForm.password,
            status: '0' // 正常状态
          }

          registerUser(userData).then(res => {
            this.registerLoading = false
            if (res.code === 200) {
              this.$success({
                title: '注册成功',
                content: (
                  <div>
                    <p>账户注册成功！</p>
                    <p>即将跳转到登录页面...</p>
                  </div>
                )
              })
              
              setTimeout(() => {
                this.$router.push('/home')
              }, 2000)
            } else {
              this.$error({
                title: '注册失败',
                content: res.msg || '注册失败，请重试'
              })
            }
          }).catch(error => {
            this.registerLoading = false
            this.$error({
              title: '注册失败',
              content: '网络错误，请重试'
            })
            console.error('注册错误:', error)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.bg-banner {
  position: absolute;
  z-index: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-image: url(../assets/images/login-bg.jpg);
}

.register-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  z-index: 1;
}

.register-form-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.title-container {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 28px;
  color: #333;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.subtitle {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.register-form {
  .register-input {
    margin-bottom: 20px;
    
    ::v-deep .el-input__inner {
      height: 50px;
      line-height: 50px;
      border-radius: 25px;
      border: 2px solid #e4e7ed;
      padding-left: 50px;
      font-size: 16px;
      transition: all 0.3s;
      
      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
      }
    }
    
    ::v-deep .el-input__prefix {
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
    }
    
    ::v-deep .el-input__suffix {
      right: 15px;
    }
  }
}

.register-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  font-size: 18px;
  font-weight: bold;
  background: linear-gradient(45deg, #409eff, #67c23a);
  border: none;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(64, 158, 255, 0.4);
  }
}

.login-link {
  text-align: center;
  margin-top: 20px;
  
  p {
    color: #666;
    margin: 0;
    
    a {
      color: #409eff;
      text-decoration: none;
      font-weight: bold;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .register-box {
    width: 90%;
    max-width: 350px;
  }
  
  .register-form-container {
    padding: 30px 20px;
  }
}
</style>
