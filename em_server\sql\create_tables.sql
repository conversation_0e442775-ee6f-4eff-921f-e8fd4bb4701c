-- 小区物业管理系统 - 数据库表结构创建脚本
-- 如果主SQL文件有问题，可以使用此文件创建基础表结构

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE IF NOT EXISTS `sys_user` (
  `id` varchar(50) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `full_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_name` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `sys_user_role` (
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `sys_options` (
  `id` varchar(50) NOT NULL COMMENT '配置ID',
  `text` text COMMENT '配置内容',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 楼宇表
CREATE TABLE IF NOT EXISTS `sys_building` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '楼宇ID',
  `building_name` varchar(100) NOT NULL COMMENT '楼宇名称',
  `building_num` varchar(50) DEFAULT NULL COMMENT '楼宇编号',
  `floors` int(11) DEFAULT NULL COMMENT '楼层数',
  `units` int(11) DEFAULT NULL COMMENT '单元数',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='楼宇表';

-- 房间表
CREATE TABLE IF NOT EXISTS `sys_room` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `building_id` int(11) NOT NULL COMMENT '楼宇ID',
  `room_num` varchar(50) NOT NULL COMMENT '房间号',
  `floor` int(11) DEFAULT NULL COMMENT '楼层',
  `unit` varchar(10) DEFAULT NULL COMMENT '单元',
  `area` decimal(10,2) DEFAULT NULL COMMENT '面积',
  `room_type` varchar(50) DEFAULT NULL COMMENT '房间类型',
  `status` char(1) DEFAULT '0' COMMENT '状态(0空闲 1已入住)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_building_id` (`building_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间表';

-- 报修表
CREATE TABLE IF NOT EXISTS `sys_repair` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '报修ID',
  `title` varchar(200) NOT NULL COMMENT '报修标题',
  `content` text COMMENT '报修内容',
  `user_name` varchar(100) DEFAULT NULL COMMENT '报修人',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `address` varchar(255) DEFAULT NULL COMMENT '报修地址',
  `status` varchar(20) DEFAULT '待处理' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报修表';

-- 投诉表
CREATE TABLE IF NOT EXISTS `sys_complaint` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '投诉ID',
  `title` varchar(200) NOT NULL COMMENT '投诉标题',
  `content` text COMMENT '投诉内容',
  `user_name` varchar(100) DEFAULT NULL COMMENT '投诉人',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` varchar(20) DEFAULT '待处理' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='投诉表';

-- 公告表
CREATE TABLE IF NOT EXISTS `sys_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(200) NOT NULL COMMENT '公告标题',
  `content` text COMMENT '公告内容',
  `status` char(1) DEFAULT '1' COMMENT '状态(0停用 1启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

-- 设施表
CREATE TABLE IF NOT EXISTS `sys_facilities` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设施ID',
  `name` varchar(100) NOT NULL COMMENT '设施名称',
  `type` varchar(50) DEFAULT NULL COMMENT '设施类型',
  `location` varchar(255) DEFAULT NULL COMMENT '位置',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1维修中)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设施表';

-- 收费类型表
CREATE TABLE IF NOT EXISTS `sys_charge_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收费类型ID',
  `name` varchar(100) NOT NULL COMMENT '收费类型名称',
  `price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `status` char(1) DEFAULT '1' COMMENT '状态(0停用 1启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收费类型表';

SET FOREIGN_KEY_CHECKS = 1;
