{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.includes.js\";\n/**\n * PNG图标映射配置\n * 用于管理项目中的PNG图标资源\n */\n\n// 可用的PNG图标列表\nexport var availableIcons = ['404', 'building', 'complaint', 'dashboard', 'facilities', 'home', 'notice', 'payment', 'repair'];\n\n// 图标别名映射\nexport var iconAliases = {\n  // 建筑相关\n  'office-building': 'building',\n  'house': 'home',\n  'room': 'home',\n  // 功能相关\n  'tools': 'repair',\n  'wrench': 'repair',\n  'maintenance': 'repair',\n  // 通知相关\n  'bell': 'notice',\n  'announcement': 'notice',\n  'info': 'notice',\n  // 投诉相关\n  'feedback': 'complaint',\n  'report': 'complaint',\n  // 支付相关\n  'money': 'payment',\n  'credit-card': 'payment',\n  'wallet': 'payment',\n  // 设施相关\n  'star': 'facilities',\n  'service': 'facilities',\n  // 仪表盘相关\n  'chart': 'dashboard',\n  'analytics': 'dashboard',\n  'data': 'dashboard'\n};\n\n/**\n * 获取图标路径\n * @param {string} iconName 图标名称\n * @returns {string} 图标路径\n */\nexport function getIconPath(iconName) {\n  // 检查别名\n  var actualName = iconAliases[iconName] || iconName;\n\n  // 检查图标是否存在\n  if (availableIcons.includes(actualName)) {\n    try {\n      return require(\"@/assets/icons/\".concat(actualName, \".png\"));\n    } catch (error) {\n      console.warn(\"Icon \".concat(actualName, \".png not found\"));\n      return require('@/assets/icons/404.png');\n    }\n  }\n\n  // 返回404图标作为后备\n  return require('@/assets/icons/404.png');\n}\n\n/**\n * 检查图标是否存在\n * @param {string} iconName 图标名称\n * @returns {boolean} 是否存在\n */\nexport function hasIcon(iconName) {\n  var actualName = iconAliases[iconName] || iconName;\n  return availableIcons.includes(actualName);\n}\n\n/**\n * 获取所有可用图标\n * @returns {Array} 图标列表\n */\nexport function getAllIcons() {\n  return [].concat(availableIcons);\n}\nexport default {\n  availableIcons: availableIcons,\n  iconAliases: iconAliases,\n  getIconPath: getIconPath,\n  hasIcon: hasIcon,\n  getAllIcons: getAllIcons\n};", "map": {"version": 3, "names": ["availableIcons", "iconAliases", "getIconPath", "iconName", "actualName", "includes", "require", "concat", "error", "console", "warn", "hasIcon", "getAllIcons"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/utils/iconMap.js"], "sourcesContent": ["/**\n * PNG图标映射配置\n * 用于管理项目中的PNG图标资源\n */\n\n// 可用的PNG图标列表\nexport const availableIcons = [\n  '404',\n  'building',\n  'complaint',\n  'dashboard',\n  'facilities',\n  'home',\n  'notice',\n  'payment',\n  'repair'\n]\n\n// 图标别名映射\nexport const iconAliases = {\n  // 建筑相关\n  'office-building': 'building',\n  'house': 'home',\n  'room': 'home',\n  \n  // 功能相关\n  'tools': 'repair',\n  'wrench': 'repair',\n  'maintenance': 'repair',\n  \n  // 通知相关\n  'bell': 'notice',\n  'announcement': 'notice',\n  'info': 'notice',\n  \n  // 投诉相关\n  'feedback': 'complaint',\n  'report': 'complaint',\n  \n  // 支付相关\n  'money': 'payment',\n  'credit-card': 'payment',\n  'wallet': 'payment',\n  \n  // 设施相关\n  'star': 'facilities',\n  'service': 'facilities',\n  \n  // 仪表盘相关\n  'chart': 'dashboard',\n  'analytics': 'dashboard',\n  'data': 'dashboard'\n}\n\n/**\n * 获取图标路径\n * @param {string} iconName 图标名称\n * @returns {string} 图标路径\n */\nexport function getIconPath(iconName) {\n  // 检查别名\n  const actualName = iconAliases[iconName] || iconName\n  \n  // 检查图标是否存在\n  if (availableIcons.includes(actualName)) {\n    try {\n      return require(`@/assets/icons/${actualName}.png`)\n    } catch (error) {\n      console.warn(`Icon ${actualName}.png not found`)\n      return require('@/assets/icons/404.png')\n    }\n  }\n  \n  // 返回404图标作为后备\n  return require('@/assets/icons/404.png')\n}\n\n/**\n * 检查图标是否存在\n * @param {string} iconName 图标名称\n * @returns {boolean} 是否存在\n */\nexport function hasIcon(iconName) {\n  const actualName = iconAliases[iconName] || iconName\n  return availableIcons.includes(actualName)\n}\n\n/**\n * 获取所有可用图标\n * @returns {Array} 图标列表\n */\nexport function getAllIcons() {\n  return [...availableIcons]\n}\n\nexport default {\n  availableIcons,\n  iconAliases,\n  getIconPath,\n  hasIcon,\n  getAllIcons\n}\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,IAAMA,cAAc,GAAG,CAC5B,KAAK,EACL,UAAU,EACV,WAAW,EACX,WAAW,EACX,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,SAAS,EACT,QAAQ,CACT;;AAED;AACA,OAAO,IAAMC,WAAW,GAAG;EACzB;EACA,iBAAiB,EAAE,UAAU;EAC7B,OAAO,EAAE,MAAM;EACf,MAAM,EAAE,MAAM;EAEd;EACA,OAAO,EAAE,QAAQ;EACjB,QAAQ,EAAE,QAAQ;EAClB,aAAa,EAAE,QAAQ;EAEvB;EACA,MAAM,EAAE,QAAQ;EAChB,cAAc,EAAE,QAAQ;EACxB,MAAM,EAAE,QAAQ;EAEhB;EACA,UAAU,EAAE,WAAW;EACvB,QAAQ,EAAE,WAAW;EAErB;EACA,OAAO,EAAE,SAAS;EAClB,aAAa,EAAE,SAAS;EACxB,QAAQ,EAAE,SAAS;EAEnB;EACA,MAAM,EAAE,YAAY;EACpB,SAAS,EAAE,YAAY;EAEvB;EACA,OAAO,EAAE,WAAW;EACpB,WAAW,EAAE,WAAW;EACxB,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,QAAQ,EAAE;EACpC;EACA,IAAMC,UAAU,GAAGH,WAAW,CAACE,QAAQ,CAAC,IAAIA,QAAQ;;EAEpD;EACA,IAAIH,cAAc,CAACK,QAAQ,CAACD,UAAU,CAAC,EAAE;IACvC,IAAI;MACF,OAAOE,OAAO,mBAAAC,MAAA,CAAmBH,UAAU,SAAM,CAAC;IACpD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,SAAAH,MAAA,CAASH,UAAU,mBAAgB,CAAC;MAChD,OAAOE,OAAO,CAAC,wBAAwB,CAAC;IAC1C;EACF;;EAEA;EACA,OAAOA,OAAO,CAAC,wBAAwB,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,OAAOA,CAACR,QAAQ,EAAE;EAChC,IAAMC,UAAU,GAAGH,WAAW,CAACE,QAAQ,CAAC,IAAIA,QAAQ;EACpD,OAAOH,cAAc,CAACK,QAAQ,CAACD,UAAU,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,WAAWA,CAAA,EAAG;EAC5B,UAAAL,MAAA,CAAWP,cAAc;AAC3B;AAEA,eAAe;EACbA,cAAc,EAAdA,cAAc;EACdC,WAAW,EAAXA,WAAW;EACXC,WAAW,EAAXA,WAAW;EACXS,OAAO,EAAPA,OAAO;EACPC,WAAW,EAAXA;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}