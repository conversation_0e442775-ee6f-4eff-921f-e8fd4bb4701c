{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getRepair, examineRepair, deleteRepair, getRepairByUserId } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      isUser: true,\n      loading: false,\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      },\n      table_selectedRowKeys: [],\n      repair_query_type: 'title',\n      repair_query_buttonTitle: '搜索',\n      repair_query_text: '',\n      repair_save_title: '报修申请单审核',\n      repair_save_modalVisible: false,\n      repair_see_modalVisible: false,\n      repair_form_data: {},\n      repair_data_list: []\n    };\n  },\n  created: function created() {\n    this.isUser = this.$route.path.indexOf('user') != -1;\n    this.Get_repairDataList();\n  },\n  watch: {\n    repair_save_modalVisible: function repair_save_modalVisible(val) {\n      if (!val) {\n        this.repair_form_data = {};\n      }\n    }\n  },\n  methods: {\n    Get_repairDataList: function Get_repairDataList() {\n      var _this = this;\n      var method = getRepairByUserId();\n      if (localStorage.getItem('isAdmin') == 'true') {\n        method = getRepair();\n      }\n      method.then(function (res) {\n        _this.repair_query_buttonTitle = '搜索';\n        _this.repair_data_list = res.data;\n      });\n    },\n    Query_repairDataList: function Query_repairDataList() {\n      var _this2 = this;\n      var text = this.repair_query_text;\n      var temp_list = [];\n      this.repair_data_list.forEach(function (item) {\n        if (item[_this2.repair_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.repair_query_buttonTitle = '返回';\n      this.repair_data_list = temp_list;\n    },\n    Edit_repairData: function Edit_repairData(form) {\n      this.repair_form_data = JSON.parse(JSON.stringify(form));\n      this.repair_save_modalVisible = true;\n    },\n    Del_repairData: function Del_repairData(id) {\n      var _this3 = this;\n      deleteRepair(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_repairDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_repairData(_this4.repair_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_repairData: function Save_repairData() {\n      var _this5 = this;\n      examineRepair(this.repair_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.repair_save_modalVisible = false;\n        _this5.Get_repairDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    See_repairDateModal: function See_repairDateModal(form) {\n      this.repair_form_data = JSON.parse(JSON.stringify(form));\n      this.repair_see_modalVisible = true;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AA0KA;AACA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAL;MACA;QACA;MACA;IACA;EACA;EACAM;IACAC;MAAA;MACA;MACA;QACAC;MACA;MACAA;QACAC;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA", "names": ["data", "isUser", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "repair_query_type", "repair_query_buttonTitle", "repair_query_text", "repair_save_title", "repair_save_modalVisible", "repair_see_modalVisible", "repair_form_data", "repair_data_list", "created", "watch", "methods", "Get_repairDataList", "method", "_this", "Query_repairDataList", "temp_list", "Edit_repairData", "Del_repairData", "deleteRepair", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_repairData", "examineRepair", "_this5", "Table_selectChange", "See_repairDateModal"], "sourceRoot": "src/views/admin/rq/guarantee", "sources": ["rq_repair_manager.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"报修管理\">\n      <div class=\"head\" v-if=\"!isUser\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"申请原因\"\n          v-model=\"repair_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"title\">申请原因</a-select-option>\n          <a-select-option value=\"userName\">用户名称</a-select-option>\n          <a-select-option value=\"address\">用户住址</a-select-option>\n          <a-select-option value=\"phone\">电话</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"repair_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"repair_query_buttonTitle\"\n          size=\"large\"\n          @search=\"repair_query_buttonTitle == '搜索' ? Query_repairDataList() : Get_repairDataList()\"\n        />\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「报修申请单」</a-button>\n      </div>\n      <a-table\n        :data-source=\"repair_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"id\" title=\"申请编号\" data-index=\"id\" />\n        <a-table-column key=\"date\" title=\"申请时间\" data-index=\"date\">\n          <!-- rTime -->\n          <template slot-scope=\"text, record\">\n            <span>{{String(record.date).substr(0,10)}}</span>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"userName\" title=\"申请人\" data-index=\"userName\" />\n        <a-table-column key=\"title\" title=\"申请原因\" data-index=\"title\" />\n        <a-table-column key=\"phone\" title=\"联系方式\" data-index=\"phone\" />\n        <a-table-column key=\"address\" title=\"用户住址\" data-index=\"address\" />\n        <a-table-column key=\"isExamine\" title=\"是否处理\" data-index=\"isExamine\">\n          <template slot-scope=\"text, record\">\n                 <a-tooltip placement=\"top\">\n        <template slot=\"title\">\n          <span>点我查看详情</span>\n        </template>\n        <div @click=\"See_repairDateModal(record)\">\n                          <a-tag :color=\"record.isExamine == 1 ? 'red' : 'blue'\" >\n              {{\n              record.isExamine == 1 ? '已处理' : '未处理'\n              }}\n            </a-tag>\n            </div>\n\n      </a-tooltip>\n            \n          </template>\n        </a-table-column>\n        <a-table-column v-if=\"!isUser\" key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button\n                :disabled=\"record.isExamine == 1\"\n                type=\"primary\"\n                @click=\"Edit_repairData(record)\"\n              >审核</a-button>\n              <a-button type=\"danger\" @click=\"Del_repairData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"repair_save_modalVisible\"\n      :title=\"repair_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_repairData\"\n    >\n      <a-form-model :model=\"repair_form_data\" :label-col=\"labelCol\" :wrapper-col=\"wrapperCol\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"申请人\">\n              <span>{{repair_form_data.userName}}</span>\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"联系方式\">\n              <span>{{repair_form_data.phone}}</span>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"保修单标题\">\n              <span>{{repair_form_data.title}}</span>\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"用户住址\">\n              <span>{{repair_form_data.address}}</span>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <div>\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">报修详情:</span>\n          <a-input\n            v-model=\"repair_form_data.text\"\n            type=\"textarea\"\n            disabled\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n        <div>\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">审核报修:</span>\n          <a-input\n            v-model=\"repair_form_data.examineData\"\n            type=\"textarea\"\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n      </a-form-model>\n    </a-modal>\n\n\n    <a-modal\n      v-model=\"repair_see_modalVisible\"\n      title=\"详细信息\"\n      ok-text=\"确认\"\n      @ok=\"repair_see_modalVisible = false\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n    >\n        <div style=\"margin-top: 10px;\">\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">报修详情:</span>\n          <a-input\n            v-model=\"repair_form_data.text\"\n            type=\"textarea\"\n            disabled\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n        <div style=\"margin-top: 10px;\">\n          <span style=\"color: rgba(0, 0, 0, 0.85);\">审核报修:</span>\n          <a-input\n            v-model=\"repair_form_data.examineData\"\n            type=\"textarea\"\n            disabled\n            :rows=\"5\"\n            style=\"margin-top: 10px;\"\n          />\n        </div>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getRepair, examineRepair ,deleteRepair,getRepairByUserId} from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\n\nexport default {\n  data () {\n    return {\n      isUser:true,\n      loading: false,\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n      table_selectedRowKeys: [],\n      repair_query_type: 'title',\n      repair_query_buttonTitle: '搜索',\n      repair_query_text: '',\n      repair_save_title: '报修申请单审核',\n      repair_save_modalVisible: false,\n      repair_see_modalVisible:false,\n      repair_form_data: {},\n      repair_data_list: [],\n    }\n  },\n  created () {\n    this.isUser = this.$route.path.indexOf('user') != -1\n    this.Get_repairDataList()\n  },\n  watch: {\n    repair_save_modalVisible (val) {\n      if (!val) {\n        this.repair_form_data = {}\n      }\n    }\n  },\n  methods: {\n    Get_repairDataList () {\n      let method = getRepairByUserId()\n      if(localStorage.getItem('isAdmin') == 'true'){\n        method = getRepair()\n      }\n      method.then(res => {\n        this.repair_query_buttonTitle = '搜索'\n        this.repair_data_list = res.data\n      })\n    },\n    Query_repairDataList () {\n      let text = this.repair_query_text\n      let temp_list = []\n      this.repair_data_list.forEach(item => {\n        if (item[this.repair_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.repair_query_buttonTitle = '返回'\n      this.repair_data_list = temp_list\n    },\n    Edit_repairData (form) {\n      this.repair_form_data = JSON.parse(JSON.stringify(form))\n      this.repair_save_modalVisible = true\n    },\n    Del_repairData (id) {\n      deleteRepair(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_repairDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_repairData(this.repair_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    Save_repairData () {\n      examineRepair(this.repair_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.repair_save_modalVisible = false\n        this.Get_repairDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    See_repairDateModal(form){\n      this.repair_form_data = JSON.parse(JSON.stringify(form))\n      this.repair_see_modalVisible = true\n    }\n  },\n}\n</script>\n    \n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n.ant-form-item-children span {\n    margin-left: 10px;\n}\n</style>"]}, "metadata": {}, "sourceType": "module"}