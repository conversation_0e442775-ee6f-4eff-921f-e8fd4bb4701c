/**
 * PNG图标映射配置
 * 用于管理项目中的PNG图标资源
 */

// 可用的PNG图标列表
export const availableIcons = [
  '404',
  'building',
  'complaint',
  'dashboard',
  'facilities',
  'home',
  'notice',
  'payment',
  'repair'
]

// 图标别名映射
export const iconAliases = {
  // 建筑相关
  'office-building': 'building',
  'house': 'home',
  'room': 'home',
  
  // 功能相关
  'tools': 'repair',
  'wrench': 'repair',
  'maintenance': 'repair',
  
  // 通知相关
  'bell': 'notice',
  'announcement': 'notice',
  'info': 'notice',
  
  // 投诉相关
  'feedback': 'complaint',
  'report': 'complaint',
  
  // 支付相关
  'money': 'payment',
  'credit-card': 'payment',
  'wallet': 'payment',
  
  // 设施相关
  'star': 'facilities',
  'service': 'facilities',
  
  // 仪表盘相关
  'chart': 'dashboard',
  'analytics': 'dashboard',
  'data': 'dashboard'
}

/**
 * 获取图标路径
 * @param {string} iconName 图标名称
 * @returns {string} 图标路径
 */
export function getIconPath(iconName) {
  // 检查别名
  const actualName = iconAliases[iconName] || iconName
  
  // 检查图标是否存在
  if (availableIcons.includes(actualName)) {
    try {
      return require(`@/assets/icons/${actualName}.png`)
    } catch (error) {
      console.warn(`Icon ${actualName}.png not found`)
      return require('@/assets/icons/404.png')
    }
  }
  
  // 返回404图标作为后备
  return require('@/assets/icons/404.png')
}

/**
 * 检查图标是否存在
 * @param {string} iconName 图标名称
 * @returns {boolean} 是否存在
 */
export function hasIcon(iconName) {
  const actualName = iconAliases[iconName] || iconName
  return availableIcons.includes(actualName)
}

/**
 * 获取所有可用图标
 * @returns {Array} 图标列表
 */
export function getAllIcons() {
  return [...availableIcons]
}

export default {
  availableIcons,
  iconAliases,
  getIconPath,
  hasIcon,
  getAllIcons
}
