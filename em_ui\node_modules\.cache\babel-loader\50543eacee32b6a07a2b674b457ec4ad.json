{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"transition\", {\n    attrs: {\n      name: \"notice\"\n    }\n  }, [_vm.show ? _c(\"div\", {\n    staticClass: \"notice\"\n  }, [_vm._v(\" \" + _vm._s(_vm.content) + \" \")]) : _vm._e()]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "name", "show", "staticClass", "_v", "_s", "content", "_e", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/ExampleNotice/main.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"transition\", { attrs: { name: \"notice\" } }, [\n    _vm.show\n      ? _c(\"div\", { staticClass: \"notice\" }, [\n          _vm._v(\" \" + _vm._s(_vm.content) + \" \"),\n        ])\n      : _vm._e(),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,YAAY,EAAE;IAAEE,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAS;EAAE,CAAC,EAAE,CACrDJ,GAAG,CAACK,IAAI,GACJJ,EAAE,CAAC,KAAK,EAAE;IAAEK,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCN,GAAG,CAACO,EAAE,CAAC,GAAG,GAAGP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACS,OAAO,CAAC,GAAG,GAAG,CAAC,CACxC,CAAC,GACFT,GAAG,CAACU,EAAE,CAAC,CAAC,CACb,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}