{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport { getIconPath } from '@/utils/iconMap';\nexport default {\n  name: 'PngIcon',\n  props: {\n    name: {\n      type: String,\n      required: true\n    },\n    size: {\n      type: [String, Number],\n      default: '1em'\n    },\n    customClass: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    iconSrc: function iconSrc() {\n      return getIconPath(this.name);\n    },\n    iconStyle: function iconStyle() {\n      var size = typeof this.size === 'number' ? \"\".concat(this.size, \"px\") : this.size;\n      return {\n        width: size,\n        height: size\n      };\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;AAWA;AAEA;EACAA;EACAC;IACAD;MACAE;MACAC;IACA;IACAC;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;IACA;EACA;EACAE;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;QACAC;MACA;IACA;EACA;AACA", "names": ["name", "props", "type", "required", "size", "default", "customClass", "computed", "iconSrc", "iconStyle", "width", "height"], "sourceRoot": "src/components/PngIcon", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <img \n    :src=\"iconSrc\" \n    :alt=\"name\"\n    :class=\"['png-icon', customClass]\"\n    :style=\"iconStyle\"\n    v-on=\"$listeners\"\n  />\n</template>\n\n<script>\nimport { getIconPath } from '@/utils/iconMap'\n\nexport default {\n  name: 'PngIcon',\n  props: {\n    name: {\n      type: String,\n      required: true\n    },\n    size: {\n      type: [String, Number],\n      default: '1em'\n    },\n    customClass: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    iconSrc() {\n      return getIconPath(this.name)\n    },\n    iconStyle() {\n      const size = typeof this.size === 'number' ? `${this.size}px` : this.size\n      return {\n        width: size,\n        height: size\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.png-icon {\n  vertical-align: -0.15em;\n  display: inline-block;\n  object-fit: contain;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}