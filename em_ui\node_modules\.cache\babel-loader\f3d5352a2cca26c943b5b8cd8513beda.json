{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableRest as default };", "map": {"version": 3, "names": ["_nonIterableRest", "TypeError", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/@babel/runtime/helpers/esm/nonIterableRest.js"], "sourcesContent": ["function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableRest as default };"], "mappings": ";AAAA,SAASA,gBAAgBA,CAAA,EAAG;EAC1B,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAClK;AACA,SAASD,gBAAgB,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}