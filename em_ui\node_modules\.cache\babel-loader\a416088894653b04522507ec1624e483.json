{"ast": null, "code": "import { addRepair } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nexport default {\n  data: function data() {\n    return {\n      current_form: {},\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      },\n      rules: {\n        userName: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        address: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        title: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        date1: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'change'\n        }],\n        text: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.current_form.date = new Date();\n  },\n  methods: {\n    Save_Repair: function Save_Repair() {\n      var _this = this;\n      this.$refs.current_form.validate(function (v) {\n        if (v) {\n          addRepair(_this.current_form).then(function (res) {\n            if (res.code == 200) {\n              Success(_this, '操作成功！');\n            }\n          });\n        }\n      });\n    },\n    Form_date_changeHandler: function Form_date_changeHandler(date) {\n      this.current_form.date = date._d;\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAkDA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;QACAC;UAAAC;UAAAC;UAAAC;QAAA;QACAC;UAAAH;UAAAC;UAAAC;QAAA;QACAE;UAAAJ;UAAAC;UAAAC;QAAA;QACAG;UAAAL;UAAAC;UAAAC;QAAA;QACAI;UAAAN;UAAAC;UAAAC;QAAA;QACAK;UAAAP;UAAAC;UAAAC;QAAA;MACA;IACA;EACA;EACAM;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACAC;YACA;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA", "names": ["data", "current_form", "labelCol", "span", "wrapperCol", "rules", "userName", "required", "message", "trigger", "phone", "address", "title", "date1", "text", "created", "methods", "Save_Repair", "addRepair", "Success", "Form_date_changeHandler"], "sourceRoot": "src/views/admin/rq/guarantee", "sources": ["rq_repair_add.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card title=\"申请投诉\">\n      <a-form-model\n        ref=\"current_form\"\n        :rules=\"rules\"\n        :model=\"current_form\"\n        style=\"width: 80vh;\"\n        :label-col=\"labelCol\"\n        :wrapper-col=\"wrapperCol\"\n      >\n        <a-form-model-item label=\"姓名\" prop=\"userName\">\n          <a-input v-model=\"current_form.userName\" placeholder=\"您的姓名\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"联系电话\" prop=\"phone\">\n          <a-input v-model=\"current_form.phone\" placeholder=\"您的联系方式\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"住址\" prop=\"address\">\n          <a-input v-model=\"current_form.address\" placeholder=\"楼宇名-单元号-房间号\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"报修内容\" prop=\"title\">\n          <a-input v-model=\"current_form.title\" placeholder=\"损坏事物名称\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"损坏时间\" prop=\"date1\">\n          <a-date-picker\n            v-model=\"current_form.date1\"\n            @change=\"Form_date_changeHandler\"\n            placeholder=\"选择时间\"\n          />\n        </a-form-model-item>\n\n        <a-form-model-item label=\"详细说明\" prop=\"text\">\n          <a-input\n            v-model=\"current_form.text\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"详细说明损坏的原因，便于维修人员施工。\"\n          />\n        </a-form-model-item>\n\n        <a-form-model-item style=\"margin-left: 33.3%;\">\n          <a-button type=\"primary\" @click=\"Save_Repair\">保存</a-button>\n          <a-button style=\"margin-left: 10px;\">取消</a-button>\n        </a-form-model-item>\n      </a-form-model>\n    </a-card>\n  </page-main>\n</template>\n\n<script>\nimport { addRepair } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\n\nexport default {\n  data () {\n    return {\n      current_form: {},\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n      rules: {\n        userName: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        phone: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        address: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        title: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        date1: [{ required: true, message: '此项为必填项', trigger: 'change' }],\n        text: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n      },\n    }\n  },\n  created () {\n    this.current_form.date = new Date()\n  },\n  methods: {\n    Save_Repair () {\n      this.$refs.current_form.validate(v => {\n        if (v) {\n          addRepair(this.current_form).then(res => {\n            if (res.code == 200) {\n              Success(this, '操作成功！')\n            }\n          })\n        }\n      })\n    },\n    Form_date_changeHandler (date) {\n      this.current_form.date = date._d\n    }\n  },\n}\n</script>"]}, "metadata": {}, "sourceType": "module"}