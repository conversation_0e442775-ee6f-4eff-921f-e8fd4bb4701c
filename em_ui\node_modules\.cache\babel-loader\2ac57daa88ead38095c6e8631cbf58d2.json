{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.array.splice.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.regexp.exec.js\");\nrequire(\"core-js/modules/es.string.split.js\");\nrequire(\"core-js/modules/es.string.trim.js\");\nrequire(\"core-js/modules/web.dom-collections.for-each.js\");\nexports.__esModule = true;\nvar _vue = require('vue');\nvar _vue2 = _interopRequireDefault(_vue);\nvar _dom = require('element-ui/lib/utils/dom');\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar hasModal = false;\nvar hasInitZIndex = false;\nvar zIndex = void 0;\nvar getModal = function getModal() {\n  if (_vue2.default.prototype.$isServer) return;\n  var modalDom = PopupManager.modalDom;\n  if (modalDom) {\n    hasModal = true;\n  } else {\n    hasModal = false;\n    modalDom = document.createElement('div');\n    PopupManager.modalDom = modalDom;\n    modalDom.addEventListener('touchmove', function (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    });\n    modalDom.addEventListener('click', function () {\n      PopupManager.doOnModalClick && PopupManager.doOnModalClick();\n    });\n  }\n  return modalDom;\n};\nvar instances = {};\nvar PopupManager = {\n  modalFade: true,\n  getInstance: function getInstance(id) {\n    return instances[id];\n  },\n  register: function register(id, instance) {\n    if (id && instance) {\n      instances[id] = instance;\n    }\n  },\n  deregister: function deregister(id) {\n    if (id) {\n      instances[id] = null;\n      delete instances[id];\n    }\n  },\n  nextZIndex: function nextZIndex() {\n    return PopupManager.zIndex++;\n  },\n  modalStack: [],\n  doOnModalClick: function doOnModalClick() {\n    var topItem = PopupManager.modalStack[PopupManager.modalStack.length - 1];\n    if (!topItem) return;\n    var instance = PopupManager.getInstance(topItem.id);\n    if (instance && instance.closeOnClickModal) {\n      instance.close();\n    }\n  },\n  openModal: function openModal(id, zIndex, dom, modalClass, modalFade) {\n    if (_vue2.default.prototype.$isServer) return;\n    if (!id || zIndex === undefined) return;\n    this.modalFade = modalFade;\n    var modalStack = this.modalStack;\n    for (var i = 0, j = modalStack.length; i < j; i++) {\n      var item = modalStack[i];\n      if (item.id === id) {\n        return;\n      }\n    }\n    var modalDom = getModal();\n    (0, _dom.addClass)(modalDom, 'v-modal');\n    if (this.modalFade && !hasModal) {\n      (0, _dom.addClass)(modalDom, 'v-modal-enter');\n    }\n    if (modalClass) {\n      var classArr = modalClass.trim().split(/\\s+/);\n      classArr.forEach(function (item) {\n        return (0, _dom.addClass)(modalDom, item);\n      });\n    }\n    setTimeout(function () {\n      (0, _dom.removeClass)(modalDom, 'v-modal-enter');\n    }, 200);\n    if (dom && dom.parentNode && dom.parentNode.nodeType !== 11) {\n      dom.parentNode.appendChild(modalDom);\n    } else {\n      document.body.appendChild(modalDom);\n    }\n    if (zIndex) {\n      modalDom.style.zIndex = zIndex;\n    }\n    modalDom.tabIndex = 0;\n    modalDom.style.display = '';\n    this.modalStack.push({\n      id: id,\n      zIndex: zIndex,\n      modalClass: modalClass\n    });\n  },\n  closeModal: function closeModal(id) {\n    var modalStack = this.modalStack;\n    var modalDom = getModal();\n    if (modalStack.length > 0) {\n      var topItem = modalStack[modalStack.length - 1];\n      if (topItem.id === id) {\n        if (topItem.modalClass) {\n          var classArr = topItem.modalClass.trim().split(/\\s+/);\n          classArr.forEach(function (item) {\n            return (0, _dom.removeClass)(modalDom, item);\n          });\n        }\n        modalStack.pop();\n        if (modalStack.length > 0) {\n          modalDom.style.zIndex = modalStack[modalStack.length - 1].zIndex;\n        }\n      } else {\n        for (var i = modalStack.length - 1; i >= 0; i--) {\n          if (modalStack[i].id === id) {\n            modalStack.splice(i, 1);\n            break;\n          }\n        }\n      }\n    }\n    if (modalStack.length === 0) {\n      if (this.modalFade) {\n        (0, _dom.addClass)(modalDom, 'v-modal-leave');\n      }\n      setTimeout(function () {\n        if (modalStack.length === 0) {\n          if (modalDom.parentNode) modalDom.parentNode.removeChild(modalDom);\n          modalDom.style.display = 'none';\n          PopupManager.modalDom = undefined;\n        }\n        (0, _dom.removeClass)(modalDom, 'v-modal-leave');\n      }, 200);\n    }\n  }\n};\nObject.defineProperty(PopupManager, 'zIndex', {\n  configurable: true,\n  get: function get() {\n    if (!hasInitZIndex) {\n      zIndex = zIndex || (_vue2.default.prototype.$ELEMENT || {}).zIndex || 2000;\n      hasInitZIndex = true;\n    }\n    return zIndex;\n  },\n  set: function set(value) {\n    zIndex = value;\n  }\n});\nvar getTopPopup = function getTopPopup() {\n  if (_vue2.default.prototype.$isServer) return;\n  if (PopupManager.modalStack.length > 0) {\n    var topPopup = PopupManager.modalStack[PopupManager.modalStack.length - 1];\n    if (!topPopup) return;\n    var instance = PopupManager.getInstance(topPopup.id);\n    return instance;\n  }\n};\nif (!_vue2.default.prototype.$isServer) {\n  // handle `esc` key when the popup is shown\n  window.addEventListener('keydown', function (event) {\n    if (event.keyCode === 27) {\n      var topPopup = getTopPopup();\n      if (topPopup && topPopup.closeOnPressEscape) {\n        topPopup.handleClose ? topPopup.handleClose() : topPopup.handleAction ? topPopup.handleAction('cancel') : topPopup.close();\n      }\n    }\n  });\n}\nexports.default = PopupManager;", "map": {"version": 3, "names": ["require", "exports", "__esModule", "_vue", "_vue2", "_interopRequireDefault", "_dom", "obj", "default", "hasModal", "hasInitZIndex", "zIndex", "getModal", "prototype", "$isServer", "modalDom", "PopupManager", "document", "createElement", "addEventListener", "event", "preventDefault", "stopPropagation", "doOnModalClick", "instances", "modalFade", "getInstance", "id", "register", "instance", "deregister", "nextZIndex", "modalStack", "topItem", "length", "closeOnClickModal", "close", "openModal", "dom", "modalClass", "undefined", "i", "j", "item", "addClass", "classArr", "trim", "split", "for<PERSON>ach", "setTimeout", "removeClass", "parentNode", "nodeType", "append<PERSON><PERSON><PERSON>", "body", "style", "tabIndex", "display", "push", "closeModal", "pop", "splice", "<PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "configurable", "get", "$ELEMENT", "set", "value", "getTopPopup", "topPopup", "window", "keyCode", "closeOnPressEscape", "handleClose", "handleAction"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/lib/utils/popup/popup-manager.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _vue = require('vue');\n\nvar _vue2 = _interopRequireDefault(_vue);\n\nvar _dom = require('element-ui/lib/utils/dom');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar hasModal = false;\nvar hasInitZIndex = false;\nvar zIndex = void 0;\n\nvar getModal = function getModal() {\n  if (_vue2.default.prototype.$isServer) return;\n  var modalDom = PopupManager.modalDom;\n  if (modalDom) {\n    hasModal = true;\n  } else {\n    hasModal = false;\n    modalDom = document.createElement('div');\n    PopupManager.modalDom = modalDom;\n\n    modalDom.addEventListener('touchmove', function (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    });\n\n    modalDom.addEventListener('click', function () {\n      PopupManager.doOnModalClick && PopupManager.doOnModalClick();\n    });\n  }\n\n  return modalDom;\n};\n\nvar instances = {};\n\nvar PopupManager = {\n  modalFade: true,\n\n  getInstance: function getInstance(id) {\n    return instances[id];\n  },\n\n  register: function register(id, instance) {\n    if (id && instance) {\n      instances[id] = instance;\n    }\n  },\n\n  deregister: function deregister(id) {\n    if (id) {\n      instances[id] = null;\n      delete instances[id];\n    }\n  },\n\n  nextZIndex: function nextZIndex() {\n    return PopupManager.zIndex++;\n  },\n\n  modalStack: [],\n\n  doOnModalClick: function doOnModalClick() {\n    var topItem = PopupManager.modalStack[PopupManager.modalStack.length - 1];\n    if (!topItem) return;\n\n    var instance = PopupManager.getInstance(topItem.id);\n    if (instance && instance.closeOnClickModal) {\n      instance.close();\n    }\n  },\n\n  openModal: function openModal(id, zIndex, dom, modalClass, modalFade) {\n    if (_vue2.default.prototype.$isServer) return;\n    if (!id || zIndex === undefined) return;\n    this.modalFade = modalFade;\n\n    var modalStack = this.modalStack;\n\n    for (var i = 0, j = modalStack.length; i < j; i++) {\n      var item = modalStack[i];\n      if (item.id === id) {\n        return;\n      }\n    }\n\n    var modalDom = getModal();\n\n    (0, _dom.addClass)(modalDom, 'v-modal');\n    if (this.modalFade && !hasModal) {\n      (0, _dom.addClass)(modalDom, 'v-modal-enter');\n    }\n    if (modalClass) {\n      var classArr = modalClass.trim().split(/\\s+/);\n      classArr.forEach(function (item) {\n        return (0, _dom.addClass)(modalDom, item);\n      });\n    }\n    setTimeout(function () {\n      (0, _dom.removeClass)(modalDom, 'v-modal-enter');\n    }, 200);\n\n    if (dom && dom.parentNode && dom.parentNode.nodeType !== 11) {\n      dom.parentNode.appendChild(modalDom);\n    } else {\n      document.body.appendChild(modalDom);\n    }\n\n    if (zIndex) {\n      modalDom.style.zIndex = zIndex;\n    }\n    modalDom.tabIndex = 0;\n    modalDom.style.display = '';\n\n    this.modalStack.push({ id: id, zIndex: zIndex, modalClass: modalClass });\n  },\n\n  closeModal: function closeModal(id) {\n    var modalStack = this.modalStack;\n    var modalDom = getModal();\n\n    if (modalStack.length > 0) {\n      var topItem = modalStack[modalStack.length - 1];\n      if (topItem.id === id) {\n        if (topItem.modalClass) {\n          var classArr = topItem.modalClass.trim().split(/\\s+/);\n          classArr.forEach(function (item) {\n            return (0, _dom.removeClass)(modalDom, item);\n          });\n        }\n\n        modalStack.pop();\n        if (modalStack.length > 0) {\n          modalDom.style.zIndex = modalStack[modalStack.length - 1].zIndex;\n        }\n      } else {\n        for (var i = modalStack.length - 1; i >= 0; i--) {\n          if (modalStack[i].id === id) {\n            modalStack.splice(i, 1);\n            break;\n          }\n        }\n      }\n    }\n\n    if (modalStack.length === 0) {\n      if (this.modalFade) {\n        (0, _dom.addClass)(modalDom, 'v-modal-leave');\n      }\n      setTimeout(function () {\n        if (modalStack.length === 0) {\n          if (modalDom.parentNode) modalDom.parentNode.removeChild(modalDom);\n          modalDom.style.display = 'none';\n          PopupManager.modalDom = undefined;\n        }\n        (0, _dom.removeClass)(modalDom, 'v-modal-leave');\n      }, 200);\n    }\n  }\n};\n\nObject.defineProperty(PopupManager, 'zIndex', {\n  configurable: true,\n  get: function get() {\n    if (!hasInitZIndex) {\n      zIndex = zIndex || (_vue2.default.prototype.$ELEMENT || {}).zIndex || 2000;\n      hasInitZIndex = true;\n    }\n    return zIndex;\n  },\n  set: function set(value) {\n    zIndex = value;\n  }\n});\n\nvar getTopPopup = function getTopPopup() {\n  if (_vue2.default.prototype.$isServer) return;\n  if (PopupManager.modalStack.length > 0) {\n    var topPopup = PopupManager.modalStack[PopupManager.modalStack.length - 1];\n    if (!topPopup) return;\n    var instance = PopupManager.getInstance(topPopup.id);\n\n    return instance;\n  }\n};\n\nif (!_vue2.default.prototype.$isServer) {\n  // handle `esc` key when the popup is shown\n  window.addEventListener('keydown', function (event) {\n    if (event.keyCode === 27) {\n      var topPopup = getTopPopup();\n\n      if (topPopup && topPopup.closeOnPressEscape) {\n        topPopup.handleClose ? topPopup.handleClose() : topPopup.handleAction ? topPopup.handleAction('cancel') : topPopup.close();\n      }\n    }\n  });\n}\n\nexports.default = PopupManager;"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,IAAI,GAAGH,OAAO,CAAC,KAAK,CAAC;AAEzB,IAAII,KAAK,GAAGC,sBAAsB,CAACF,IAAI,CAAC;AAExC,IAAIG,IAAI,GAAGN,OAAO,CAAC,0BAA0B,CAAC;AAE9C,SAASK,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACL,UAAU,GAAGK,GAAG,GAAG;IAAEC,OAAO,EAAED;EAAI,CAAC;AAAE;AAE9F,IAAIE,QAAQ,GAAG,KAAK;AACpB,IAAIC,aAAa,GAAG,KAAK;AACzB,IAAIC,MAAM,GAAG,KAAK,CAAC;AAEnB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,IAAIR,KAAK,CAACI,OAAO,CAACK,SAAS,CAACC,SAAS,EAAE;EACvC,IAAIC,QAAQ,GAAGC,YAAY,CAACD,QAAQ;EACpC,IAAIA,QAAQ,EAAE;IACZN,QAAQ,GAAG,IAAI;EACjB,CAAC,MAAM;IACLA,QAAQ,GAAG,KAAK;IAChBM,QAAQ,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACxCF,YAAY,CAACD,QAAQ,GAAGA,QAAQ;IAEhCA,QAAQ,CAACI,gBAAgB,CAAC,WAAW,EAAE,UAAUC,KAAK,EAAE;MACtDA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC;IAEFP,QAAQ,CAACI,gBAAgB,CAAC,OAAO,EAAE,YAAY;MAC7CH,YAAY,CAACO,cAAc,IAAIP,YAAY,CAACO,cAAc,CAAC,CAAC;IAC9D,CAAC,CAAC;EACJ;EAEA,OAAOR,QAAQ;AACjB,CAAC;AAED,IAAIS,SAAS,GAAG,CAAC,CAAC;AAElB,IAAIR,YAAY,GAAG;EACjBS,SAAS,EAAE,IAAI;EAEfC,WAAW,EAAE,SAASA,WAAWA,CAACC,EAAE,EAAE;IACpC,OAAOH,SAAS,CAACG,EAAE,CAAC;EACtB,CAAC;EAEDC,QAAQ,EAAE,SAASA,QAAQA,CAACD,EAAE,EAAEE,QAAQ,EAAE;IACxC,IAAIF,EAAE,IAAIE,QAAQ,EAAE;MAClBL,SAAS,CAACG,EAAE,CAAC,GAAGE,QAAQ;IAC1B;EACF,CAAC;EAEDC,UAAU,EAAE,SAASA,UAAUA,CAACH,EAAE,EAAE;IAClC,IAAIA,EAAE,EAAE;MACNH,SAAS,CAACG,EAAE,CAAC,GAAG,IAAI;MACpB,OAAOH,SAAS,CAACG,EAAE,CAAC;IACtB;EACF,CAAC;EAEDI,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,OAAOf,YAAY,CAACL,MAAM,EAAE;EAC9B,CAAC;EAEDqB,UAAU,EAAE,EAAE;EAEdT,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;IACxC,IAAIU,OAAO,GAAGjB,YAAY,CAACgB,UAAU,CAAChB,YAAY,CAACgB,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;IACzE,IAAI,CAACD,OAAO,EAAE;IAEd,IAAIJ,QAAQ,GAAGb,YAAY,CAACU,WAAW,CAACO,OAAO,CAACN,EAAE,CAAC;IACnD,IAAIE,QAAQ,IAAIA,QAAQ,CAACM,iBAAiB,EAAE;MAC1CN,QAAQ,CAACO,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAEDC,SAAS,EAAE,SAASA,SAASA,CAACV,EAAE,EAAEhB,MAAM,EAAE2B,GAAG,EAAEC,UAAU,EAAEd,SAAS,EAAE;IACpE,IAAIrB,KAAK,CAACI,OAAO,CAACK,SAAS,CAACC,SAAS,EAAE;IACvC,IAAI,CAACa,EAAE,IAAIhB,MAAM,KAAK6B,SAAS,EAAE;IACjC,IAAI,CAACf,SAAS,GAAGA,SAAS;IAE1B,IAAIO,UAAU,GAAG,IAAI,CAACA,UAAU;IAEhC,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGV,UAAU,CAACE,MAAM,EAAEO,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjD,IAAIE,IAAI,GAAGX,UAAU,CAACS,CAAC,CAAC;MACxB,IAAIE,IAAI,CAAChB,EAAE,KAAKA,EAAE,EAAE;QAClB;MACF;IACF;IAEA,IAAIZ,QAAQ,GAAGH,QAAQ,CAAC,CAAC;IAEzB,CAAC,CAAC,EAAEN,IAAI,CAACsC,QAAQ,EAAE7B,QAAQ,EAAE,SAAS,CAAC;IACvC,IAAI,IAAI,CAACU,SAAS,IAAI,CAAChB,QAAQ,EAAE;MAC/B,CAAC,CAAC,EAAEH,IAAI,CAACsC,QAAQ,EAAE7B,QAAQ,EAAE,eAAe,CAAC;IAC/C;IACA,IAAIwB,UAAU,EAAE;MACd,IAAIM,QAAQ,GAAGN,UAAU,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;MAC7CF,QAAQ,CAACG,OAAO,CAAC,UAAUL,IAAI,EAAE;QAC/B,OAAO,CAAC,CAAC,EAAErC,IAAI,CAACsC,QAAQ,EAAE7B,QAAQ,EAAE4B,IAAI,CAAC;MAC3C,CAAC,CAAC;IACJ;IACAM,UAAU,CAAC,YAAY;MACrB,CAAC,CAAC,EAAE3C,IAAI,CAAC4C,WAAW,EAAEnC,QAAQ,EAAE,eAAe,CAAC;IAClD,CAAC,EAAE,GAAG,CAAC;IAEP,IAAIuB,GAAG,IAAIA,GAAG,CAACa,UAAU,IAAIb,GAAG,CAACa,UAAU,CAACC,QAAQ,KAAK,EAAE,EAAE;MAC3Dd,GAAG,CAACa,UAAU,CAACE,WAAW,CAACtC,QAAQ,CAAC;IACtC,CAAC,MAAM;MACLE,QAAQ,CAACqC,IAAI,CAACD,WAAW,CAACtC,QAAQ,CAAC;IACrC;IAEA,IAAIJ,MAAM,EAAE;MACVI,QAAQ,CAACwC,KAAK,CAAC5C,MAAM,GAAGA,MAAM;IAChC;IACAI,QAAQ,CAACyC,QAAQ,GAAG,CAAC;IACrBzC,QAAQ,CAACwC,KAAK,CAACE,OAAO,GAAG,EAAE;IAE3B,IAAI,CAACzB,UAAU,CAAC0B,IAAI,CAAC;MAAE/B,EAAE,EAAEA,EAAE;MAAEhB,MAAM,EAAEA,MAAM;MAAE4B,UAAU,EAAEA;IAAW,CAAC,CAAC;EAC1E,CAAC;EAEDoB,UAAU,EAAE,SAASA,UAAUA,CAAChC,EAAE,EAAE;IAClC,IAAIK,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIjB,QAAQ,GAAGH,QAAQ,CAAC,CAAC;IAEzB,IAAIoB,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;MACzB,IAAID,OAAO,GAAGD,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;MAC/C,IAAID,OAAO,CAACN,EAAE,KAAKA,EAAE,EAAE;QACrB,IAAIM,OAAO,CAACM,UAAU,EAAE;UACtB,IAAIM,QAAQ,GAAGZ,OAAO,CAACM,UAAU,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;UACrDF,QAAQ,CAACG,OAAO,CAAC,UAAUL,IAAI,EAAE;YAC/B,OAAO,CAAC,CAAC,EAAErC,IAAI,CAAC4C,WAAW,EAAEnC,QAAQ,EAAE4B,IAAI,CAAC;UAC9C,CAAC,CAAC;QACJ;QAEAX,UAAU,CAAC4B,GAAG,CAAC,CAAC;QAChB,IAAI5B,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;UACzBnB,QAAQ,CAACwC,KAAK,CAAC5C,MAAM,GAAGqB,UAAU,CAACA,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC,CAACvB,MAAM;QAClE;MACF,CAAC,MAAM;QACL,KAAK,IAAI8B,CAAC,GAAGT,UAAU,CAACE,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC/C,IAAIT,UAAU,CAACS,CAAC,CAAC,CAACd,EAAE,KAAKA,EAAE,EAAE;YAC3BK,UAAU,CAAC6B,MAAM,CAACpB,CAAC,EAAE,CAAC,CAAC;YACvB;UACF;QACF;MACF;IACF;IAEA,IAAIT,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI,IAAI,CAACT,SAAS,EAAE;QAClB,CAAC,CAAC,EAAEnB,IAAI,CAACsC,QAAQ,EAAE7B,QAAQ,EAAE,eAAe,CAAC;MAC/C;MACAkC,UAAU,CAAC,YAAY;QACrB,IAAIjB,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;UAC3B,IAAInB,QAAQ,CAACoC,UAAU,EAAEpC,QAAQ,CAACoC,UAAU,CAACW,WAAW,CAAC/C,QAAQ,CAAC;UAClEA,QAAQ,CAACwC,KAAK,CAACE,OAAO,GAAG,MAAM;UAC/BzC,YAAY,CAACD,QAAQ,GAAGyB,SAAS;QACnC;QACA,CAAC,CAAC,EAAElC,IAAI,CAAC4C,WAAW,EAAEnC,QAAQ,EAAE,eAAe,CAAC;MAClD,CAAC,EAAE,GAAG,CAAC;IACT;EACF;AACF,CAAC;AAEDgD,MAAM,CAACC,cAAc,CAAChD,YAAY,EAAE,QAAQ,EAAE;EAC5CiD,YAAY,EAAE,IAAI;EAClBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;IAClB,IAAI,CAACxD,aAAa,EAAE;MAClBC,MAAM,GAAGA,MAAM,IAAI,CAACP,KAAK,CAACI,OAAO,CAACK,SAAS,CAACsD,QAAQ,IAAI,CAAC,CAAC,EAAExD,MAAM,IAAI,IAAI;MAC1ED,aAAa,GAAG,IAAI;IACtB;IACA,OAAOC,MAAM;EACf,CAAC;EACDyD,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;IACvB1D,MAAM,GAAG0D,KAAK;EAChB;AACF,CAAC,CAAC;AAEF,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvC,IAAIlE,KAAK,CAACI,OAAO,CAACK,SAAS,CAACC,SAAS,EAAE;EACvC,IAAIE,YAAY,CAACgB,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IACtC,IAAIqC,QAAQ,GAAGvD,YAAY,CAACgB,UAAU,CAAChB,YAAY,CAACgB,UAAU,CAACE,MAAM,GAAG,CAAC,CAAC;IAC1E,IAAI,CAACqC,QAAQ,EAAE;IACf,IAAI1C,QAAQ,GAAGb,YAAY,CAACU,WAAW,CAAC6C,QAAQ,CAAC5C,EAAE,CAAC;IAEpD,OAAOE,QAAQ;EACjB;AACF,CAAC;AAED,IAAI,CAACzB,KAAK,CAACI,OAAO,CAACK,SAAS,CAACC,SAAS,EAAE;EACtC;EACA0D,MAAM,CAACrD,gBAAgB,CAAC,SAAS,EAAE,UAAUC,KAAK,EAAE;IAClD,IAAIA,KAAK,CAACqD,OAAO,KAAK,EAAE,EAAE;MACxB,IAAIF,QAAQ,GAAGD,WAAW,CAAC,CAAC;MAE5B,IAAIC,QAAQ,IAAIA,QAAQ,CAACG,kBAAkB,EAAE;QAC3CH,QAAQ,CAACI,WAAW,GAAGJ,QAAQ,CAACI,WAAW,CAAC,CAAC,GAAGJ,QAAQ,CAACK,YAAY,GAAGL,QAAQ,CAACK,YAAY,CAAC,QAAQ,CAAC,GAAGL,QAAQ,CAACnC,KAAK,CAAC,CAAC;MAC5H;IACF;EACF,CAAC,CAAC;AACJ;AAEAnC,OAAO,CAACO,OAAO,GAAGQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}