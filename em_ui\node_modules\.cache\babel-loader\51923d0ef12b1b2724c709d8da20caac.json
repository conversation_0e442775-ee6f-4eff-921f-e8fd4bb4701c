{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nimport Vue from 'vue';\nvar component = require('./main.vue').default;\nvar constructor = Vue.extend(component);\nvar exampleNotice = function exampleNotice(options) {\n  options = options || {};\n  var instance = new constructor({\n    data: options\n  });\n  instance.vm = instance.$mount();\n  instance.dom = instance.vm.$el;\n  document.body.appendChild(instance.dom);\n  return instance.vm;\n};\nexport default {\n  install: function install(Vue) {\n    Vue.prototype[\"$\".concat(component.name)] = exampleNotice;\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON>", "component", "require", "default", "constructor", "extend", "exampleNotice", "options", "instance", "data", "vm", "$mount", "dom", "$el", "document", "body", "append<PERSON><PERSON><PERSON>", "install", "prototype", "concat", "name"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/ExampleNotice/index.js"], "sourcesContent": ["import Vue from 'vue'\n\nconst component = require('./main.vue').default\nconst constructor = Vue.extend(component)\n\nconst exampleNotice = options => {\n    options = options || {}\n    let instance = new constructor({\n        data: options\n    })\n    instance.vm = instance.$mount()\n    instance.dom = instance.vm.$el\n    document.body.appendChild(instance.dom)\n    return instance.vm\n}\n\nexport default {\n    install: Vue => {\n        Vue.prototype[`$${component.name}`] = exampleNotice\n    }\n}\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,KAAK;AAErB,IAAMC,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC,CAACC,OAAO;AAC/C,IAAMC,WAAW,GAAGJ,GAAG,CAACK,MAAM,CAACJ,SAAS,CAAC;AAEzC,IAAMK,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,OAAO,EAAI;EAC7BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIC,QAAQ,GAAG,IAAIJ,WAAW,CAAC;IAC3BK,IAAI,EAAEF;EACV,CAAC,CAAC;EACFC,QAAQ,CAACE,EAAE,GAAGF,QAAQ,CAACG,MAAM,CAAC,CAAC;EAC/BH,QAAQ,CAACI,GAAG,GAAGJ,QAAQ,CAACE,EAAE,CAACG,GAAG;EAC9BC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACR,QAAQ,CAACI,GAAG,CAAC;EACvC,OAAOJ,QAAQ,CAACE,EAAE;AACtB,CAAC;AAED,eAAe;EACXO,OAAO,EAAE,SAATA,OAAOA,CAAEjB,GAAG,EAAI;IACZA,GAAG,CAACkB,SAAS,KAAAC,MAAA,CAAKlB,SAAS,CAACmB,IAAI,EAAG,GAAGd,aAAa;EACvD;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}