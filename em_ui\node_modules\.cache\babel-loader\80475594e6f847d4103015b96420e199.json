{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.regexp.constructor.js\";\nimport \"core-js/modules/es.regexp.dot-all.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.sticky.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.match.js\";\nimport _typeof from 'babel-runtime/helpers/typeof';\nimport * as util from '../util';\nimport required from './required';\n\n/* eslint max-len:0 */\n\nvar pattern = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/,\n  url: new RegExp(\"^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$\", 'i'),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\n};\nvar types = {\n  integer: function integer(value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  float: function float(value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array: function array(value) {\n    return Array.isArray(value);\n  },\n  regexp: function regexp(value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date: function date(value) {\n    return typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear === 'function';\n  },\n  number: function number(value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object: function object(value) {\n    return (typeof value === 'undefined' ? 'undefined' : _typeof(value)) === 'object' && !types.array(value);\n  },\n  method: function method(value) {\n    return typeof value === 'function';\n  },\n  email: function email(value) {\n    return typeof value === 'string' && !!value.match(pattern.email) && value.length < 255;\n  },\n  url: function url(value) {\n    return typeof value === 'string' && !!value.match(pattern.url);\n  },\n  hex: function hex(value) {\n    return typeof value === 'string' && !!value.match(pattern.hex);\n  }\n};\n\n/**\n *  Rule for validating the type of a value.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction type(rule, value, source, errors, options) {\n  if (rule.required && value === undefined) {\n    required(rule, value, source, errors, options);\n    return;\n  }\n  var custom = ['integer', 'float', 'array', 'regexp', 'object', 'method', 'email', 'number', 'date', 'url', 'hex'];\n  var ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(util.format(options.messages.types[ruleType], rule.fullField, rule.type));\n    }\n    // straight typeof check\n  } else if (ruleType && (typeof value === 'undefined' ? 'undefined' : _typeof(value)) !== rule.type) {\n    errors.push(util.format(options.messages.types[ruleType], rule.fullField, rule.type));\n  }\n}\nexport default type;", "map": {"version": 3, "names": ["_typeof", "util", "required", "pattern", "email", "url", "RegExp", "hex", "types", "integer", "value", "number", "parseInt", "float", "array", "Array", "isArray", "regexp", "e", "date", "getTime", "getMonth", "getYear", "isNaN", "object", "method", "match", "length", "type", "rule", "source", "errors", "options", "undefined", "custom", "ruleType", "indexOf", "push", "format", "messages", "fullField"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/node_modules/async-validator/es/rule/type.js"], "sourcesContent": ["import _typeof from 'babel-runtime/helpers/typeof';\nimport * as util from '../util';\nimport required from './required';\n\n/* eslint max-len:0 */\n\nvar pattern = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/,\n  url: new RegExp('^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-?)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$', 'i'),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\n};\n\nvar types = {\n  integer: function integer(value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  float: function float(value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array: function array(value) {\n    return Array.isArray(value);\n  },\n  regexp: function regexp(value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date: function date(value) {\n    return typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear === 'function';\n  },\n  number: function number(value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object: function object(value) {\n    return (typeof value === 'undefined' ? 'undefined' : _typeof(value)) === 'object' && !types.array(value);\n  },\n  method: function method(value) {\n    return typeof value === 'function';\n  },\n  email: function email(value) {\n    return typeof value === 'string' && !!value.match(pattern.email) && value.length < 255;\n  },\n  url: function url(value) {\n    return typeof value === 'string' && !!value.match(pattern.url);\n  },\n  hex: function hex(value) {\n    return typeof value === 'string' && !!value.match(pattern.hex);\n  }\n};\n\n/**\n *  Rule for validating the type of a value.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction type(rule, value, source, errors, options) {\n  if (rule.required && value === undefined) {\n    required(rule, value, source, errors, options);\n    return;\n  }\n  var custom = ['integer', 'float', 'array', 'regexp', 'object', 'method', 'email', 'number', 'date', 'url', 'hex'];\n  var ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(util.format(options.messages.types[ruleType], rule.fullField, rule.type));\n    }\n    // straight typeof check\n  } else if (ruleType && (typeof value === 'undefined' ? 'undefined' : _typeof(value)) !== rule.type) {\n    errors.push(util.format(options.messages.types[ruleType], rule.fullField, rule.type));\n  }\n}\n\nexport default type;"], "mappings": ";;;;;;;AAAA,OAAOA,OAAO,MAAM,8BAA8B;AAClD,OAAO,KAAKC,IAAI,MAAM,SAAS;AAC/B,OAAOC,QAAQ,MAAM,YAAY;;AAEjC;;AAEA,IAAIC,OAAO,GAAG;EACZ;EACAC,KAAK,EAAE,wJAAwJ;EAC/JC,GAAG,EAAE,IAAIC,MAAM,CAAC,gZAAgZ,EAAE,GAAG,CAAC;EACtaC,GAAG,EAAE;AACP,CAAC;AAED,IAAIC,KAAK,GAAG;EACVC,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;IAC/B,OAAOF,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC,IAAIE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,KAAKA,KAAK;EAC7D,CAAC;EACDG,KAAK,EAAE,SAASA,KAAKA,CAACH,KAAK,EAAE;IAC3B,OAAOF,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;EACrD,CAAC;EACDI,KAAK,EAAE,SAASA,KAAKA,CAACJ,KAAK,EAAE;IAC3B,OAAOK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC;EAC7B,CAAC;EACDO,MAAM,EAAE,SAASA,MAAMA,CAACP,KAAK,EAAE;IAC7B,IAAIA,KAAK,YAAYJ,MAAM,EAAE;MAC3B,OAAO,IAAI;IACb;IACA,IAAI;MACF,OAAO,CAAC,CAAC,IAAIA,MAAM,CAACI,KAAK,CAAC;IAC5B,CAAC,CAAC,OAAOQ,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC;EACDC,IAAI,EAAE,SAASA,IAAIA,CAACT,KAAK,EAAE;IACzB,OAAO,OAAOA,KAAK,CAACU,OAAO,KAAK,UAAU,IAAI,OAAOV,KAAK,CAACW,QAAQ,KAAK,UAAU,IAAI,OAAOX,KAAK,CAACY,OAAO,KAAK,UAAU;EAC3H,CAAC;EACDX,MAAM,EAAE,SAASA,MAAMA,CAACD,KAAK,EAAE;IAC7B,IAAIa,KAAK,CAACb,KAAK,CAAC,EAAE;MAChB,OAAO,KAAK;IACd;IACA,OAAO,OAAOA,KAAK,KAAK,QAAQ;EAClC,CAAC;EACDc,MAAM,EAAE,SAASA,MAAMA,CAACd,KAAK,EAAE;IAC7B,OAAO,CAAC,OAAOA,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGV,OAAO,CAACU,KAAK,CAAC,MAAM,QAAQ,IAAI,CAACF,KAAK,CAACM,KAAK,CAACJ,KAAK,CAAC;EAC1G,CAAC;EACDe,MAAM,EAAE,SAASA,MAAMA,CAACf,KAAK,EAAE;IAC7B,OAAO,OAAOA,KAAK,KAAK,UAAU;EACpC,CAAC;EACDN,KAAK,EAAE,SAASA,KAAKA,CAACM,KAAK,EAAE;IAC3B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,CAACA,KAAK,CAACgB,KAAK,CAACvB,OAAO,CAACC,KAAK,CAAC,IAAIM,KAAK,CAACiB,MAAM,GAAG,GAAG;EACxF,CAAC;EACDtB,GAAG,EAAE,SAASA,GAAGA,CAACK,KAAK,EAAE;IACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,CAACA,KAAK,CAACgB,KAAK,CAACvB,OAAO,CAACE,GAAG,CAAC;EAChE,CAAC;EACDE,GAAG,EAAE,SAASA,GAAGA,CAACG,KAAK,EAAE;IACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,CAACA,KAAK,CAACgB,KAAK,CAACvB,OAAO,CAACI,GAAG,CAAC;EAChE;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,IAAIA,CAACC,IAAI,EAAEnB,KAAK,EAAEoB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAClD,IAAIH,IAAI,CAAC3B,QAAQ,IAAIQ,KAAK,KAAKuB,SAAS,EAAE;IACxC/B,QAAQ,CAAC2B,IAAI,EAAEnB,KAAK,EAAEoB,MAAM,EAAEC,MAAM,EAAEC,OAAO,CAAC;IAC9C;EACF;EACA,IAAIE,MAAM,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;EACjH,IAAIC,QAAQ,GAAGN,IAAI,CAACD,IAAI;EACxB,IAAIM,MAAM,CAACE,OAAO,CAACD,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;IACjC,IAAI,CAAC3B,KAAK,CAAC2B,QAAQ,CAAC,CAACzB,KAAK,CAAC,EAAE;MAC3BqB,MAAM,CAACM,IAAI,CAACpC,IAAI,CAACqC,MAAM,CAACN,OAAO,CAACO,QAAQ,CAAC/B,KAAK,CAAC2B,QAAQ,CAAC,EAAEN,IAAI,CAACW,SAAS,EAAEX,IAAI,CAACD,IAAI,CAAC,CAAC;IACvF;IACA;EACF,CAAC,MAAM,IAAIO,QAAQ,IAAI,CAAC,OAAOzB,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGV,OAAO,CAACU,KAAK,CAAC,MAAMmB,IAAI,CAACD,IAAI,EAAE;IAClGG,MAAM,CAACM,IAAI,CAACpC,IAAI,CAACqC,MAAM,CAACN,OAAO,CAACO,QAAQ,CAAC/B,KAAK,CAAC2B,QAAQ,CAAC,EAAEN,IAAI,CAACW,SAAS,EAAEX,IAAI,CAACD,IAAI,CAAC,CAAC;EACvF;AACF;AAEA,eAAeA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}