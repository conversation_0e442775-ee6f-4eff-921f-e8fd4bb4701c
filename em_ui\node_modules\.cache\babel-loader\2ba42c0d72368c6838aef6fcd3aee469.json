{"ast": null, "code": "export default {\n  name: 'SvgIcon',\n  props: {\n    name: {\n      type: String,\n      required: true\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAOA;EACAA;EACAC;IACAD;MACAE;MACAC;IACA;EACA;AACA", "names": ["name", "props", "type", "required"], "sourceRoot": "src/components/SvgIcon", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <svg class=\"svg-icon\" aria-hidden=\"true\" v-on=\"$listeners\">\n        <use :xlink:href=\"`#icon-${name}`\" />\n    </svg>\n</template>\n\n<script>\nexport default {\n    name: 'SvgIcon',\n    props: {\n        name: {\n            type: String,\n            required: true\n        }\n    }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n    width: 1em;\n    height: 1em;\n    vertical-align: -0.15em;\n    fill: currentColor;\n    overflow: hidden;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}