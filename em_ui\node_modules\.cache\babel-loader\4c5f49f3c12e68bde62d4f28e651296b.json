{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { compile } from 'path-to-regexp';\nimport { deepClone } from '@/util';\nimport UserMenu from '../UserMenu';\nexport default {\n  name: 'Breadcrumb',\n  components: {\n    UserMenu: UserMenu\n  },\n  computed: {\n    breadcrumbList: function breadcrumbList() {\n      var _this = this;\n      var breadcrumbList = [];\n      if (this.$store.state.settings.enableDashboard) {\n        breadcrumbList.push({\n          path: '/dashboard',\n          title: this.$store.state.settings.dashboardTitle\n        });\n      }\n      if (this.$store.state.settings.enableFlatRoutes) {\n        if (this.$route.meta.breadcrumbNeste) {\n          this.$route.meta.breadcrumbNeste.map(function (item, index) {\n            var tmpItem = deepClone(item);\n            if (index != 0) {\n              tmpItem.path = \"\".concat(_this.$route.meta.breadcrumbNeste[0].path, \"/\").concat(item.path);\n            }\n            breadcrumbList.push(tmpItem);\n          });\n        }\n      } else {\n        this.$route.matched.map(function (item) {\n          if (item.meta && item.meta.title && item.meta.breadcrumb !== false && item.path != '/dashboard') {\n            breadcrumbList.push({\n              path: item.path,\n              title: item.meta.title\n            });\n          }\n        });\n      }\n      return breadcrumbList;\n    }\n  },\n  methods: {\n    pathCompile: function pathCompile(path) {\n      var toPath = compile(path);\n      return toPath(this.$route.params);\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;AAqBA;AACA;AACA;AAEA;EACAA;EACAC;IACAC;EACA;EACAC;IACAC;MAAA;MACA;MACA;QACAA;UACAC;UACAC;QACA;MACA;MACA;QACA;UACA;YACA;YACA;cACAC;YACA;YACAH;UACA;QACA;MACA;QACA;UACA;YACAA;cACAC;cACAC;YACA;UACA;QACA;MACA;MACA;IACA;EACA;EACAE;IACAC;MACA;MACA;IACA;EACA;AACA", "names": ["name", "components", "UserMenu", "computed", "breadcrumbList", "path", "title", "tmpItem", "methods", "pathCompile"], "sourceRoot": "src/layout/components/Topbar", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"topbar-container\">\n        <div class=\"left-box\">\n            <div v-if=\"$store.state.settings.mode == 'mobile' || $store.state.settings.enableSidebarCollapse\" :class=\"{\n                'sidebar-collapse': true,\n                'is-collapse': $store.state.settings.sidebarCollapse\n            }\" @click=\"$store.commit('settings/toggleSidebarCollapse')\"\n            >\n                <svg-icon name=\"collapse\" />\n            </div>\n            <el-breadcrumb v-if=\"$store.state.settings.mode == 'pc'\" separator-class=\"el-icon-arrow-right\">\n                <transition-group name=\"breadcrumb\">\n                    <el-breadcrumb-item v-for=\"item in breadcrumbList\" :key=\"item.path\" :to=\"pathCompile(item.path)\">{{ item.title }}</el-breadcrumb-item>\n                </transition-group>\n            </el-breadcrumb>\n        </div>\n        <UserMenu />\n    </div>\n</template>\n\n<script>\nimport { compile } from 'path-to-regexp'\nimport { deepClone } from '@/util'\nimport UserMenu from '../UserMenu'\n\nexport default {\n    name: 'Breadcrumb',\n    components: {\n        UserMenu\n    },\n    computed: {\n        breadcrumbList() {\n            let breadcrumbList = []\n            if (this.$store.state.settings.enableDashboard) {\n                breadcrumbList.push({\n                    path: '/dashboard',\n                    title: this.$store.state.settings.dashboardTitle\n                })\n            }\n            if (this.$store.state.settings.enableFlatRoutes) {\n                if (this.$route.meta.breadcrumbNeste) {\n                    this.$route.meta.breadcrumbNeste.map((item, index) => {\n                        let tmpItem = deepClone(item)\n                        if (index != 0) {\n                            tmpItem.path = `${this.$route.meta.breadcrumbNeste[0].path}/${item.path}`\n                        }\n                        breadcrumbList.push(tmpItem)\n                    })\n                }\n            } else {\n                this.$route.matched.map(item => {\n                    if (item.meta && item.meta.title && item.meta.breadcrumb !== false && item.path != '/dashboard') {\n                        breadcrumbList.push({\n                            path: item.path,\n                            title: item.meta.title\n                        })\n                    }\n                })\n            }\n            return breadcrumbList\n        }\n    },\n    methods: {\n        pathCompile(path) {\n            var toPath = compile(path)\n            return toPath(this.$route.params)\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n[data-mode=mobile] {\n    .topbar-container {\n        width: 100%;\n        transform: translateX(-50%);\n    }\n}\n.topbar-container {\n    position: fixed;\n    z-index: 999;\n    top: 0;\n    right: 0;\n    left: 50%;\n    width: calc(100% - #{$g_sidebar_width});\n    transform: translateX(calc(-50% + #{$g_sidebar_width} * 0.5));\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: $g-topbar-height;\n    background-color: #fff;\n    transition: 0.3s, box-shadow 0.2s;\n    box-shadow: 0 0 1px 0 #ccc;\n    &.shadow {\n        box-shadow: 0 10px 10px -10px #ccc;\n    }\n    .left-box {\n        display: flex;\n        align-items: center;\n        padding-right: 50px;\n        overflow: hidden;\n        mask-image: linear-gradient(90deg, #000 0%, #000 calc(100% - 50px), transparent);\n        .sidebar-collapse {\n            display: flex;\n            align-items: center;\n            padding: 0 20px;\n            height: 50px;\n            cursor: pointer;\n            transition: 0.3s;\n            &:hover {\n                background-image: linear-gradient(to right, #ddd, transparent);\n            }\n            .svg-icon {\n                transition: 0.3s;\n            }\n            &.is-collapse .svg-icon {\n                transform: rotateZ(-180deg);\n            }\n            & + .el-breadcrumb {\n                margin-left: 0;\n            }\n        }\n        ::v-deep .el-breadcrumb {\n            margin-left: 20px;\n            white-space: nowrap;\n            .el-breadcrumb__item {\n                display: inline-block;\n                float: none;\n                span {\n                    font-weight: normal;\n                }\n                &:last-child span {\n                    color: #97a8be;\n                }\n            }\n        }\n    }\n}\n// 面包屑动画\n.breadcrumb-enter-active {\n    transition: all 0.25s;\n}\n.breadcrumb-enter,\n.breadcrumb-leave-active {\n    opacity: 0;\n    transform: translateX(30px) skewX(-50deg);\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}