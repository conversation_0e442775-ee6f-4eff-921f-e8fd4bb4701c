{"ast": null, "code": "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };", "map": {"version": 3, "names": ["arrayLikeToArray", "_arrayWithoutHoles", "r", "Array", "isArray", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js"], "sourcesContent": ["import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,kBAAkBA,CAACC,CAAC,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAAE,OAAOF,gBAAgB,CAACE,CAAC,CAAC;AAClD;AACA,SAASD,kBAAkB,IAAII,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}