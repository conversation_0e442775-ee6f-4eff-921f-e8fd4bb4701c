{"ast": null, "code": "import \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.replace.js\";\nexport var rTime = function rTime(date) {\n  var json_date = new Date(date).toJSON();\n  return new Date(new Date(json_date) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\\.[\\d]{3}Z/, '');\n};", "map": {"version": 3, "names": ["rTime", "date", "json_date", "Date", "toJSON", "toISOString", "replace"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/util/time.js"], "sourcesContent": ["export let rTime = (date) => {\n  let json_date = new Date(date).toJSON();\n  return new Date(new Date(json_date) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\\.[\\d]{3}Z/, '')\n}"], "mappings": ";;AAAA,OAAO,IAAIA,KAAK,GAAG,SAARA,KAAKA,CAAIC,IAAI,EAAK;EAC3B,IAAIC,SAAS,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC,CAACG,MAAM,CAAC,CAAC;EACvC,OAAO,IAAID,IAAI,CAAC,IAAIA,IAAI,CAACD,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAACG,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AACnH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}