{"ast": null, "code": "import { authAll } from '@/util';\nexport default {\n  name: 'AuthAll',\n  props: {\n    value: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  methods: {\n    check: function check() {\n      return authAll(this.value);\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAQA;AAEA;EACAA;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "props", "value", "type", "default", "methods", "check"], "sourceRoot": "src/components/AuthAll", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div>\n        <slot v-if=\"check()\" />\n        <slot v-else name=\"no-auth\" />\n    </div>\n</template>\n\n<script>\nimport { authAll } from '@/util'\n\nexport default {\n    name: 'AuthAll',\n    props: {\n        value: {\n            type: Array,\n            default: () => []\n        }\n    },\n    methods: {\n        check() {\n            return authAll(this.value)\n        }\n    }\n}\n</script>\n"]}, "metadata": {}, "sourceType": "module"}