{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getBuilding, saveBuilding, deleteBuilding } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      rTime: rTime,\n      loading: false,\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      },\n      table_selectedRowKeys: [],\n      building_query_type: 'name',\n      building_query_buttonTitle: '搜索',\n      building_query_text: '',\n      building_save_title: '新增楼宇',\n      building_save_modalVisible: false,\n      building_form_data: {},\n      building_data_list: []\n    };\n  },\n  created: function created() {\n    this.Get_buildingDataList();\n  },\n  watch: {\n    building_save_modalVisible: function building_save_modalVisible(val) {\n      if (!val) {\n        this.building_form_data = {};\n      }\n    }\n  },\n  methods: {\n    Get_buildingDataList: function Get_buildingDataList() {\n      var _this = this;\n      getBuilding().then(function (res) {\n        _this.building_query_buttonTitle = '搜索';\n        _this.building_data_list = res.data;\n        _this.building_save_title = '新增楼宇';\n      });\n    },\n    Query_buildingDataList: function Query_buildingDataList() {\n      var _this2 = this;\n      var text = this.building_query_text;\n      var temp_list = [];\n      this.building_data_list.forEach(function (item) {\n        var flag = false;\n        if (typeof item[_this2.building_query_type] == 'number') {\n          if (item[_this2.building_query_type] <= parseInt(text)) {\n            flag = true;\n          }\n        } else {\n          if (item[_this2.building_query_type].indexOf(text) != -1) {\n            flag = true;\n          }\n        }\n        if (flag) {\n          temp_list.push(item);\n        }\n      });\n      this.building_query_buttonTitle = '返回';\n      this.building_data_list = temp_list;\n    },\n    Edit_buildingData: function Edit_buildingData(form) {\n      this.building_save_title = '编辑楼宇';\n      this.building_form_data = JSON.parse(JSON.stringify(form));\n      this.building_save_modalVisible = true;\n    },\n    Del_buildingData: function Del_buildingData(id) {\n      var _this3 = this;\n      deleteBuilding(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_buildingDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_buildingData(_this4.building_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_buildingData: function Save_buildingData() {\n      var _this5 = this;\n      saveBuilding(this.building_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.building_save_modalVisible = false;\n        _this5.Get_buildingDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_date_changeHandler: function Form_date_changeHandler(date) {\n      this.building_form_data.date = date._d;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AA+GA;AACA;AACA;AACA;EACAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAJ;MACA;QACA;MACA;IACA;EACA;EACAK;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;UACA;YACAC;UACA;QACA;UACA;YACAA;UACA;QACA;QACA;UACAC;QACA;MAEA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA", "names": ["data", "rTime", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "building_query_type", "building_query_buttonTitle", "building_query_text", "building_save_title", "building_save_modalVisible", "building_form_data", "building_data_list", "created", "watch", "methods", "Get_buildingDataList", "getBuilding", "_this", "Query_buildingDataList", "flag", "temp_list", "Edit_buildingData", "Del_buildingData", "deleteBuilding", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_buildingData", "saveBuilding", "_this5", "Table_selectChange", "Form_date_changeHandler"], "sourceRoot": "src/views/admin/rq", "sources": ["rq_building.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"社区楼宇管理\">\n      <div class=\"head\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"单位名称\"\n          v-model=\"building_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"name\">楼宇名称</a-select-option>\n          <a-select-option value=\"layers\">楼宇层数</a-select-option>\n          <a-select-option value=\"height\">楼宇高度</a-select-option>\n          <a-select-option value=\"area\">楼宇面积</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"building_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"building_query_buttonTitle\"\n          size=\"large\"\n          @search=\"building_query_buttonTitle == '搜索' ? Query_buildingDataList() : Get_buildingDataList()\"\n        />\n        <a-button\n          type=\"primary\"\n          style=\"height: 38px;\"\n          @click=\"building_save_modalVisible = true\"\n        >添加楼宇</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「楼宇」</a-button>\n      </div>\n      <a-table\n        :data-source=\"building_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"name\" title=\"楼宇名称\" data-index=\"name\" />\n        <a-table-column key=\"layers\" title=\"楼宇层数\" data-index=\"layers\" />\n        <a-table-column key=\"height\" title=\"楼宇高度\" data-index=\"height\" />\n        <a-table-column key=\"area\" title=\"楼宇面积\" data-index=\"area\" />\n        <a-table-column key=\"date\" title=\"楼宇建成时间\" data-index=\"date\">\n          <!-- rTime -->\n          <template slot-scope=\"text, record\">\n            <span>{{rTime(record.date)}}</span>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_buildingData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_buildingData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"building_save_modalVisible\"\n      :title=\"building_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_buildingData\"\n    >\n      <a-form-model :model=\"building_form_data\" :label-col=\"labelCol\" :wrapper-col=\"wrapperCol\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"楼宇名称\">\n              <a-input v-model=\"building_form_data.name\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"楼宇层数\">\n              <a-input v-model=\"building_form_data.layers\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"楼宇高度\">\n              <a-input v-model=\"building_form_data.height\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"楼宇面积\">\n              <a-input v-model=\"building_form_data.area\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"发布时间\">\n              <a-date-picker\n                :default-value=\"building_form_data.date\"\n                @change=\"Form_date_changeHandler\"\n                placeholder=\"选择时间\"\n              />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getBuilding, saveBuilding, deleteBuilding } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\nexport default {\n  data () {\n    return {\n      rTime,\n      loading: false,\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n      table_selectedRowKeys: [],\n      building_query_type: 'name',\n      building_query_buttonTitle: '搜索',\n      building_query_text: '',\n      building_save_title: '新增楼宇',\n      building_save_modalVisible: false,\n      building_form_data: {},\n      building_data_list: [],\n    }\n  },\n  created () {\n    this.Get_buildingDataList()\n  },\n  watch: {\n    building_save_modalVisible (val) {\n      if (!val) {\n        this.building_form_data = {}\n      }\n    }\n  },\n  methods: {\n    Get_buildingDataList () {\n      getBuilding().then(res => {\n        this.building_query_buttonTitle = '搜索'\n        this.building_data_list = res.data\n        this.building_save_title = '新增楼宇'\n      })\n    },\n    Query_buildingDataList () {\n      let text = this.building_query_text\n      let temp_list = []\n      this.building_data_list.forEach(item => {\n        let flag = false\n        if (typeof (item[this.building_query_type]) == 'number') {\n          if (item[this.building_query_type] <= parseInt(text)) {\n            flag = true\n          }\n        } else {\n          if (item[this.building_query_type].indexOf(text) != -1) {\n            flag = true\n          }\n        }\n        if (flag) {\n          temp_list.push(item)\n        }\n\n      })\n      this.building_query_buttonTitle = '返回'\n      this.building_data_list = temp_list\n    },\n    Edit_buildingData (form) {\n      this.building_save_title = '编辑楼宇'\n      this.building_form_data = JSON.parse(JSON.stringify(form))\n      this.building_save_modalVisible = true\n    },\n    Del_buildingData (id) {\n      deleteBuilding(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_buildingDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_buildingData(this.building_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    Save_buildingData () {\n      saveBuilding(this.building_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.building_save_modalVisible = false\n        this.Get_buildingDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_date_changeHandler (date) {\n      this.building_form_data.date = date._d\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}