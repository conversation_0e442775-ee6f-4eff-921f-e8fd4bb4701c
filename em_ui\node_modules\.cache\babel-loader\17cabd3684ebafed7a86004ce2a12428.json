{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { isAdmin, logout_ } from '@/api/requests/rq-manage.js';\nvar state = {\n  account: localStorage.username,\n  token: localStorage.token,\n  failure_time: localStorage.failure_time,\n  permissions: []\n};\nvar getters = {\n  isLogin: function isLogin(state) {\n    var retn = false;\n    if (state.token != null) {\n      var unix = Date.parse(new Date());\n      if (unix < state.failure_time * 1000) {\n        retn = true;\n      }\n    }\n    return retn;\n  }\n};\nvar actions = {\n  login: function login(_ref, data) {\n    var commit = _ref.commit;\n    return new Promise(function (resolve) {\n      commit('setUserData', {});\n      resolve({\n        isSuccess: true,\n        msg: 'aaa'\n      });\n    });\n  },\n  logout: function logout(_ref2) {\n    var commit = _ref2.commit;\n    logout_();\n    window.location.href = '/home';\n  },\n  // 获取我的权限\n  getPermissions: function getPermissions(_ref3) {\n    var state = _ref3.state,\n      commit = _ref3.commit;\n    return new Promise(function (resolve) {\n      isAdmin().then(function (result) {\n        var per = ['permission.browse', 'permission.create', 'permission.edit', 'permission.remove'];\n        if (result) {\n          per.push('admin');\n          localStorage.setItem('isAdmin', true);\n        } else {\n          localStorage.setItem('isAdmin', false);\n        }\n        commit('setPermissions', per);\n        resolve(per);\n      });\n    });\n  }\n};\nvar mutations = {\n  setUserData: function setUserData(state, data) {\n    localStorage.setItem('account', 'admin');\n    localStorage.setItem('username', 'admin');\n    localStorage.setItem('token', ' data.token');\n    localStorage.setItem('failure_time', Date.parse(new Date()) + 1000 * 50 * 60 * 24);\n    state.account = data.username;\n    state.token = data.token;\n    state.failure_time = data.failureTime;\n  },\n  removeUserData: function removeUserData(state) {\n    localStorage.removeItem('account');\n    localStorage.removeItem('token');\n    localStorage.removeItem('failure_time');\n    state.account = '';\n    state.token = '';\n    state.failure_time = '';\n  },\n  setPermissions: function setPermissions(state, permissions) {\n    state.permissions = permissions;\n  }\n};\nexport default {\n  namespaced: true,\n  state: state,\n  actions: actions,\n  getters: getters,\n  mutations: mutations\n};", "map": {"version": 3, "names": ["isAdmin", "logout_", "state", "account", "localStorage", "username", "token", "failure_time", "permissions", "getters", "is<PERSON>ogin", "retn", "unix", "Date", "parse", "actions", "login", "_ref", "data", "commit", "Promise", "resolve", "isSuccess", "msg", "logout", "_ref2", "window", "location", "href", "getPermissions", "_ref3", "then", "result", "per", "push", "setItem", "mutations", "setUserData", "failureTime", "removeUserData", "removeItem", "setPermissions", "namespaced"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/store/modules/user.js"], "sourcesContent": ["import { isAdmin, logout_ } from '@/api/requests/rq-manage.js'\n\nconst state = {\n  account: localStorage.username,\n  token: localStorage.token,\n  failure_time: localStorage.failure_time,\n  permissions: []\n}\n\nconst getters = {\n  isLogin: state => {\n    let retn = false\n    if (state.token != null) {\n      let unix = Date.parse(new Date())\n      if (unix < state.failure_time * 1000) {\n        retn = true\n      }\n    }\n    return retn\n  }\n}\n\nconst actions = {\n  login ({ commit }, data) {\n    return new Promise(resolve => {\n\n      commit('setUserData', {})\n      resolve({\n        isSuccess: true,\n        msg: 'aaa'\n      })\n    })\n  },\n  logout ({ commit }) {\n    logout_()\n    window.location.href = '/home'\n\n  },\n  // 获取我的权限\n  getPermissions ({ state, commit }) {\n    return new Promise(resolve => {\n      isAdmin().then(result => {\n        let per = [\n          'permission.browse',\n          'permission.create',\n          'permission.edit',\n          'permission.remove'\n        ]\n        if (result) {\n          per.push('admin')\n          localStorage.setItem('isAdmin', true)\n        } else {\n          localStorage.setItem('isAdmin', false)\n        }\n\n        commit('setPermissions', per)\n        resolve(per)\n      })\n\n    })\n\n  }\n}\n\nconst mutations = {\n  setUserData (state, data) {\n    localStorage.setItem('account', 'admin')\n    localStorage.setItem('username', 'admin')\n    localStorage.setItem('token', ' data.token')\n    localStorage.setItem('failure_time', Date.parse(new Date()) + 1000 * 50 * 60 * 24)\n    state.account = data.username\n    state.token = data.token\n    state.failure_time = data.failureTime\n  },\n  removeUserData (state) {\n    localStorage.removeItem('account')\n    localStorage.removeItem('token')\n    localStorage.removeItem('failure_time')\n    state.account = ''\n    state.token = ''\n    state.failure_time = ''\n  },\n  setPermissions (state, permissions) {\n    state.permissions = permissions\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  actions,\n  getters,\n  mutations\n}\n"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,6BAA6B;AAE9D,IAAMC,KAAK,GAAG;EACZC,OAAO,EAAEC,YAAY,CAACC,QAAQ;EAC9BC,KAAK,EAAEF,YAAY,CAACE,KAAK;EACzBC,YAAY,EAAEH,YAAY,CAACG,YAAY;EACvCC,WAAW,EAAE;AACf,CAAC;AAED,IAAMC,OAAO,GAAG;EACdC,OAAO,EAAE,SAATA,OAAOA,CAAER,KAAK,EAAI;IAChB,IAAIS,IAAI,GAAG,KAAK;IAChB,IAAIT,KAAK,CAACI,KAAK,IAAI,IAAI,EAAE;MACvB,IAAIM,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC;MACjC,IAAID,IAAI,GAAGV,KAAK,CAACK,YAAY,GAAG,IAAI,EAAE;QACpCI,IAAI,GAAG,IAAI;MACb;IACF;IACA,OAAOA,IAAI;EACb;AACF,CAAC;AAED,IAAMI,OAAO,GAAG;EACdC,KAAK,WAALA,KAAKA,CAAAC,IAAA,EAAcC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACb,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAE5BF,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;MACzBE,OAAO,CAAC;QACNC,SAAS,EAAE,IAAI;QACfC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDC,MAAM,WAANA,MAAMA,CAAAC,KAAA,EAAc;IAAA,IAAVN,MAAM,GAAAM,KAAA,CAANN,MAAM;IACdlB,OAAO,CAAC,CAAC;IACTyB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAO;EAEhC,CAAC;EACD;EACAC,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAqB;IAAA,IAAjB5B,KAAK,GAAA4B,KAAA,CAAL5B,KAAK;MAAEiB,MAAM,GAAAW,KAAA,CAANX,MAAM;IAC7B,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BrB,OAAO,CAAC,CAAC,CAAC+B,IAAI,CAAC,UAAAC,MAAM,EAAI;QACvB,IAAIC,GAAG,GAAG,CACR,mBAAmB,EACnB,mBAAmB,EACnB,iBAAiB,EACjB,mBAAmB,CACpB;QACD,IAAID,MAAM,EAAE;UACVC,GAAG,CAACC,IAAI,CAAC,OAAO,CAAC;UACjB9B,YAAY,CAAC+B,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;QACvC,CAAC,MAAM;UACL/B,YAAY,CAAC+B,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC;QACxC;QAEAhB,MAAM,CAAC,gBAAgB,EAAEc,GAAG,CAAC;QAC7BZ,OAAO,CAACY,GAAG,CAAC;MACd,CAAC,CAAC;IAEJ,CAAC,CAAC;EAEJ;AACF,CAAC;AAED,IAAMG,SAAS,GAAG;EAChBC,WAAW,WAAXA,WAAWA,CAAEnC,KAAK,EAAEgB,IAAI,EAAE;IACxBd,YAAY,CAAC+B,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;IACxC/B,YAAY,CAAC+B,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;IACzC/B,YAAY,CAAC+B,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC;IAC5C/B,YAAY,CAAC+B,OAAO,CAAC,cAAc,EAAEtB,IAAI,CAACC,KAAK,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IAClFX,KAAK,CAACC,OAAO,GAAGe,IAAI,CAACb,QAAQ;IAC7BH,KAAK,CAACI,KAAK,GAAGY,IAAI,CAACZ,KAAK;IACxBJ,KAAK,CAACK,YAAY,GAAGW,IAAI,CAACoB,WAAW;EACvC,CAAC;EACDC,cAAc,WAAdA,cAAcA,CAAErC,KAAK,EAAE;IACrBE,YAAY,CAACoC,UAAU,CAAC,SAAS,CAAC;IAClCpC,YAAY,CAACoC,UAAU,CAAC,OAAO,CAAC;IAChCpC,YAAY,CAACoC,UAAU,CAAC,cAAc,CAAC;IACvCtC,KAAK,CAACC,OAAO,GAAG,EAAE;IAClBD,KAAK,CAACI,KAAK,GAAG,EAAE;IAChBJ,KAAK,CAACK,YAAY,GAAG,EAAE;EACzB,CAAC;EACDkC,cAAc,WAAdA,cAAcA,CAAEvC,KAAK,EAAEM,WAAW,EAAE;IAClCN,KAAK,CAACM,WAAW,GAAGA,WAAW;EACjC;AACF,CAAC;AAED,eAAe;EACbkC,UAAU,EAAE,IAAI;EAChBxC,KAAK,EAALA,KAAK;EACLa,OAAO,EAAPA,OAAO;EACPN,OAAO,EAAPA,OAAO;EACP2B,SAAS,EAATA;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}