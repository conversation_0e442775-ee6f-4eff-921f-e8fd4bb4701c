package com.kum.controller;

import com.alibaba.fastjson2.JSONObject;
import com.kum.domain.AjaxResult;
import com.kum.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 统计数据控制器
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/system/statistics")
public class SysStatisticsController {

    @Autowired
    private SysBuildingService sysBuildingService;
    
    @Autowired
    private SysRoomService sysRoomService;
    
    @Autowired
    private SysUserService sysUserService;
    
    @Autowired
    private SysRepairService sysRepairService;
    
    @Autowired
    private SysComplaintService sysComplaintService;

    /**
     * 获取系统统计数据
     */
    @PreAuthorize("@ps.hasPermi('system:statistics:view')")
    @GetMapping("/overview")
    public AjaxResult getOverview() {
        JSONObject result = new JSONObject();
        
        // 获取各种统计数据
        int buildingCount = sysBuildingService.list().size();
        int roomCount = sysRoomService.list().size();
        int userCount = sysUserService.list().size();
        int repairCount = sysRepairService.list().size();
        int complaintCount = sysComplaintService.list().size();
        
        result.put("buildingCount", buildingCount);
        result.put("roomCount", roomCount);
        result.put("userCount", userCount);
        result.put("repairCount", repairCount);
        result.put("complaintCount", complaintCount);
        
        return AjaxResult.success(result);
    }

    /**
     * 获取最新报修记录
     */
    @PreAuthorize("@ps.hasPermi('system:statistics:view')")
    @GetMapping("/recent/repairs")
    public AjaxResult getRecentRepairs() {
        // 获取最新的5条报修记录
        List<?> recentRepairs = sysRepairService.list();
        // 这里应该添加分页和排序逻辑
        return AjaxResult.success(recentRepairs);
    }

    /**
     * 获取最新投诉记录
     */
    @PreAuthorize("@ps.hasPermi('system:statistics:view')")
    @GetMapping("/recent/complaints")
    public AjaxResult getRecentComplaints() {
        // 获取最新的5条投诉记录
        List<?> recentComplaints = sysComplaintService.list();
        // 这里应该添加分页和排序逻辑
        return AjaxResult.success(recentComplaints);
    }

    /**
     * 获取月度统计数据
     */
    @PreAuthorize("@ps.hasPermi('system:statistics:view')")
    @GetMapping("/monthly")
    public AjaxResult getMonthlyStats() {
        JSONObject result = new JSONObject();
        
        // 这里应该添加按月统计的逻辑
        // 例如：每月报修数量、投诉数量、费用收缴情况等
        
        return AjaxResult.success(result);
    }
}
