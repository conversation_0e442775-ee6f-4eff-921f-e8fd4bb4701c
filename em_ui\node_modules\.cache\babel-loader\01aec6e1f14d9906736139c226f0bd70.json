{"ast": null, "code": "import _slicedToArray from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport area from '@/util/area';\nexport default {\n  name: 'CascaderArea',\n  props: {\n    // 传入数据为中文数组，例如：['河北省', '唐山市', '市辖区']\n    value: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    size: {\n      type: String,\n      default: ''\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      options: area,\n      props: {\n        value: 'code',\n        label: 'name'\n      }\n    };\n  },\n  computed: {\n    myValue: {\n      // 将中文转成 code 码\n      get: function get() {\n        var myValue = [];\n        if (this.value.length === 3) {\n          var _this$value = _slicedToArray(this.value, 3),\n            pName = _this$value[0],\n            cName = _this$value[1],\n            aName = _this$value[2];\n          area.map(function (p) {\n            if (p.name === pName) {\n              myValue.push(p.code);\n              p.children.map(function (c) {\n                if (c.name === cName) {\n                  myValue.push(c.code);\n                  c.children.map(function (a) {\n                    if (a.name === aName) {\n                      myValue.push(a.code);\n                    }\n                  });\n                }\n              });\n            }\n          });\n        }\n        return myValue;\n      },\n      // 将 code 码转成中文\n      set: function set(value) {\n        var _value = _slicedToArray(value, 3),\n          pCode = _value[0],\n          cCode = _value[1],\n          aCode = _value[2];\n        area.map(function (p) {\n          if (p.code === pCode) {\n            pCode = p.name;\n            p.children.map(function (c) {\n              if (c.code === cCode) {\n                cCode = c.name;\n                c.children.map(function (a) {\n                  if (a.code === aCode) {\n                    aCode = a.name;\n                  }\n                });\n              }\n            });\n          }\n        });\n        this.$emit('input', [pCode, cCode, aCode]);\n      }\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AAKA;AAEA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;MACAP;QACAC;QACAO;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACA;QACA;UACA;YAAAC;YAAAC;YAAAC;UACAC;YACA;cACAL;cACAM;gBACA;kBACAN;kBACAO;oBACA;sBACAP;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;QACA;MACA;MACA;MACAQ;QACA;UAAAC;UAAAC;UAAAC;QACAN;UACA;YACAI;YACAH;cACA;gBACAI;gBACAH;kBACA;oBACAI;kBACA;gBACA;cACA;YACA;UACA;QACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "props", "value", "type", "default", "size", "disabled", "data", "options", "label", "computed", "myValue", "get", "pName", "cName", "aName", "area", "p", "c", "set", "pCode", "cCode", "aCode"], "sourceRoot": "src/components/CascaderArea", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <el-cascader v-model=\"myValue\" :options=\"options\" :props=\"props\" :size=\"size\" :disabled=\"disabled\" clearable filterable />\n</template>\n\n<script>\nimport area from '@/util/area'\n\nexport default {\n    name: 'CascaderArea',\n    props: {\n        // 传入数据为中文数组，例如：['河北省', '唐山市', '市辖区']\n        value: {\n            type: Array,\n            default: () => []\n        },\n        size: {\n            type: String,\n            default: ''\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        }\n    },\n    data() {\n        return {\n            options: area,\n            props: {\n                value: 'code',\n                label: 'name'\n            }\n        }\n    },\n    computed: {\n        myValue: {\n            // 将中文转成 code 码\n            get: function() {\n                const myValue = []\n                if (this.value.length === 3) {\n                    const [pName, cName, aName] = this.value\n                    area.map(p => {\n                        if (p.name === pName) {\n                            myValue.push(p.code)\n                            p.children.map(c => {\n                                if (c.name === cName) {\n                                    myValue.push(c.code)\n                                    c.children.map(a => {\n                                        if (a.name === aName) {\n                                            myValue.push(a.code)\n                                        }\n                                    })\n                                }\n                            })\n                        }\n                    })\n                }\n                return myValue\n            },\n            // 将 code 码转成中文\n            set: function(value) {\n                let [pCode, cCode, aCode] = value\n                area.map(p => {\n                    if (p.code === pCode) {\n                        pCode = p.name\n                        p.children.map(c => {\n                            if (c.code === cCode) {\n                                cCode = c.name\n                                c.children.map(a => {\n                                    if (a.code === aCode) {\n                                        aCode = a.name\n                                    }\n                                })\n                            }\n                        })\n                    }\n                })\n                this.$emit('input', [pCode, cCode, aCode])\n            }\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-cascader {\n    width: 100%;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}