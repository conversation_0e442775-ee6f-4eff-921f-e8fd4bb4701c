{"ast": null, "code": "import { getAllIcons } from '@/utils/iconMap';\nexport default {\n  name: 'IconDemo',\n  data: function data() {\n    return {\n      availableIcons: getAllIcons()\n    };\n  }\n};", "map": {"version": 3, "mappings": "AAsFA;AAEA;EACAA;EACAC;IACA;MACAC;IACA;EACA;AACA", "names": ["name", "data", "availableIcons"], "sourceRoot": "src/views/admin/system", "sources": ["icon-demo.vue"], "sourcesContent": ["<template>\n  <div class=\"icon-demo\">\n    <page-header title=\"PNG图标示例\">\n      <template #content>\n        <div>展示项目中可用的PNG图标及其使用方法</div>\n      </template>\n    </page-header>\n    \n    <page-main>\n      <el-card>\n        <div slot=\"header\">\n          <span>可用图标</span>\n        </div>\n        \n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\" v-for=\"icon in availableIcons\" :key=\"icon\">\n            <div class=\"icon-item\">\n              <div class=\"icon-display\">\n                <png-icon :name=\"icon\" size=\"32\" />\n              </div>\n              <div class=\"icon-name\">{{ icon }}</div>\n              <div class=\"icon-usage\">\n                <el-input \n                  :value=\"`<png-icon name='${icon}' size='24' />`\"\n                  readonly\n                  size=\"mini\"\n                />\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </el-card>\n      \n      <el-card style=\"margin-top: 20px;\">\n        <div slot=\"header\">\n          <span>使用示例</span>\n        </div>\n        \n        <div class=\"usage-examples\">\n          <h4>基本用法</h4>\n          <div class=\"example\">\n            <div class=\"demo\">\n              <png-icon name=\"home\" size=\"24\" />\n              <png-icon name=\"building\" size=\"32\" />\n              <png-icon name=\"repair\" size=\"40\" />\n            </div>\n            <div class=\"code\">\n              <pre><code>&lt;png-icon name=\"home\" size=\"24\" /&gt;\n&lt;png-icon name=\"building\" size=\"32\" /&gt;\n&lt;png-icon name=\"repair\" size=\"40\" /&gt;</code></pre>\n            </div>\n          </div>\n          \n          <h4>自定义样式</h4>\n          <div class=\"example\">\n            <div class=\"demo\">\n              <png-icon name=\"notice\" size=\"24\" custom-class=\"custom-icon\" />\n              <png-icon name=\"payment\" size=\"24\" custom-class=\"colored-icon\" />\n            </div>\n            <div class=\"code\">\n              <pre><code>&lt;png-icon name=\"notice\" size=\"24\" custom-class=\"custom-icon\" /&gt;\n&lt;png-icon name=\"payment\" size=\"24\" custom-class=\"colored-icon\" /&gt;</code></pre>\n            </div>\n          </div>\n          \n          <h4>在卡片中使用</h4>\n          <div class=\"example\">\n            <div class=\"demo\">\n              <div class=\"stat-card\">\n                <div class=\"stat-icon\">\n                  <png-icon name=\"dashboard\" size=\"24\" />\n                </div>\n                <div class=\"stat-info\">\n                  <div class=\"stat-number\">123</div>\n                  <div class=\"stat-label\">数据统计</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-card>\n    </page-main>\n  </div>\n</template>\n\n<script>\nimport { getAllIcons } from '@/utils/iconMap'\n\nexport default {\n  name: 'IconDemo',\n  data() {\n    return {\n      availableIcons: getAllIcons()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.icon-demo {\n  .icon-item {\n    text-align: center;\n    padding: 20px;\n    border: 1px solid #e4e7ed;\n    border-radius: 8px;\n    margin-bottom: 20px;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      border-color: #409eff;\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n    }\n    \n    .icon-display {\n      margin-bottom: 10px;\n    }\n    \n    .icon-name {\n      font-weight: bold;\n      margin-bottom: 10px;\n      color: #303133;\n    }\n    \n    .icon-usage {\n      .el-input {\n        font-family: 'Courier New', monospace;\n      }\n    }\n  }\n  \n  .usage-examples {\n    h4 {\n      color: #303133;\n      margin: 20px 0 10px 0;\n    }\n    \n    .example {\n      margin-bottom: 30px;\n      \n      .demo {\n        padding: 20px;\n        border: 1px solid #e4e7ed;\n        border-radius: 8px;\n        margin-bottom: 10px;\n        display: flex;\n        align-items: center;\n        gap: 15px;\n      }\n      \n      .code {\n        background: #f5f7fa;\n        padding: 15px;\n        border-radius: 8px;\n        \n        pre {\n          margin: 0;\n          font-family: 'Courier New', monospace;\n          font-size: 14px;\n          color: #606266;\n        }\n      }\n    }\n    \n    .stat-card {\n      display: flex;\n      align-items: center;\n      padding: 15px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 8px;\n      color: white;\n      width: 200px;\n      \n      .stat-icon {\n        margin-right: 15px;\n        \n        .png-icon {\n          filter: brightness(0) invert(1);\n        }\n      }\n      \n      .stat-info {\n        .stat-number {\n          font-size: 24px;\n          font-weight: bold;\n          line-height: 1;\n        }\n        \n        .stat-label {\n          font-size: 14px;\n          opacity: 0.9;\n          margin-top: 5px;\n        }\n      }\n    }\n  }\n}\n\n// 自定义图标样式示例\n::v-deep .custom-icon {\n  border: 2px solid #409eff;\n  border-radius: 50%;\n  padding: 5px;\n}\n\n::v-deep .colored-icon {\n  filter: hue-rotate(120deg) saturate(2);\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}