module.exports = [
    {
        name: 'vue',
        library: 'Vue',
        js: 'https://cdn.jsdelivr.net/npm/vue@2.6.11/dist/vue.min.js',
        css: ''
    },
    {
        name: 'vue-router',
        library: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        js: 'https://cdn.jsdelivr.net/npm/vue-router@3.3.4/dist/vue-router.min.js',
        css: ''
    },
    {
        name: 'vuex',
        library: 'Vuex',
        js: 'https://cdn.jsdelivr.net/npm/vuex@3.5.1/dist/vuex.min.js',
        css: ''
    },
    {
        name: 'axios',
        library: 'axios',
        js: 'https://cdn.jsdelivr.net/npm/axios@0.19.2/dist/axios.min.js',
        css: ''
    },
    {
        name: 'qs',
        library: 'Qs',
        js: 'https://cdn.jsdelivr.net/npm/qs@6.9.3/dist/qs.js',
        css: ''
    },
    {
        name: 'nprogress',
        library: 'NProgress',
        js: 'https://cdn.jsdelivr.net/npm/nprogress@0.2.0/nprogress.min.js',
        css: 'https://cdn.jsdelivr.net/npm/nprogress@0.2.0/nprogress.css'
    },
    {
        name: 'vue-meta',
        library: 'VueMeta',
        js: 'https://cdn.jsdelivr.net/npm/vue-meta@2.4.0/dist/vue-meta.min.js',
        css: ''
    },
    {
        name: 'js-cookie',
        library: 'Cookies',
        js: 'https://cdn.jsdelivr.net/npm/js-cookie@2.2.1/src/js.cookie.min.js',
        css: ''
    },
    {
        name: 'dayjs',
        library: 'dayjs',
        js: 'https://cdn.jsdelivr.net/npm/dayjs@1.8.29/dayjs.min.js',
        css: ''
    },
    {
        name: 'hotkeys-js',
        library: 'hotkeys',
        js: 'https://cdn.jsdelivr.net/npm/hotkeys-js@3.8.1/dist/hotkeys.min.js',
        css: ''
    }
]
