{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getEstateUser, saveUser, deleteUser } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      loading: false,\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      },\n      table_selectedRowKeys: [],\n      estateUser_query_type: 'name',\n      estateUser_query_buttonTitle: '搜索',\n      estateUser_query_text: '',\n      estateUser_save_title: '新增物业人员',\n      estateUser_save_modalVisible: false,\n      estateUser_form_data: {},\n      estateUser_data_list: []\n    };\n  },\n  created: function created() {\n    this.Get_estateUserDataList();\n  },\n  watch: {\n    estateUser_save_modalVisible: function estateUser_save_modalVisible(val) {\n      if (!val) {\n        this.estateUser_form_data = {};\n      }\n    }\n  },\n  methods: {\n    Get_estateUserDataList: function Get_estateUserDataList() {\n      var _this = this;\n      getEstateUser().then(function (res) {\n        _this.estateUser_query_buttonTitle = '搜索';\n        _this.estateUser_data_list = res.data;\n        _this.estateUser_save_title = '新增物业人员';\n      });\n    },\n    Query_estateUserDataList: function Query_estateUserDataList() {\n      var _this2 = this;\n      var text = this.estateUser_query_text;\n      var temp_list = [];\n      this.estateUser_data_list.forEach(function (item) {\n        if (item[_this2.estateUser_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.estateUser_query_buttonTitle = '返回';\n      this.estateUser_data_list = temp_list;\n    },\n    Edit_estateUserData: function Edit_estateUserData(form) {\n      this.estateUser_save_title = '编辑物业人员';\n      this.estateUser_form_data = JSON.parse(JSON.stringify(form));\n      this.estateUser_save_modalVisible = true;\n    },\n    Del_estateUserData: function Del_estateUserData(id) {\n      var _this3 = this;\n      deleteEstateUser(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_estateUserDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_estateUserData(_this4.estateUser_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_estateUserData: function Save_estateUserData() {\n      var _this5 = this;\n      saveUser(this.estateUser_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.estateUser_save_modalVisible = false;\n        _this5.Get_estateUserDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Change_estateUserStatus: function Change_estateUserStatus(r) {\n      r.status = r.status == 0 ? 1 : 0;\n      this.estateUser_form_data = r;\n      this.Save_estateUserData();\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AAqGA;AACA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAJ;MACA;QACA;MACA;IACA;EACA;EACAK;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;IACAC;MACAC;MACA;MACA;IACA;EACA;AACA", "names": ["data", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "estateUser_query_type", "estateUser_query_buttonTitle", "estateUser_query_text", "estateUser_save_title", "estateUser_save_modalVisible", "estateUser_form_data", "estateUser_data_list", "created", "watch", "methods", "Get_estateUserDataList", "getEstateUser", "_this", "Query_estateUserDataList", "temp_list", "Edit_estateUserData", "Del_estateUserData", "deleteEstateUser", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_estateUserData", "saveUser", "_this5", "Table_selectChange", "Change_estateUserStatus", "r"], "sourceRoot": "src/views/admin/user", "sources": ["estate_user_manager.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"社区物业人员管理\">\n      <div class=\"head\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"单位名称\"\n          v-model=\"estateUser_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"name\">单位名称</a-select-option>\n          <a-select-option value=\"chargePerson\">负责人</a-select-option>\n          <a-select-option value=\"contactPerson\">联系人</a-select-option>\n          <a-select-option value=\"phone\">电话</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"estateUser_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"estateUser_query_buttonTitle\"\n          size=\"large\"\n          @search=\"estateUser_query_buttonTitle == '搜索' ? Query_estateUserDataList() : Get_estateUserDataList()\"\n        />\n        <a-button\n          type=\"primary\"\n          style=\"height: 40px;\"\n          @click=\"estateUser_save_modalVisible = true\"\n        >添加物业人员</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 40px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「物业人员」</a-button>\n      </div>\n      <a-table\n        :data-source=\"estateUser_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"userName\" title=\"用户名\" data-index=\"userName\" />\n        <a-table-column key=\"fullName\" title=\"真实姓名\" data-index=\"fullName\" />\n        <a-table-column key=\"phone\" title=\"联系电话\" data-index=\"phone\" />\n        <a-table-column key=\"status\" title=\"是否启用\" data-index=\"status\">\n          <template slot-scope=\"text, record\">\n            <a-switch v-model=\"record.status == 0\" @change=\"Change_estateUserStatus(record)\" />\n          </template>\n        </a-table-column>\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_estateUserData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_estateUserData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存物业管理人员提示框 -->\n    <a-modal\n      v-model=\"estateUser_save_modalVisible\"\n      :title=\"estateUser_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_estateUserData\"\n    >\n      <a-form-model :model=\"estateUser_form_data\" :label-col=\"labelCol\" :wrapper-col=\"wrapperCol\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"用户名\">\n              <a-input v-model=\"estateUser_form_data.name\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"真实姓名\">\n              <a-input v-model=\"estateUser_form_data.type\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"负责人\">\n              <a-input v-model=\"estateUser_form_data.chargePerson\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"联系电话\">\n              <a-input v-model=\"estateUser_form_data.phone\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getEstateUser, saveUser, deleteUser } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\n\nexport default {\n  data () {\n    return {\n      loading: false,\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n      table_selectedRowKeys: [],\n      estateUser_query_type: 'name',\n      estateUser_query_buttonTitle: '搜索',\n      estateUser_query_text: '',\n      estateUser_save_title: '新增物业人员',\n      estateUser_save_modalVisible: false,\n      estateUser_form_data: {},\n      estateUser_data_list: [],\n    }\n  },\n  created () {\n    this.Get_estateUserDataList()\n  },\n  watch: {\n    estateUser_save_modalVisible (val) {\n      if (!val) {\n        this.estateUser_form_data = {}\n      }\n    }\n  },\n  methods: {\n    Get_estateUserDataList () {\n      getEstateUser().then(res => {\n        this.estateUser_query_buttonTitle = '搜索'\n        this.estateUser_data_list = res.data\n        this.estateUser_save_title = '新增物业人员'\n      })\n    },\n    Query_estateUserDataList () {\n      let text = this.estateUser_query_text\n      let temp_list = []\n      this.estateUser_data_list.forEach(item => {\n        if (item[this.estateUser_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.estateUser_query_buttonTitle = '返回'\n      this.estateUser_data_list = temp_list\n    },\n    Edit_estateUserData (form) {\n      this.estateUser_save_title = '编辑物业人员'\n      this.estateUser_form_data = JSON.parse(JSON.stringify(form))\n      this.estateUser_save_modalVisible = true\n    },\n    Del_estateUserData (id) {\n      deleteEstateUser(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_estateUserDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_estateUserData(this.estateUser_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    Save_estateUserData () {\n      saveUser(this.estateUser_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.estateUser_save_modalVisible = false\n        this.Get_estateUserDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Change_estateUserStatus (r) {\n      r.status = r.status == 0 ? 1 : 0\n      this.estateUser_form_data = r\n      this.Save_estateUserData()\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}