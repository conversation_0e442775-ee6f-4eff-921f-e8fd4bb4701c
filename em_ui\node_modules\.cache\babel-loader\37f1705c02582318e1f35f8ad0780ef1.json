{"ast": null, "code": "export default function (target) {\n  for (var i = 1, j = arguments.length; i < j; i++) {\n    var source = arguments[i] || {};\n    for (var prop in source) {\n      if (source.hasOwnProperty(prop)) {\n        var value = source[prop];\n        if (value !== undefined) {\n          target[prop] = value;\n        }\n      }\n    }\n  }\n  return target;\n}\n;", "map": {"version": 3, "names": ["target", "i", "j", "arguments", "length", "source", "prop", "hasOwnProperty", "value", "undefined"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/src/utils/merge.js"], "sourcesContent": ["export default function(target) {\n  for (let i = 1, j = arguments.length; i < j; i++) {\n    let source = arguments[i] || {};\n    for (let prop in source) {\n      if (source.hasOwnProperty(prop)) {\n        let value = source[prop];\n        if (value !== undefined) {\n          target[prop] = value;\n        }\n      }\n    }\n  }\n\n  return target;\n};\n"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAChD,IAAII,MAAM,GAAGF,SAAS,CAACF,CAAC,CAAC,IAAI,CAAC,CAAC;IAC/B,KAAK,IAAIK,IAAI,IAAID,MAAM,EAAE;MACvB,IAAIA,MAAM,CAACE,cAAc,CAACD,IAAI,CAAC,EAAE;QAC/B,IAAIE,KAAK,GAAGH,MAAM,CAACC,IAAI,CAAC;QACxB,IAAIE,KAAK,KAAKC,SAAS,EAAE;UACvBT,MAAM,CAACM,IAAI,CAAC,GAAGE,KAAK;QACtB;MACF;IACF;EACF;EAEA,OAAOR,MAAM;AACf;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}