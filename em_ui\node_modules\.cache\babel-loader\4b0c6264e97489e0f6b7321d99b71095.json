{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };", "map": {"version": 3, "names": ["_nonIterableSpread", "TypeError", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js"], "sourcesContent": ["function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };"], "mappings": ";AAAA,SAASA,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AACA,SAASD,kBAAkB,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}