{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport { registerUser } from '@/api/requests/rq-manage.js';\nexport default {\n  name: 'Register',\n  data: function data() {\n    var _this = this;\n    var validateConfirmPassword = function validateConfirmPassword(rule, value, callback) {\n      if (value !== _this.registerForm.password) {\n        callback(new Error('两次输入的密码不一致'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      registerForm: {\n        userName: '',\n        fullName: '',\n        phone: '',\n        password: '',\n        confirmPassword: ''\n      },\n      registerRules: {\n        userName: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }, {\n          min: 3,\n          max: 20,\n          message: '用户名长度为3到20位',\n          trigger: 'blur'\n        }],\n        fullName: [{\n          required: true,\n          message: '请输入真实姓名',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 10,\n          message: '姓名长度为2到10位',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          message: '请输入手机号',\n          trigger: 'blur'\n        }, {\n          pattern: /^1[3-9]\\d{9}$/,\n          message: '请输入正确的手机号',\n          trigger: 'blur'\n        }],\n        password: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          max: 20,\n          message: '密码长度为6到20位',\n          trigger: 'blur'\n        }],\n        confirmPassword: [{\n          required: true,\n          message: '请确认密码',\n          trigger: 'blur'\n        }, {\n          validator: validateConfirmPassword,\n          trigger: 'blur'\n        }]\n      },\n      registerLoading: false\n    };\n  },\n  methods: {\n    handleRegister: function handleRegister() {\n      var _this2 = this;\n      var h = this.$createElement;\n      this.$refs.registerForm.validate(function (valid) {\n        if (valid) {\n          _this2.registerLoading = true;\n          var userData = {\n            userName: _this2.registerForm.userName,\n            fullName: _this2.registerForm.fullName,\n            phone: _this2.registerForm.phone,\n            password: _this2.registerForm.password,\n            status: '0' // 正常状态\n          };\n          registerUser(userData).then(function (res) {\n            _this2.registerLoading = false;\n            if (res.code === 200) {\n              _this2.$success({\n                title: '注册成功',\n                content: h(\"div\", [h(\"p\", [\"\\u8D26\\u6237\\u6CE8\\u518C\\u6210\\u529F\\uFF01\"]), h(\"p\", [\"\\u5373\\u5C06\\u8DF3\\u8F6C\\u5230\\u767B\\u5F55\\u9875\\u9762...\"])])\n              });\n              setTimeout(function () {\n                _this2.$router.push('/home');\n              }, 2000);\n            } else {\n              _this2.$error({\n                title: '注册失败',\n                content: res.msg || '注册失败，请重试'\n              });\n            }\n          }).catch(function (error) {\n            _this2.registerLoading = false;\n            _this2.$error({\n              title: '注册失败',\n              content: '网络错误，请重试'\n            });\n            console.error('注册错误:', error);\n          });\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;AA8FA;AAEA;EACAA;EACAC;IAAA;IACA;MACA;QACAC;MACA;QACAA;MACA;IACA;IAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAL,WACA;UAAAM;UAAAC;UAAAC;QAAA,GACA;UAAAC;UAAAC;UAAAH;UAAAC;QAAA,EACA;QACAP,WACA;UAAAK;UAAAC;UAAAC;QAAA,GACA;UAAAC;UAAAC;UAAAH;UAAAC;QAAA,EACA;QACAN,QACA;UAAAI;UAAAC;UAAAC;QAAA,GACA;UAAAG;UAAAJ;UAAAC;QAAA,EACA;QACAL,WACA;UAAAG;UAAAC;UAAAC;QAAA,GACA;UAAAC;UAAAC;UAAAH;UAAAC;QAAA,EACA;QACAJ,kBACA;UAAAE;UAAAC;UAAAC;QAAA,GACA;UAAAI;UAAAJ;QAAA;MAEA;MACAK;IACA;EACA;EACAC;IACAC;MAAA;MAAA;MACA;QACA;UACAC;UAEA;YACAhB;YACAC;YACAC;YACAC;YACAc;UACA;UAEAC;YACAF;YACA;cACAA;gBACAG;gBACAC;cAMA;cAEAC;gBACAL;cACA;YACA;cACAA;gBACAG;gBACAC;cACA;YACA;UACA;YACAJ;YACAA;cACAG;cACAC;YACA;YACAE;UACA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "callback", "registerForm", "userName", "fullName", "phone", "password", "confirmPassword", "registerRules", "required", "message", "trigger", "min", "max", "pattern", "validator", "registerLoading", "methods", "handleRegister", "_this2", "status", "registerUser", "title", "content", "setTimeout", "console"], "sourceRoot": "src/views", "sources": ["register.vue"], "sourcesContent": ["<template>\n  <div class=\"register-container\">\n    <div class=\"bg-banner\" />\n    <div class=\"register-box\">\n      <div class=\"register-form-container\">\n        <div class=\"title-container\">\n          <h3 class=\"title\">用户注册</h3>\n          <p class=\"subtitle\">创建您的账户</p>\n        </div>\n\n        <el-form\n          ref=\"registerForm\"\n          :model=\"registerForm\"\n          :rules=\"registerRules\"\n          class=\"register-form\"\n          auto-complete=\"off\"\n          label-position=\"left\"\n        >\n          <el-form-item prop=\"userName\">\n            <el-input\n              v-model=\"registerForm.userName\"\n              placeholder=\"请输入用户名\"\n              prefix-icon=\"el-icon-user\"\n              size=\"large\"\n              class=\"register-input\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"fullName\">\n            <el-input\n              v-model=\"registerForm.fullName\"\n              placeholder=\"请输入真实姓名\"\n              prefix-icon=\"el-icon-user-solid\"\n              size=\"large\"\n              class=\"register-input\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"phone\">\n            <el-input\n              v-model=\"registerForm.phone\"\n              placeholder=\"请输入手机号\"\n              prefix-icon=\"el-icon-phone\"\n              size=\"large\"\n              class=\"register-input\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"registerForm.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              prefix-icon=\"el-icon-lock\"\n              size=\"large\"\n              class=\"register-input\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"confirmPassword\">\n            <el-input\n              v-model=\"registerForm.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请确认密码\"\n              prefix-icon=\"el-icon-lock\"\n              size=\"large\"\n              class=\"register-input\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item>\n            <el-button\n              type=\"primary\"\n              size=\"large\"\n              class=\"register-button\"\n              :loading=\"registerLoading\"\n              @click=\"handleRegister\"\n            >\n              {{ registerLoading ? '注册中...' : '注册' }}\n            </el-button>\n          </el-form-item>\n\n          <div class=\"login-link\">\n            <p>已有账户？<router-link to=\"/home\">立即登录</router-link></p>\n          </div>\n        </el-form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { registerUser } from '@/api/requests/rq-manage.js'\n\nexport default {\n  name: 'Register',\n  data() {\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.registerForm.password) {\n        callback(new Error('两次输入的密码不一致'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      registerForm: {\n        userName: '',\n        fullName: '',\n        phone: '',\n        password: '',\n        confirmPassword: ''\n      },\n      registerRules: {\n        userName: [\n          { required: true, message: '请输入用户名', trigger: 'blur' },\n          { min: 3, max: 20, message: '用户名长度为3到20位', trigger: 'blur' }\n        ],\n        fullName: [\n          { required: true, message: '请输入真实姓名', trigger: 'blur' },\n          { min: 2, max: 10, message: '姓名长度为2到10位', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入手机号', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度为6到20位', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      registerLoading: false\n    }\n  },\n  methods: {\n    handleRegister() {\n      this.$refs.registerForm.validate((valid) => {\n        if (valid) {\n          this.registerLoading = true\n          \n          const userData = {\n            userName: this.registerForm.userName,\n            fullName: this.registerForm.fullName,\n            phone: this.registerForm.phone,\n            password: this.registerForm.password,\n            status: '0' // 正常状态\n          }\n\n          registerUser(userData).then(res => {\n            this.registerLoading = false\n            if (res.code === 200) {\n              this.$success({\n                title: '注册成功',\n                content: (\n                  <div>\n                    <p>账户注册成功！</p>\n                    <p>即将跳转到登录页面...</p>\n                  </div>\n                )\n              })\n              \n              setTimeout(() => {\n                this.$router.push('/home')\n              }, 2000)\n            } else {\n              this.$error({\n                title: '注册失败',\n                content: res.msg || '注册失败，请重试'\n              })\n            }\n          }).catch(error => {\n            this.registerLoading = false\n            this.$error({\n              title: '注册失败',\n              content: '网络错误，请重试'\n            })\n            console.error('注册错误:', error)\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.register-container {\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n}\n\n.bg-banner {\n  position: absolute;\n  z-index: 0;\n  width: 100%;\n  height: 100%;\n  background-size: cover;\n  background-image: url(../assets/images/login-bg.jpg);\n}\n\n.register-box {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 400px;\n  z-index: 1;\n}\n\n.register-form-container {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 10px;\n  padding: 40px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n}\n\n.title-container {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.title {\n  font-size: 28px;\n  color: #333;\n  margin: 0 0 10px 0;\n  font-weight: bold;\n}\n\n.subtitle {\n  color: #666;\n  margin: 0;\n  font-size: 14px;\n}\n\n.register-form {\n  .register-input {\n    margin-bottom: 20px;\n    \n    ::v-deep .el-input__inner {\n      height: 50px;\n      line-height: 50px;\n      border-radius: 25px;\n      border: 2px solid #e4e7ed;\n      padding-left: 50px;\n      font-size: 16px;\n      transition: all 0.3s;\n      \n      &:focus {\n        border-color: #409eff;\n        box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);\n      }\n    }\n    \n    ::v-deep .el-input__prefix {\n      left: 15px;\n      top: 50%;\n      transform: translateY(-50%);\n    }\n    \n    ::v-deep .el-input__suffix {\n      right: 15px;\n    }\n  }\n}\n\n.register-button {\n  width: 100%;\n  height: 50px;\n  border-radius: 25px;\n  font-size: 18px;\n  font-weight: bold;\n  background: linear-gradient(45deg, #409eff, #67c23a);\n  border: none;\n  transition: all 0.3s;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(64, 158, 255, 0.4);\n  }\n}\n\n.login-link {\n  text-align: center;\n  margin-top: 20px;\n  \n  p {\n    color: #666;\n    margin: 0;\n    \n    a {\n      color: #409eff;\n      text-decoration: none;\n      font-weight: bold;\n      \n      &:hover {\n        text-decoration: underline;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 480px) {\n  .register-box {\n    width: 90%;\n    max-width: 350px;\n  }\n  \n  .register-form-container {\n    padding: 30px 20px;\n  }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}