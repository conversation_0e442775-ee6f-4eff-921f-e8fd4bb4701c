{"ast": null, "code": "import \"core-js/modules/es.number.constructor.js\";\nexport default {\n  name: 'ImagePreview',\n  props: {\n    src: {\n      type: String,\n      required: true\n    },\n    width: {\n      type: [Number, String],\n      default: ''\n    },\n    height: {\n      type: [Number, String],\n      default: ''\n    }\n  },\n  data: function data() {\n    return {\n      dialogVisible: false\n    };\n  },\n  computed: {\n    realWidth: function realWidth() {\n      return typeof this.width == 'string' ? this.width : \"\".concat(this.width, \"px\");\n    },\n    realHeight: function realHeight() {\n      return typeof this.height == 'string' ? this.height : \"\".concat(this.height, \"px\");\n    }\n  },\n  created: function created() {},\n  mounted: function mounted() {},\n  methods: {}\n};", "map": {"version": 3, "mappings": ";AASA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;IACA;EACA;EACAE;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;EACAC;EACAC;AACA", "names": ["name", "props", "src", "type", "required", "width", "default", "height", "data", "dialogVisible", "computed", "realWidth", "realHeight", "created", "mounted", "methods"], "sourceRoot": "src/components/ImagePreview", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <el-image :src=\"src\" fit=\"cover\" :style=\"`width:${realWidth};height:${realHeight};`\" :preview-src-list=\"[src]\">\n        <div slot=\"error\" class=\"image-slot\">\n            <png-icon name=\"404\" size=\"48\" />\n        </div>\n    </el-image>\n</template>\n\n<script>\nexport default {\n    name: 'ImagePreview',\n    props: {\n        src: {\n            type: String,\n            required: true\n        },\n        width: {\n            type: [Number, String],\n            default: ''\n        },\n        height: {\n            type: [Number, String],\n            default: ''\n        }\n    },\n    data() {\n        return {\n            dialogVisible: false\n        }\n    },\n    computed: {\n        realWidth() {\n            return typeof this.width == 'string' ? this.width : `${this.width}px`\n        },\n        realHeight() {\n            return typeof this.height == 'string' ? this.height : `${this.height}px`\n        }\n    },\n    created() {},\n    mounted() {},\n    methods: {}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-image {\n    border-radius: 5px;\n    background-color: #ebeef5;\n    box-shadow: 0 0 5px 1px #ccc;\n    ::v-deep .el-image__inner {\n        transition: all 0.3s;\n        cursor: pointer;\n        &:hover {\n            transform: scale(1.2);\n        }\n    }\n    ::v-deep .image-slot {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 100%;\n        height: 100%;\n        color: #909399;\n        font-size: 30px;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}