{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"header\"\n  }, [_c(\"div\", [_c(\"div\", {\n    staticClass: \"title\"\n  }, [_vm._v(_vm._s(_vm.title))]), _c(\"div\", {\n    staticClass: \"content\"\n  }, [_vm._t(\"content\", function () {\n    return [_vm._v(_vm._s(_vm.content))];\n  })], 2)]), _c(\"div\", [_vm._t(\"default\")], 2)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "title", "_t", "content", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/components/PageHeader/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"header\" }, [\n    _c(\"div\", [\n      _c(\"div\", { staticClass: \"title\" }, [_vm._v(_vm._s(_vm.title))]),\n      _c(\n        \"div\",\n        { staticClass: \"content\" },\n        [\n          _vm._t(\"content\", function () {\n            return [_vm._v(_vm._s(_vm.content))]\n          }),\n        ],\n        2\n      ),\n    ]),\n    _c(\"div\", [_vm._t(\"default\")], 2),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC,EAChEL,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAU,CAAC,EAC1B,CACEH,GAAG,CAACO,EAAE,CAAC,SAAS,EAAE,YAAY;IAC5B,OAAO,CAACP,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC;EACtC,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE,CAACD,GAAG,CAACO,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAClC,CAAC;AACJ,CAAC;AACD,IAAIE,eAAe,GAAG,EAAE;AACxBV,MAAM,CAACW,aAAa,GAAG,IAAI;AAE3B,SAASX,MAAM,EAAEU,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}