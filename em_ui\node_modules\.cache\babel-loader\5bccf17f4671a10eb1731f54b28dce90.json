{"ast": null, "code": "import Vue from 'vue';\nvar scrollBarWidth;\nexport default function () {\n  if (Vue.prototype.$isServer) return 0;\n  if (scrollBarWidth !== undefined) return scrollBarWidth;\n  var outer = document.createElement('div');\n  outer.className = 'el-scrollbar__wrap';\n  outer.style.visibility = 'hidden';\n  outer.style.width = '100px';\n  outer.style.position = 'absolute';\n  outer.style.top = '-9999px';\n  document.body.appendChild(outer);\n  var widthNoScroll = outer.offsetWidth;\n  outer.style.overflow = 'scroll';\n  var inner = document.createElement('div');\n  inner.style.width = '100%';\n  outer.appendChild(inner);\n  var widthWithScroll = inner.offsetWidth;\n  outer.parentNode.removeChild(outer);\n  scrollBarWidth = widthNoScroll - widthWithScroll;\n  return scrollBarWidth;\n}\n;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "scrollBarWidth", "prototype", "$isServer", "undefined", "outer", "document", "createElement", "className", "style", "visibility", "width", "position", "top", "body", "append<PERSON><PERSON><PERSON>", "widthNoScroll", "offsetWidth", "overflow", "inner", "widthWithScroll", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/src/utils/scrollbar-width.js"], "sourcesContent": ["import Vue from 'vue';\n\nlet scrollBarWidth;\n\nexport default function() {\n  if (Vue.prototype.$isServer) return 0;\n  if (scrollBarWidth !== undefined) return scrollBarWidth;\n\n  const outer = document.createElement('div');\n  outer.className = 'el-scrollbar__wrap';\n  outer.style.visibility = 'hidden';\n  outer.style.width = '100px';\n  outer.style.position = 'absolute';\n  outer.style.top = '-9999px';\n  document.body.appendChild(outer);\n\n  const widthNoScroll = outer.offsetWidth;\n  outer.style.overflow = 'scroll';\n\n  const inner = document.createElement('div');\n  inner.style.width = '100%';\n  outer.appendChild(inner);\n\n  const widthWithScroll = inner.offsetWidth;\n  outer.parentNode.removeChild(outer);\n  scrollBarWidth = widthNoScroll - widthWithScroll;\n\n  return scrollBarWidth;\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AAErB,IAAIC,cAAc;AAElB,eAAe,YAAW;EACxB,IAAID,GAAG,CAACE,SAAS,CAACC,SAAS,EAAE,OAAO,CAAC;EACrC,IAAIF,cAAc,KAAKG,SAAS,EAAE,OAAOH,cAAc;EAEvD,IAAMI,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC3CF,KAAK,CAACG,SAAS,GAAG,oBAAoB;EACtCH,KAAK,CAACI,KAAK,CAACC,UAAU,GAAG,QAAQ;EACjCL,KAAK,CAACI,KAAK,CAACE,KAAK,GAAG,OAAO;EAC3BN,KAAK,CAACI,KAAK,CAACG,QAAQ,GAAG,UAAU;EACjCP,KAAK,CAACI,KAAK,CAACI,GAAG,GAAG,SAAS;EAC3BP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,KAAK,CAAC;EAEhC,IAAMW,aAAa,GAAGX,KAAK,CAACY,WAAW;EACvCZ,KAAK,CAACI,KAAK,CAACS,QAAQ,GAAG,QAAQ;EAE/B,IAAMC,KAAK,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC3CY,KAAK,CAACV,KAAK,CAACE,KAAK,GAAG,MAAM;EAC1BN,KAAK,CAACU,WAAW,CAACI,KAAK,CAAC;EAExB,IAAMC,eAAe,GAAGD,KAAK,CAACF,WAAW;EACzCZ,KAAK,CAACgB,UAAU,CAACC,WAAW,CAACjB,KAAK,CAAC;EACnCJ,cAAc,GAAGe,aAAa,GAAGI,eAAe;EAEhD,OAAOnB,cAAc;AACvB;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}