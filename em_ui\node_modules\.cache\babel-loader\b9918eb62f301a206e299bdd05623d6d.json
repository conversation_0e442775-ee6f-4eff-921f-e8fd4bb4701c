{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.object.get-prototype-of.js\");\nexports.__esModule = true;\nexports.i18n = exports.use = exports.t = undefined;\nvar _zhCN = require('element-ui/lib/locale/lang/zh-CN');\nvar _zhCN2 = _interopRequireDefault(_zhCN);\nvar _vue = require('vue');\nvar _vue2 = _interopRequireDefault(_vue);\nvar _deepmerge = require('deepmerge');\nvar _deepmerge2 = _interopRequireDefault(_deepmerge);\nvar _format = require('./format');\nvar _format2 = _interopRequireDefault(_format);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar format = (0, _format2.default)(_vue2.default);\nvar lang = _zhCN2.default;\nvar merged = false;\nvar i18nHandler = function i18nHandler() {\n  var vuei18n = Object.getPrototypeOf(this || _vue2.default).$t;\n  if (typeof vuei18n === 'function' && !!_vue2.default.locale) {\n    if (!merged) {\n      merged = true;\n      _vue2.default.locale(_vue2.default.config.lang, (0, _deepmerge2.default)(lang, _vue2.default.locale(_vue2.default.config.lang) || {}, {\n        clone: true\n      }));\n    }\n    return vuei18n.apply(this, arguments);\n  }\n};\nvar t = exports.t = function t(path, options) {\n  var value = i18nHandler.apply(this, arguments);\n  if (value !== null && value !== undefined) return value;\n  var array = path.split('.');\n  var current = lang;\n  for (var i = 0, j = array.length; i < j; i++) {\n    var property = array[i];\n    value = current[property];\n    if (i === j - 1) return format(value, options);\n    if (!value) return '';\n    current = value;\n  }\n  return '';\n};\nvar use = exports.use = function use(l) {\n  lang = l || lang;\n};\nvar i18n = exports.i18n = function i18n(fn) {\n  i18nHandler = fn || i18nHandler;\n};\nexports.default = {\n  use: use,\n  t: t,\n  i18n: i18n\n};", "map": {"version": 3, "names": ["require", "exports", "__esModule", "i18n", "use", "t", "undefined", "_zhCN", "_zhCN2", "_interopRequireDefault", "_vue", "_vue2", "_deepmerge", "_deepmerge2", "_format", "_format2", "obj", "default", "format", "lang", "merged", "i18nHandler", "vuei18n", "Object", "getPrototypeOf", "$t", "locale", "config", "clone", "apply", "arguments", "path", "options", "value", "array", "split", "current", "i", "j", "length", "property", "l", "fn"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/element-ui/lib/locale/index.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\nexports.i18n = exports.use = exports.t = undefined;\n\nvar _zhCN = require('element-ui/lib/locale/lang/zh-CN');\n\nvar _zhCN2 = _interopRequireDefault(_zhCN);\n\nvar _vue = require('vue');\n\nvar _vue2 = _interopRequireDefault(_vue);\n\nvar _deepmerge = require('deepmerge');\n\nvar _deepmerge2 = _interopRequireDefault(_deepmerge);\n\nvar _format = require('./format');\n\nvar _format2 = _interopRequireDefault(_format);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar format = (0, _format2.default)(_vue2.default);\nvar lang = _zhCN2.default;\nvar merged = false;\nvar i18nHandler = function i18nHandler() {\n  var vuei18n = Object.getPrototypeOf(this || _vue2.default).$t;\n  if (typeof vuei18n === 'function' && !!_vue2.default.locale) {\n    if (!merged) {\n      merged = true;\n      _vue2.default.locale(_vue2.default.config.lang, (0, _deepmerge2.default)(lang, _vue2.default.locale(_vue2.default.config.lang) || {}, { clone: true }));\n    }\n    return vuei18n.apply(this, arguments);\n  }\n};\n\nvar t = exports.t = function t(path, options) {\n  var value = i18nHandler.apply(this, arguments);\n  if (value !== null && value !== undefined) return value;\n\n  var array = path.split('.');\n  var current = lang;\n\n  for (var i = 0, j = array.length; i < j; i++) {\n    var property = array[i];\n    value = current[property];\n    if (i === j - 1) return format(value, options);\n    if (!value) return '';\n    current = value;\n  }\n  return '';\n};\n\nvar use = exports.use = function use(l) {\n  lang = l || lang;\n};\n\nvar i18n = exports.i18n = function i18n(fn) {\n  i18nHandler = fn || i18nHandler;\n};\n\nexports.default = { use: use, t: t, i18n: i18n };"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,IAAI,GAAGF,OAAO,CAACG,GAAG,GAAGH,OAAO,CAACI,CAAC,GAAGC,SAAS;AAElD,IAAIC,KAAK,GAAGP,OAAO,CAAC,kCAAkC,CAAC;AAEvD,IAAIQ,MAAM,GAAGC,sBAAsB,CAACF,KAAK,CAAC;AAE1C,IAAIG,IAAI,GAAGV,OAAO,CAAC,KAAK,CAAC;AAEzB,IAAIW,KAAK,GAAGF,sBAAsB,CAACC,IAAI,CAAC;AAExC,IAAIE,UAAU,GAAGZ,OAAO,CAAC,WAAW,CAAC;AAErC,IAAIa,WAAW,GAAGJ,sBAAsB,CAACG,UAAU,CAAC;AAEpD,IAAIE,OAAO,GAAGd,OAAO,CAAC,UAAU,CAAC;AAEjC,IAAIe,QAAQ,GAAGN,sBAAsB,CAACK,OAAO,CAAC;AAE9C,SAASL,sBAAsBA,CAACO,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACd,UAAU,GAAGc,GAAG,GAAG;IAAEC,OAAO,EAAED;EAAI,CAAC;AAAE;AAE9F,IAAIE,MAAM,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAACE,OAAO,EAAEN,KAAK,CAACM,OAAO,CAAC;AACjD,IAAIE,IAAI,GAAGX,MAAM,CAACS,OAAO;AACzB,IAAIG,MAAM,GAAG,KAAK;AAClB,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvC,IAAIC,OAAO,GAAGC,MAAM,CAACC,cAAc,CAAC,IAAI,IAAIb,KAAK,CAACM,OAAO,CAAC,CAACQ,EAAE;EAC7D,IAAI,OAAOH,OAAO,KAAK,UAAU,IAAI,CAAC,CAACX,KAAK,CAACM,OAAO,CAACS,MAAM,EAAE;IAC3D,IAAI,CAACN,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI;MACbT,KAAK,CAACM,OAAO,CAACS,MAAM,CAACf,KAAK,CAACM,OAAO,CAACU,MAAM,CAACR,IAAI,EAAE,CAAC,CAAC,EAAEN,WAAW,CAACI,OAAO,EAAEE,IAAI,EAAER,KAAK,CAACM,OAAO,CAACS,MAAM,CAACf,KAAK,CAACM,OAAO,CAACU,MAAM,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;QAAES,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;IACzJ;IACA,OAAON,OAAO,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;AACF,CAAC;AAED,IAAIzB,CAAC,GAAGJ,OAAO,CAACI,CAAC,GAAG,SAASA,CAACA,CAAC0B,IAAI,EAAEC,OAAO,EAAE;EAC5C,IAAIC,KAAK,GAAGZ,WAAW,CAACQ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC9C,IAAIG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK3B,SAAS,EAAE,OAAO2B,KAAK;EAEvD,IAAIC,KAAK,GAAGH,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC;EAC3B,IAAIC,OAAO,GAAGjB,IAAI;EAElB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC5C,IAAIG,QAAQ,GAAGN,KAAK,CAACG,CAAC,CAAC;IACvBJ,KAAK,GAAGG,OAAO,CAACI,QAAQ,CAAC;IACzB,IAAIH,CAAC,KAAKC,CAAC,GAAG,CAAC,EAAE,OAAOpB,MAAM,CAACe,KAAK,EAAED,OAAO,CAAC;IAC9C,IAAI,CAACC,KAAK,EAAE,OAAO,EAAE;IACrBG,OAAO,GAAGH,KAAK;EACjB;EACA,OAAO,EAAE;AACX,CAAC;AAED,IAAI7B,GAAG,GAAGH,OAAO,CAACG,GAAG,GAAG,SAASA,GAAGA,CAACqC,CAAC,EAAE;EACtCtB,IAAI,GAAGsB,CAAC,IAAItB,IAAI;AAClB,CAAC;AAED,IAAIhB,IAAI,GAAGF,OAAO,CAACE,IAAI,GAAG,SAASA,IAAIA,CAACuC,EAAE,EAAE;EAC1CrB,WAAW,GAAGqB,EAAE,IAAIrB,WAAW;AACjC,CAAC;AAEDpB,OAAO,CAACgB,OAAO,GAAG;EAAEb,GAAG,EAAEA,GAAG;EAAEC,CAAC,EAAEA,CAAC;EAAEF,IAAI,EAAEA;AAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}