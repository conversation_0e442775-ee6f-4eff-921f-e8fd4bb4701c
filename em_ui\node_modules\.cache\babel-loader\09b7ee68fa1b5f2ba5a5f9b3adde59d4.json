{"ast": null, "code": "export default {\n  mounted: function mounted() {\n    this.$router.go(-1);\n  }\n};", "map": {"version": 3, "mappings": "AAKA;EACAA;IACA;EACA;AACA", "names": ["mounted"], "sourceRoot": "src/views", "sources": ["reload.vue"], "sourcesContent": ["<template>\n    <div />\n</template>\n\n<script>\nexport default {\n    mounted() {\n        this.$router.go(-1)\n    }\n}\n</script>\n"]}, "metadata": {}, "sourceType": "module"}