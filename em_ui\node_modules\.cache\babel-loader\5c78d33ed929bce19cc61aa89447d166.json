{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport _variables from '@/assets/styles/resources/variables.scss';\nimport Logo from './components/Logo';\nimport UserMenu from './components/UserMenu';\nimport SidebarItem from './components/SidebarItem';\nimport Topbar from './components/Topbar';\nimport Search from './components/Search';\nimport ThemeSetting from './components/ThemeSetting';\nexport default {\n  name: 'Layout',\n  components: {\n    Logo: Logo,\n    Search: Search,\n    SidebarItem: SidebarItem,\n    Topbar: Topbar,\n    ThemeSetting: ThemeSetting,\n    UserMenu: UserMenu\n  },\n  provide: function provide() {\n    return {\n      reload: this.reload\n    };\n  },\n  data: function data() {\n    return {\n      isRouterAlive: true,\n      routePath: '',\n      sidebarScrollTop: 0,\n      scrollTop: 0\n    };\n  },\n  computed: {\n    variables: function variables() {\n      return _variables;\n    },\n    realSidebarWidth: function realSidebarWidth() {\n      var realSidebarWidth = 0;\n      if (this.$store.state.settings.mode == 'pc') {\n        if (!this.$store.state.settings.showHeader && this.$store.state.menu.routes.length > 1) {\n          realSidebarWidth = parseInt(_variables.g_main_sidebar_width);\n        }\n        if (this.$store.state.settings.sidebarCollapse) {\n          realSidebarWidth += 64;\n        } else {\n          realSidebarWidth += parseInt(_variables.g_sub_sidebar_width);\n        }\n      } else {\n        realSidebarWidth = parseInt(_variables.g_main_sidebar_width) + parseInt(_variables.g_sub_sidebar_width);\n      }\n      return \"\".concat(realSidebarWidth, \"px\");\n    }\n  },\n  watch: {\n    $route: 'routeChange',\n    '$store.state.settings.sidebarCollapse': function $storeStateSettingsSidebarCollapse(val) {\n      if (this.$store.state.settings.mode == 'mobile') {\n        if (!val) {\n          document.querySelector('body').classList.add('hidden');\n        } else {\n          document.querySelector('body').classList.remove('hidden');\n        }\n      }\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.$hotkeys('alt+s', function (e) {\n      if (_this.$store.state.settings.enableNavSearch) {\n        e.preventDefault();\n        _this.$eventBus.$emit('global-search-toggle');\n      }\n    });\n    this.$hotkeys('f5', function (e) {\n      if (_this.$store.state.settings.enablePageReload) {\n        e.preventDefault();\n        _this.reload(2);\n      }\n    });\n    window.addEventListener('scroll', this.onScroll);\n  },\n  destroyed: function destroyed() {\n    window.removeEventListener('scroll', this.onScroll);\n  },\n  methods: {\n    reload: function reload() {\n      var _this2 = this;\n      var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1;\n      if (type == 1) {\n        this.isRouterAlive = false;\n        this.$nextTick(function () {\n          return _this2.isRouterAlive = true;\n        });\n      } else {\n        this.$router.push({\n          name: 'reload'\n        });\n      }\n    },\n    routeChange: function routeChange(newVal, oldVal) {\n      if (newVal.name == oldVal.name) {\n        this.reload();\n      }\n    },\n    onSidebarScroll: function onSidebarScroll(e) {\n      this.sidebarScrollTop = e.target.scrollTop;\n    },\n    onScroll: function onScroll() {\n      this.scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;AA+HA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;UACAA;QACA;QACA;UACAA;QACA;UACAA;QACA;MACA;QACAA;MACA;MACA;IACA;EACA;EACAC;IACAC;IACA;MACA;QACA;UACAC;QACA;UACAA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;QACAC;QACAC;MACA;IACA;IACA;MACA;QACAD;QACAC;MACA;IACA;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IACAjB;MAAA;MAAA;MACA;QACA;QACA;UAAA;QAAA;MACA;QACA;UACAT;QACA;MACA;IACA;IACA2B;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA", "names": ["name", "components", "Logo", "Search", "SidebarItem", "Topbar", "ThemeSetting", "UserMenu", "provide", "reload", "data", "isRouterAlive", "routePath", "sidebarScrollTop", "scrollTop", "computed", "variables", "realSidebarWidth", "watch", "$route", "document", "mounted", "e", "_this", "window", "destroyed", "methods", "routeChange", "onSidebarScroll", "onScroll"], "sourceRoot": "src/layout", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"layout\">\n    <div id=\"app-main\" :style=\"{\n            '--real-sidebar-width': realSidebarWidth\n        }\">\n      <header v-if=\"$store.state.settings.mode == 'pc' && $store.state.settings.showHeader\">\n        <div class=\"header-container\">\n          <div class=\"main\">\n            <Logo />\n            <!-- 当头部导航大于 1 个的时候才会显示 -->\n            <div v-if=\"$store.state.menu.routes.length > 1\" class=\"nav\">\n              <template v-for=\"(item, index) in $store.state.menu.routes\">\n                <div\n                  v-if=\"item.children && item.children.length !== 0\"\n                  :key=\"index\"\n                  :class=\"{\n                                    'item': true,\n                                    'active': index == $store.state.menu.headerActived\n                                }\"\n                  @click=\"$store.commit('menu/switchHeaderActived', index)\"\n                >\n                  <svg-icon v-if=\"item.meta.icon\" :name=\"item.meta.icon\" />\n                  <span v-if=\"item.meta.title\">{{ item.meta.title }}</span>\n                </div>\n              </template>\n            </div>\n          </div>\n          <UserMenu />\n        </div>\n      </header>\n      <div class=\"wrapper\">\n        <div\n          :class=\"{\n                    'sidebar-container': true,\n                    'show': $store.state.settings.mode == 'mobile' && !$store.state.settings.sidebarCollapse\n                }\"\n        >\n          <div\n            v-if=\"(!$store.state.settings.showHeader || $store.state.settings.mode == 'mobile') && $store.state.menu.routes.length > 1\"\n            class=\"main-sidebar-container\"\n          >\n            <Logo :show-title=\"false\" class=\"sidebar-logo\" />\n            <div class=\"nav\">\n              <template v-for=\"(item, index) in $store.state.menu.routes\">\n                <div\n                  v-if=\"item.children && item.children.length !== 0\"\n                  :key=\"index\"\n                  :class=\"{\n                                    'item': true,\n                                    'active': index == $store.state.menu.headerActived\n                                }\"\n                  :title=\"item.meta.title\"\n                  @click=\"$store.commit('menu/switchHeaderActived', index)\"\n                >\n                  <svg-icon v-if=\"item.meta.icon\" :name=\"item.meta.icon\" />\n                  <span>{{ item.meta.title }}</span>\n                </div>\n              </template>\n            </div>\n          </div>\n          <div\n            :class=\"{\n                        'sub-sidebar-container': true,\n                        'is-collapse': $store.state.settings.mode == 'pc' && $store.state.settings.sidebarCollapse\n                    }\"\n            @scroll=\"onSidebarScroll\"\n          >\n            <Logo\n              :show-logo=\"$store.state.menu.routes.length <= 1\"\n              :class=\"{\n                            'sidebar-logo': true,\n                            'sidebar-logo-bg': $store.state.menu.routes.length <= 1,\n                            'shadow': sidebarScrollTop\n                        }\"\n            />\n            <el-menu\n              :background-color=\"variables.g_sub_sidebar_bg\"\n              :text-color=\"variables.g_sub_sidebar_menu_color\"\n              :active-text-color=\"variables.g_sub_sidebar_menu_active_color\"\n              unique-opened\n              :default-active=\"$route.meta.activeMenu || $route.path\"\n              :collapse=\"$store.state.settings.mode == 'pc' && $store.state.settings.sidebarCollapse\"\n              :collapse-transition=\"false\"\n              :class=\"{\n                            'is-collapse-without-logo': $store.state.menu.routes.length > 1 && $store.state.settings.mode == 'pc' && $store.state.settings.sidebarCollapse\n                        }\"\n            >\n              <transition-group name=\"sidebar\">\n                <template v-for=\"route in $store.getters['menu/sidebarRoutes']\">\n                  <SidebarItem\n                    v-if=\"route.meta.sidebar !== false\"\n                    :key=\"route.path\"\n                    :item=\"route\"\n                    :base-path=\"route.path\"\n                  />\n                </template>\n              </transition-group>\n            </el-menu>\n          </div>\n        </div>\n        <div\n          :class=\"{\n                    'sidebar-mask': true,\n                    'show': $store.state.settings.mode == 'mobile' && !$store.state.settings.sidebarCollapse\n                }\"\n          @click=\"$store.commit('settings/toggleSidebarCollapse')\"\n        />\n        <div class=\"main-container\">\n          <Topbar :class=\"{'shadow': scrollTop}\" />\n          <div class=\"main\">\n            <transition name=\"main\" mode=\"out-in\">\n              <keep-alive v-if=\"isRouterAlive\" :include=\"$store.state.keepAlive.list\">\n                <RouterView :key=\"$route.path\" />\n              </keep-alive>\n            </transition>\n          </div>\n          <Copyright v-if=\"$store.state.settings.showCopyright\" />\n        </div>\n      </div>\n      <el-backtop :right=\"20\" :bottom=\"20\" title=\"回到顶部\" />\n    </div>\n    <Search />\n    <ThemeSetting />\n  </div>\n</template>\n\n<script>\nimport variables from '@/assets/styles/resources/variables.scss'\nimport Logo from './components/Logo'\nimport UserMenu from './components/UserMenu'\nimport SidebarItem from './components/SidebarItem'\nimport Topbar from './components/Topbar'\nimport Search from './components/Search'\nimport ThemeSetting from './components/ThemeSetting'\n\nexport default {\n  name: 'Layout',\n  components: {\n    Logo,\n    Search,\n    SidebarItem,\n    Topbar,\n    ThemeSetting,\n    UserMenu,\n  },\n  provide () {\n    return {\n      reload: this.reload\n    }\n  },\n  data () {\n    return {\n      isRouterAlive: true,\n      routePath: '',\n      sidebarScrollTop: 0,\n      scrollTop: 0\n    }\n  },\n  computed: {\n    variables () {\n      return variables\n    },\n    realSidebarWidth () {\n      let realSidebarWidth = 0\n      if (this.$store.state.settings.mode == 'pc') {\n        if (!this.$store.state.settings.showHeader && this.$store.state.menu.routes.length > 1) {\n          realSidebarWidth = parseInt(variables.g_main_sidebar_width)\n        }\n        if (this.$store.state.settings.sidebarCollapse) {\n          realSidebarWidth += 64\n        } else {\n          realSidebarWidth += parseInt(variables.g_sub_sidebar_width)\n        }\n      } else {\n        realSidebarWidth = parseInt(variables.g_main_sidebar_width) + parseInt(variables.g_sub_sidebar_width)\n      }\n      return `${realSidebarWidth}px`\n    }\n  },\n  watch: {\n    $route: 'routeChange',\n    '$store.state.settings.sidebarCollapse' (val) {\n      if (this.$store.state.settings.mode == 'mobile') {\n        if (!val) {\n          document.querySelector('body').classList.add('hidden')\n        } else {\n          document.querySelector('body').classList.remove('hidden')\n        }\n      }\n    }\n  },\n  mounted () {\n    this.$hotkeys('alt+s', e => {\n      if (this.$store.state.settings.enableNavSearch) {\n        e.preventDefault()\n        this.$eventBus.$emit('global-search-toggle')\n      }\n    })\n    this.$hotkeys('f5', e => {\n      if (this.$store.state.settings.enablePageReload) {\n        e.preventDefault()\n        this.reload(2)\n      }\n    })\n    window.addEventListener('scroll', this.onScroll)\n  },\n  destroyed () {\n    window.removeEventListener('scroll', this.onScroll)\n  },\n  methods: {\n    reload (type = 1) {\n      if (type == 1) {\n        this.isRouterAlive = false\n        this.$nextTick(() => (this.isRouterAlive = true))\n      } else {\n        this.$router.push({\n          name: 'reload'\n        })\n      }\n    },\n    routeChange (newVal, oldVal) {\n      if (newVal.name == oldVal.name) {\n        this.reload()\n      }\n    },\n    onSidebarScroll (e) {\n      this.sidebarScrollTop = e.target.scrollTop\n    },\n    onScroll () {\n      this.scrollTop = document.documentElement.scrollTop || document.body.scrollTop\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n[data-mode=\"mobile\"] {\n    .sidebar-container {\n        transition: 0.3s;\n        transform: translateX(calc(-1 * #{$g-sidebar-width}));\n        &.show {\n            transform: translateX(0);\n        }\n    }\n    .main-container {\n        margin-left: 0 !important;\n    }\n}\n.layout {\n    height: 100%;\n}\n#app-main {\n    width: 100%;\n    height: 100%;\n    margin: 0 auto;\n    transition: all 0.2s;\n}\nheader {\n    position: fixed;\n    z-index: 1000;\n    top: 0;\n    left: 0;\n    right: 0;\n    display: flex;\n    align-items: center;\n    padding: 0 20px;\n    height: $g-header-height;\n    background-color: $g-header-bg;\n    color: $g-header-menu-color;\n    .header-container {\n        width: $g-header-width;\n        margin: 0 auto;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        .main {\n            display: flex;\n            align-items: center;\n        }\n    }\n    @media screen and (max-width: $g-header-width) {\n        .header-container {\n            width: 100%;\n        }\n    }\n    ::v-deep .title {\n        position: relative;\n        width: inherit;\n        height: inherit;\n        padding: inherit;\n        background-color: inherit;\n        .logo {\n            width: 50px;\n            height: 50px;\n        }\n        span {\n            font-size: 24px;\n            letter-spacing: 1px;\n        }\n    }\n    .nav {\n        display: flex;\n        margin-left: 50px;\n        .item {\n            margin: 0 10px;\n            padding: 10px;\n            border-radius: 10px;\n            cursor: pointer;\n            transition: all 0.3s;\n            &.active,\n            &:hover {\n                background-color: $g-header-menu-active-bg;\n            }\n            .svg-icon {\n                font-size: 20px;\n                & + span {\n                    margin-left: 5px;\n                }\n            }\n        }\n    }\n    ::v-deep .user {\n        padding: 0;\n        .user-container {\n            color: #fff;\n            font-size: 16px;\n        }\n    }\n}\n.wrapper {\n    position: relative;\n    width: 100%;\n    height: 100%;\n    .sidebar-container {\n        position: fixed;\n        z-index: 1010;\n        top: 0;\n        bottom: 0;\n        display: flex;\n        width: var(--real-sidebar-width);\n    }\n    .sidebar-mask {\n        position: fixed;\n        z-index: 1000;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-color: rgba($color: #000, $alpha: 0.5);\n        backdrop-filter: blur(2px);\n        transition: all 0.2s;\n        transform: translateZ(0);\n        opacity: 0;\n        visibility: hidden;\n        &.show {\n            opacity: 1;\n            visibility: visible;\n        }\n    }\n    .main-sidebar-container,\n    .sub-sidebar-container {\n        transition: 0.3s;\n        overflow-x: hidden;\n        overflow-y: auto;\n        overscroll-behavior: contain;\n        // firefox隐藏滚动条\n        scrollbar-width: none;\n        // chrome隐藏滚动条\n        &::-webkit-scrollbar {\n            display: none;\n        }\n    }\n    .main-sidebar-container {\n        width: $g-main-sidebar-width;\n        background-color: $g-main-sidebar-bg;\n        color: #fff;\n        .sidebar-logo {\n            transition: 0.3s;\n            background-color: $g-main-sidebar-bg;\n        }\n        .nav {\n            width: inherit;\n            padding-top: $g-sidebar-logo-height;\n            .item {\n                display: flex;\n                flex-direction: column;\n                justify-content: center;\n                height: 60px;\n                padding: 0 5px;\n                cursor: pointer;\n                transition: 0.3s;\n                &.active,\n                &:hover {\n                    background-color: $g-main-sidebar-menu-active-bg;\n                }\n                .svg-icon {\n                    margin: 0 auto;\n                    font-size: 20px;\n                }\n                span {\n                    text-align: center;\n                    font-size: 14px;\n                    @include text-overflow;\n                }\n            }\n        }\n    }\n    .sub-sidebar-container {\n        width: $g-sub-sidebar-width;\n        background-color: $g-sub-sidebar-bg;\n        position: absolute;\n        left: 0;\n        top: 0;\n        bottom: 0;\n        box-shadow: 10px 0 10px -10px darken($g-sub-sidebar-bg, 20);\n        &.is-collapse {\n            width: 64px;\n            .sidebar-logo {\n                &:not(.sidebar-logo-bg) {\n                    display: none;\n                }\n                ::v-deep span {\n                    display: none;\n                }\n            }\n        }\n        .sidebar-logo {\n            background: $g-sub-sidebar-bg;\n            transition: box-shadow 0.2s, background-color 0.3s, color 0.3s;\n            &:not(.sidebar-logo-bg) {\n                ::v-deep span {\n                    color: $g-sub-sidebar-menu-color;\n                }\n            }\n            &.sidebar-logo-bg {\n                background: $g-main-sidebar-bg;\n            }\n            &.shadow {\n                box-shadow: 0 10px 10px -10px darken($g-sub-sidebar-bg, 20);\n            }\n        }\n        .el-menu {\n            border-right: 0;\n            padding-top: $g-sidebar-logo-height;\n            transition: border-color 0.3s, background-color 0.3s, color 0.3s;\n            &:not(.el-menu--collapse) {\n                width: inherit;\n            }\n            &.is-collapse-without-logo {\n                padding-top: 0;\n            }\n            &.el-menu--collapse {\n                ::v-deep .svg-icon {\n                    margin-right: 0;\n                }\n                ::v-deep .el-menu-item,\n                ::v-deep .el-submenu__title {\n                    span {\n                        display: none;\n                    }\n                    i {\n                        right: 7px;\n                        margin-top: -5px;\n                    }\n                }\n            }\n        }\n    }\n    .main-sidebar-container + .sub-sidebar-container {\n        left: $g-main-sidebar-width;\n    }\n    .main-container {\n        display: flex;\n        flex-direction: column;\n        min-height: 100%;\n        margin-left: $g-sidebar-width;\n        background-color: #f5f7f9;\n        box-shadow: 1px 0 0 0 darken($g-main-bg, 10);\n        transition: 0.3s;\n        .main {\n            height: 100%;\n            flex: auto;\n            position: relative;\n            padding: $g-topbar-height 0 0;\n            overflow: hidden;\n        }\n    }\n}\nheader + .wrapper {\n    padding-top: $g-header-height;\n    .sidebar-container {\n        top: $g-header-height;\n        .sidebar-logo {\n            display: none;\n        }\n        .el-menu {\n            padding-top: 0;\n        }\n    }\n    .main-container {\n        .topbar-container {\n            top: $g-header-height;\n            ::v-deep .user {\n                display: none;\n            }\n        }\n    }\n}\n\n// 侧边栏动画\n.sidebar-enter-active {\n    transition: all 0.2s;\n}\n.sidebar-enter,\n.sidebar-leave-active {\n    opacity: 0;\n    transform: translateY(20px);\n}\n.sidebar-leave-active {\n    position: absolute;\n}\n\n// 主内容区动画\n.main-enter-active {\n    transition: all 0.2s;\n}\n.main-leave-active {\n    transition: all 0.15s;\n}\n.main-enter {\n    opacity: 0;\n    margin-left: -20px;\n}\n.main-leave-to {\n    opacity: 0;\n    margin-left: 20px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}