{"ast": null, "code": "export default {\n  name: 'BatchAction<PERSON><PERSON>',\n  props: {\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    selectionData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  data: function data() {\n    return {};\n  },\n  computed: {\n    checkAll: {\n      get: function get() {\n        var flag = false;\n        if (this.data.length != 0 && this.data.length == this.selectionData.length) {\n          flag = true;\n        }\n        return flag;\n      },\n      set: function set(newVal) {\n        newVal ? this.$emit('check-all') : this.$emit('check-null');\n      }\n    },\n    isIndeterminate: function isIndeterminate() {\n      var flag = false;\n      if (this.selectionData.length > 0 && this.selectionData.length < this.data.length) {\n        flag = true;\n      }\n      return flag;\n    }\n  },\n  created: function created() {},\n  mounted: function mounted() {},\n  methods: {}\n};", "map": {"version": 3, "mappings": "AAWA;EACAA;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;QAAA;MAAA;IACA;EACA;EACAF;IACA;EACA;EACAI;IACAC;MACAC;QACA;QACA;UACAC;QACA;QACA;MACA;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;QACAH;MACA;MACA;IACA;EACA;EACAI;EACAC;EACAC;AACA", "names": ["name", "props", "data", "type", "default", "selectionData", "computed", "checkAll", "get", "flag", "set", "newVal", "isIndeterminate", "created", "mounted", "methods"], "sourceRoot": "src/components/BatchActionBar", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"batch-action-bar\">\n        <el-checkbox v-model=\"checkAll\" :indeterminate=\"isIndeterminate\" :disabled=\"!data.length\">当页全选</el-checkbox>\n        <div v-if=\"selectionData.length\" class=\"tips\">已选 {{ selectionData.length }} 项</div>\n        <el-form :disabled=\"!selectionData.length\">\n            <slot />\n        </el-form>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'BatchActionBar',\n    props: {\n        data: {\n            type: Array,\n            default: () => []\n        },\n        selectionData: {\n            type: Array,\n            default: () => []\n        }\n    },\n    data() {\n        return {}\n    },\n    computed: {\n        checkAll: {\n            get: function() {\n                let flag = false\n                if (this.data.length != 0 && this.data.length == this.selectionData.length) {\n                    flag = true\n                }\n                return flag\n            },\n            set: function(newVal) {\n                newVal ? this.$emit('check-all') : this.$emit('check-null')\n            }\n        },\n        isIndeterminate() {\n            let flag = false\n            if (this.selectionData.length > 0 && this.selectionData.length < this.data.length) {\n                flag = true\n            }\n            return flag\n        }\n    },\n    created() {},\n    mounted() {},\n    methods: {}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.batch-action-bar {\n    display: flex;\n    align-items: center;\n    margin: 20px 0;\n    padding: 0 10px;\n    &:first-child {\n        margin-top: 0;\n    }\n    .el-checkbox {\n        margin-right: 20px;\n    }\n    .tips {\n        margin-right: 20px;\n        color: #909399;\n        font-size: 13px;\n    }\n    ::v-deep .el-form {\n        > .el-button {\n            margin-right: 10px;\n        }\n        > .el-button-group {\n            margin-right: 10px;\n        }\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}