{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm.item.meta.sidebar !== false ? _c(\"div\", [!_vm.hasChildren ? _c(\"router-link\", {\n    attrs: {\n      custom: \"\",\n      to: _vm.resolvePath(_vm.item.path)\n    },\n    scopedSlots: _vm._u([{\n      key: \"default\",\n      fn: function fn(_ref) {\n        var href = _ref.href,\n          navigate = _ref.navigate,\n          isActive = _ref.isActive,\n          isExactActive = _ref.isExactActive;\n        return [_c(\"a\", {\n          class: [isActive && \"router-link-active\", isExactActive && \"router-link-exact-active\"],\n          attrs: {\n            href: _vm.isExternal(_vm.resolvePath(_vm.item.path)) ? _vm.resolvePath(_vm.item.path) : href,\n            target: _vm.isExternal(_vm.resolvePath(_vm.item.path)) ? \"_blank\" : \"_self\"\n          },\n          on: {\n            click: navigate\n          }\n        }, [_c(\"el-menu-item\", {\n          attrs: {\n            title: _vm.item.meta.title,\n            index: _vm.resolvePath(_vm.item.path)\n          }\n        }, [_vm.item.meta.icon ? _c(\"svg-icon\", {\n          attrs: {\n            name: _vm.item.meta.icon\n          }\n        }) : _vm._e(), _c(\"span\", [_vm._v(_vm._s(_vm.item.meta.title))])], 1)], 1)];\n      }\n    }], null, false, 451524632)\n  }) : _c(\"el-submenu\", {\n    attrs: {\n      title: _vm.item.meta.title,\n      index: _vm.resolvePath(_vm.item.path)\n    }\n  }, [_c(\"template\", {\n    slot: \"title\"\n  }, [_vm.item.meta.icon ? _c(\"svg-icon\", {\n    attrs: {\n      name: _vm.item.meta.icon\n    }\n  }) : _vm._e(), _c(\"span\", [_vm._v(_vm._s(_vm.item.meta.title))])], 1), _vm._l(_vm.item.children, function (route) {\n    return _c(\"SidebarItem\", {\n      key: route.path,\n      attrs: {\n        item: route,\n        \"base-path\": _vm.resolvePath(_vm.item.path)\n      }\n    });\n  })], 2)], 1) : _vm._e();\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "item", "meta", "sidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attrs", "custom", "to", "<PERSON><PERSON><PERSON>", "path", "scopedSlots", "_u", "key", "fn", "_ref", "href", "navigate", "isActive", "isExactActive", "class", "isExternal", "target", "on", "click", "title", "index", "icon", "name", "_e", "_v", "_s", "slot", "_l", "children", "route", "staticRenderFns", "_withStripped"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/layout/components/SidebarItem/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.item.meta.sidebar !== false\n    ? _c(\n        \"div\",\n        [\n          !_vm.hasChildren\n            ? _c(\"router-link\", {\n                attrs: { custom: \"\", to: _vm.resolvePath(_vm.item.path) },\n                scopedSlots: _vm._u(\n                  [\n                    {\n                      key: \"default\",\n                      fn: function ({\n                        href,\n                        navigate,\n                        isActive,\n                        isExactActive,\n                      }) {\n                        return [\n                          _c(\n                            \"a\",\n                            {\n                              class: [\n                                isActive && \"router-link-active\",\n                                isExactActive && \"router-link-exact-active\",\n                              ],\n                              attrs: {\n                                href: _vm.isExternal(\n                                  _vm.resolvePath(_vm.item.path)\n                                )\n                                  ? _vm.resolvePath(_vm.item.path)\n                                  : href,\n                                target: _vm.isExternal(\n                                  _vm.resolvePath(_vm.item.path)\n                                )\n                                  ? \"_blank\"\n                                  : \"_self\",\n                              },\n                              on: { click: navigate },\n                            },\n                            [\n                              _c(\n                                \"el-menu-item\",\n                                {\n                                  attrs: {\n                                    title: _vm.item.meta.title,\n                                    index: _vm.resolvePath(_vm.item.path),\n                                  },\n                                },\n                                [\n                                  _vm.item.meta.icon\n                                    ? _c(\"svg-icon\", {\n                                        attrs: { name: _vm.item.meta.icon },\n                                      })\n                                    : _vm._e(),\n                                  _c(\"span\", [\n                                    _vm._v(_vm._s(_vm.item.meta.title)),\n                                  ]),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ]\n                      },\n                    },\n                  ],\n                  null,\n                  false,\n                  451524632\n                ),\n              })\n            : _c(\n                \"el-submenu\",\n                {\n                  attrs: {\n                    title: _vm.item.meta.title,\n                    index: _vm.resolvePath(_vm.item.path),\n                  },\n                },\n                [\n                  _c(\n                    \"template\",\n                    { slot: \"title\" },\n                    [\n                      _vm.item.meta.icon\n                        ? _c(\"svg-icon\", {\n                            attrs: { name: _vm.item.meta.icon },\n                          })\n                        : _vm._e(),\n                      _c(\"span\", [_vm._v(_vm._s(_vm.item.meta.title))]),\n                    ],\n                    1\n                  ),\n                  _vm._l(_vm.item.children, function (route) {\n                    return _c(\"SidebarItem\", {\n                      key: route.path,\n                      attrs: {\n                        item: route,\n                        \"base-path\": _vm.resolvePath(_vm.item.path),\n                      },\n                    })\n                  }),\n                ],\n                2\n              ),\n        ],\n        1\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,IAAI,CAACC,IAAI,CAACC,OAAO,KAAK,KAAK,GAClCJ,EAAE,CACA,KAAK,EACL,CACE,CAACD,GAAG,CAACM,WAAW,GACZL,EAAE,CAAC,aAAa,EAAE;IAChBM,KAAK,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,EAAE,EAAET,GAAG,CAACU,WAAW,CAACV,GAAG,CAACG,IAAI,CAACQ,IAAI;IAAE,CAAC;IACzDC,WAAW,EAAEZ,GAAG,CAACa,EAAE,CACjB,CACE;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAJA,EAAEA,CAAAC,IAAA,EAKC;QAAA,IAJDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;UACJC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;UACRC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;UACRC,aAAa,GAAAJ,IAAA,CAAbI,aAAa;QAEb,OAAO,CACLnB,EAAE,CACA,GAAG,EACH;UACEoB,KAAK,EAAE,CACLF,QAAQ,IAAI,oBAAoB,EAChCC,aAAa,IAAI,0BAA0B,CAC5C;UACDb,KAAK,EAAE;YACLU,IAAI,EAAEjB,GAAG,CAACsB,UAAU,CAClBtB,GAAG,CAACU,WAAW,CAACV,GAAG,CAACG,IAAI,CAACQ,IAAI,CAC/B,CAAC,GACGX,GAAG,CAACU,WAAW,CAACV,GAAG,CAACG,IAAI,CAACQ,IAAI,CAAC,GAC9BM,IAAI;YACRM,MAAM,EAAEvB,GAAG,CAACsB,UAAU,CACpBtB,GAAG,CAACU,WAAW,CAACV,GAAG,CAACG,IAAI,CAACQ,IAAI,CAC/B,CAAC,GACG,QAAQ,GACR;UACN,CAAC;UACDa,EAAE,EAAE;YAAEC,KAAK,EAAEP;UAAS;QACxB,CAAC,EACD,CACEjB,EAAE,CACA,cAAc,EACd;UACEM,KAAK,EAAE;YACLmB,KAAK,EAAE1B,GAAG,CAACG,IAAI,CAACC,IAAI,CAACsB,KAAK;YAC1BC,KAAK,EAAE3B,GAAG,CAACU,WAAW,CAACV,GAAG,CAACG,IAAI,CAACQ,IAAI;UACtC;QACF,CAAC,EACD,CACEX,GAAG,CAACG,IAAI,CAACC,IAAI,CAACwB,IAAI,GACd3B,EAAE,CAAC,UAAU,EAAE;UACbM,KAAK,EAAE;YAAEsB,IAAI,EAAE7B,GAAG,CAACG,IAAI,CAACC,IAAI,CAACwB;UAAK;QACpC,CAAC,CAAC,GACF5B,GAAG,CAAC8B,EAAE,CAAC,CAAC,EACZ7B,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAAC+B,EAAE,CAAC/B,GAAG,CAACgC,EAAE,CAAChC,GAAG,CAACG,IAAI,CAACC,IAAI,CAACsB,KAAK,CAAC,CAAC,CACpC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,SACF;EACF,CAAC,CAAC,GACFzB,EAAE,CACA,YAAY,EACZ;IACEM,KAAK,EAAE;MACLmB,KAAK,EAAE1B,GAAG,CAACG,IAAI,CAACC,IAAI,CAACsB,KAAK;MAC1BC,KAAK,EAAE3B,GAAG,CAACU,WAAW,CAACV,GAAG,CAACG,IAAI,CAACQ,IAAI;IACtC;EACF,CAAC,EACD,CACEV,EAAE,CACA,UAAU,EACV;IAAEgC,IAAI,EAAE;EAAQ,CAAC,EACjB,CACEjC,GAAG,CAACG,IAAI,CAACC,IAAI,CAACwB,IAAI,GACd3B,EAAE,CAAC,UAAU,EAAE;IACbM,KAAK,EAAE;MAAEsB,IAAI,EAAE7B,GAAG,CAACG,IAAI,CAACC,IAAI,CAACwB;IAAK;EACpC,CAAC,CAAC,GACF5B,GAAG,CAAC8B,EAAE,CAAC,CAAC,EACZ7B,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAAC+B,EAAE,CAAC/B,GAAG,CAACgC,EAAE,CAAChC,GAAG,CAACG,IAAI,CAACC,IAAI,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,CAClD,EACD,CACF,CAAC,EACD1B,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACG,IAAI,CAACgC,QAAQ,EAAE,UAAUC,KAAK,EAAE;IACzC,OAAOnC,EAAE,CAAC,aAAa,EAAE;MACvBa,GAAG,EAAEsB,KAAK,CAACzB,IAAI;MACfJ,KAAK,EAAE;QACLJ,IAAI,EAAEiC,KAAK;QACX,WAAW,EAAEpC,GAAG,CAACU,WAAW,CAACV,GAAG,CAACG,IAAI,CAACQ,IAAI;MAC5C;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACN,EACD,CACF,CAAC,GACDX,GAAG,CAAC8B,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAIO,eAAe,GAAG,EAAE;AACxBtC,MAAM,CAACuC,aAAa,GAAG,IAAI;AAE3B,SAASvC,MAAM,EAAEsC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}