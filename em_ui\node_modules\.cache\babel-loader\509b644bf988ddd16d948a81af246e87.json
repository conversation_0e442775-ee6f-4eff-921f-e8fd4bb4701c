{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getFacilities, saveFacilities, deleteFacilities } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      loading: false,\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      },\n      table_selectedRowKeys: [],\n      facilities_query_type: 'name',\n      facilities_query_buttonTitle: '搜索',\n      facilities_query_text: '',\n      facilities_save_title: '新增公共设施',\n      facilities_save_modalVisible: false,\n      facilities_form_data: {},\n      facilities_data_list: []\n    };\n  },\n  created: function created() {\n    this.Get_facilitiesDataList();\n  },\n  watch: {\n    facilities_save_modalVisible: function facilities_save_modalVisible(val) {\n      if (!val) {\n        this.facilities_form_data = {};\n      }\n    }\n  },\n  methods: {\n    Get_facilitiesDataList: function Get_facilitiesDataList() {\n      var _this = this;\n      getFacilities().then(function (res) {\n        _this.facilities_query_buttonTitle = '搜索';\n        _this.facilities_data_list = res.data;\n        _this.facilities_save_title = '新增公共设施';\n      });\n    },\n    Query_facilitiesDataList: function Query_facilitiesDataList() {\n      var _this2 = this;\n      var text = this.facilities_query_text;\n      var temp_list = [];\n      this.facilities_data_list.forEach(function (item) {\n        if (item[_this2.facilities_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.facilities_query_buttonTitle = '返回';\n      this.facilities_data_list = temp_list;\n    },\n    Edit_facilitiesData: function Edit_facilitiesData(form) {\n      this.facilities_save_title = '编辑公共设施';\n      this.facilities_form_data = JSON.parse(JSON.stringify(form));\n      this.facilities_save_modalVisible = true;\n    },\n    Del_facilitiesData: function Del_facilitiesData(id) {\n      var _this3 = this;\n      deleteFacilities(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this3, '操作成功');\n        } else {\n          Warning(_this3, '操作失败');\n        }\n        _this3.Get_facilitiesDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this4 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this4.Del_facilitiesData(_this4.facilities_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_facilitiesData: function Save_facilitiesData() {\n      var _this5 = this;\n      saveFacilities(this.facilities_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this5, '操作成功');\n        } else {\n          Warning(_this5, '操作失败');\n        }\n        _this5.facilities_save_modalVisible = false;\n        _this5.Get_facilitiesDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;AA0GA;AACA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAJ;MACA;QACA;MACA;IACA;EACA;EACAK;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;MACA;IAGA;IACAC;MACA;IACA;EACA;AACA", "names": ["data", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "facilities_query_type", "facilities_query_buttonTitle", "facilities_query_text", "facilities_save_title", "facilities_save_modalVisible", "facilities_form_data", "facilities_data_list", "created", "watch", "methods", "Get_facilitiesDataList", "getFacilities", "_this", "Query_facilitiesDataList", "temp_list", "Edit_facilitiesData", "Del_facilitiesData", "deleteFacilities", "Success", "Warning", "_this3", "Del_batchData", "_this4", "Save_facilitiesData", "saveFacilities", "_this5", "Table_selectChange"], "sourceRoot": "src/views/admin/rq", "sources": ["rq_facilities.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"社区公共设施管理\">\n      <div class=\"head\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"单位名称\"\n          v-model=\"facilities_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"name\">单位名称</a-select-option>\n          <a-select-option value=\"chargePerson\">负责人</a-select-option>\n          <a-select-option value=\"contactPerson\">联系人</a-select-option>\n          <a-select-option value=\"phone\">电话</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"facilities_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"facilities_query_buttonTitle\"\n          size=\"large\"\n          @search=\"facilities_query_buttonTitle == '搜索' ? Query_facilitiesDataList() : Get_facilitiesDataList()\"\n        />\n        <a-button\n          type=\"primary\"\n          style=\"height: 38px;\"\n          @click=\"facilities_save_modalVisible = true\"\n        >添加设施</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「公共设施」</a-button>\n      </div>\n      <a-table\n        :data-source=\"facilities_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"name\" title=\"设施名称\" data-index=\"name\" />\n        <a-table-column key=\"type\" title=\"设施类型\" data-index=\"type\" />\n        <a-table-column key=\"chargePerson\" title=\"设施负责人\" data-index=\"chargePerson\" />\n        <a-table-column key=\"contactPerson\" title=\"设施联系人\" data-index=\"contactPerson\" />\n        <a-table-column key=\"phone\" title=\"联系电话\" data-index=\"phone\" />\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_facilitiesData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_facilitiesData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"facilities_save_modalVisible\"\n      :title=\"facilities_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_facilitiesData\"\n    >\n      <a-form-model :model=\"facilities_form_data\" :label-col=\"labelCol\" :wrapper-col=\"wrapperCol\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"单位名称\">\n              <a-input v-model=\"facilities_form_data.name\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"设备类型\">\n              <a-input v-model=\"facilities_form_data.type\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"负责人\">\n              <a-input v-model=\"facilities_form_data.chargePerson\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"联系人\">\n              <a-input v-model=\"facilities_form_data.contactPerson\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"联系电话\">\n              <a-input v-model=\"facilities_form_data.phone\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <a-form-model-item label=\"设施描述\" :labelCol=\" { span: 4 }\">\n          <a-input v-model=\"facilities_form_data.descri\" type=\"textarea\" />\n        </a-form-model-item>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getFacilities, saveFacilities, deleteFacilities } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { rTime } from '@/util/time.js'\n\nexport default {\n  data () {\n    return {\n      loading: false,\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n      table_selectedRowKeys: [],\n      facilities_query_type: 'name',\n      facilities_query_buttonTitle: '搜索',\n      facilities_query_text: '',\n      facilities_save_title: '新增公共设施',\n      facilities_save_modalVisible: false,\n      facilities_form_data: {},\n      facilities_data_list: [],\n    }\n  },\n  created () {\n    this.Get_facilitiesDataList()\n  },\n  watch: {\n    facilities_save_modalVisible (val) {\n      if (!val) {\n        this.facilities_form_data = {}\n      }\n    }\n  },\n  methods: {\n    Get_facilitiesDataList () {\n      getFacilities().then(res => {\n        this.facilities_query_buttonTitle = '搜索'\n        this.facilities_data_list = res.data\n        this.facilities_save_title = '新增公共设施'\n      })\n    },\n    Query_facilitiesDataList () {\n      let text = this.facilities_query_text\n      let temp_list = []\n      this.facilities_data_list.forEach(item => {\n        if (item[this.facilities_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.facilities_query_buttonTitle = '返回'\n      this.facilities_data_list = temp_list\n    },\n    Edit_facilitiesData (form) {\n      this.facilities_save_title = '编辑公共设施'\n      this.facilities_form_data = JSON.parse(JSON.stringify(form))\n      this.facilities_save_modalVisible = true\n    },\n    Del_facilitiesData (id) {\n      deleteFacilities(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_facilitiesDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_facilitiesData(this.facilities_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    Save_facilitiesData () {\n      saveFacilities(this.facilities_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.facilities_save_modalVisible = false\n        this.Get_facilitiesDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}