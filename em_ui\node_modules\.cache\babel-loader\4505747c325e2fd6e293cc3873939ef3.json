{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getBuildingNames, getUnitNameList, getRoom, saveRoom, deleteRoom, downloadRooms } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { download } from '@/util/download.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      rTime: rTime,\n      loading: false,\n      init: true,\n      isEdit: false,\n      labelCol: {\n        span: 8\n      },\n      wrapperCol: {\n        span: 14\n      },\n      table_selectedRowKeys: [],\n      building_query_name: '',\n      unit_query_name: '',\n      room_query_buttonTitle: '搜索',\n      room_query_text: '',\n      room_save_title: '新增房间',\n      room_save_modalVisible: false,\n      room_form_data: {},\n      room_data_list: [],\n      room_building_name_list: [],\n      room_unit_name_list: []\n    };\n  },\n  created: function created() {\n    this.Get_buildingNameList();\n    this.Get_roomDataList();\n  },\n  watch: {\n    room_save_modalVisible: function room_save_modalVisible(val) {\n      if (!val) {\n        this.room_form_data = {};\n      }\n    },\n    building_query_name: function building_query_name(val) {\n      this.Get_unitNameList(this.building_query_name);\n      this.Get_roomDataList();\n    },\n    unit_query_name: function unit_query_name(val) {\n      this.Get_roomDataList();\n    },\n    isEdit: function isEdit(val) {\n      if (val) {\n        this.room_save_title = '新增房间';\n      } else {\n        this.room_save_title = '编辑房间';\n      }\n    }\n  },\n  methods: {\n    Get_buildingNameList: function Get_buildingNameList() {\n      var _this = this;\n      getBuildingNames().then(function (res) {\n        _this.room_building_name_list = res.data;\n        _this.building_query_name = res.data[0].name;\n        _this.Get_unitNameList(res.data[0].name);\n        _this.init = false;\n      });\n    },\n    Get_unitNameList: function Get_unitNameList(buildingName) {\n      var _this2 = this;\n      getUnitNameList(buildingName).then(function (res) {\n        _this2.room_unit_name_list = res.data;\n        _this2.unit_query_name = res.data[0].unitName;\n      });\n    },\n    Get_roomDataList: function Get_roomDataList() {\n      var _this3 = this;\n      this.loading = true;\n      if (this.init) {\n        this.Get_buildingNameList();\n      }\n      getRoom().then(function (res) {\n        _this3.room_query_buttonTitle = '搜索';\n        var temp_list = [];\n        res.data.forEach(function (item) {\n          if (item['buildingName'] == _this3.building_query_name && item['unitName'] == _this3.unit_query_name) {\n            temp_list.push(item);\n          }\n        });\n        _this3.room_data_list = temp_list;\n        _this3.loading = false;\n      });\n    },\n    Edit_roomData: function Edit_roomData(form) {\n      this.room_save_title = '编辑房间';\n      this.room_form_data = JSON.parse(JSON.stringify(form));\n      this.room_save_modalVisible = true;\n      this.isEdit = true;\n    },\n    Del_roomData: function Del_roomData(id) {\n      var _this4 = this;\n      deleteRoom(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this4, '操作成功');\n        } else {\n          Warning(_this4, '操作失败');\n        }\n        _this4.Get_roomDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this5 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this5.Del_roomData(_this5.room_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_roomData: function Save_roomData() {\n      var _this6 = this;\n      if (this.room_form_data.isSale == undefined) {\n        this.room_form_data.isSale = '空';\n      }\n      saveRoom(this.room_form_data).then(function (res) {\n        if (res.code == 200) {\n          Success(_this6, '操作成功');\n        } else {\n          Warning(_this6, '操作失败');\n        }\n        _this6.room_save_modalVisible = false;\n        _this6.isEdit = false;\n        _this6.Get_roomDataList();\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_isSale_changeHandler: function Form_isSale_changeHandler(val) {\n      this.room_form_data.isSale = val ? '已有住户' : '空';\n    },\n    Download_roomExcel: function Download_roomExcel() {\n      downloadRooms().then(function (res) {\n        download('room.xlsx', res.data);\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;;AAwKA;AACA;AACA;AACA;AACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAN;MACA;QACA;MACA;IACA;IACAL;MACA;MACA;IACA;IACAC;MACA;IACA;IACAN;MACA;QACA;MACA;QACA;MAEA;IACA;EAEA;EACAiB;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;QACAA;MACA;IAEA;IACAC;MAAA;MACAC;QACAC;QACAA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACAC;QACAC;QACA;QACAC;UACA;YACAC;UACA;QACA;QACAF;QACAA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACAC;QACA;UACAN;QACA;UACAC;QACA;QACAM;QACAA;QACAA;MACA;IAGA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA", "names": ["data", "rTime", "loading", "init", "isEdit", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "building_query_name", "unit_query_name", "room_query_buttonTitle", "room_query_text", "room_save_title", "room_save_modalVisible", "room_form_data", "room_data_list", "room_building_name_list", "room_unit_name_list", "created", "watch", "methods", "Get_buildingNameList", "getBuildingNames", "_this", "Get_unitNameList", "getUnitNameList", "_this2", "Get_roomDataList", "getRoom", "_this3", "res", "temp_list", "Edit_roomData", "Del_roomData", "deleteRoom", "Success", "Warning", "_this4", "Del_batchData", "_this5", "Save_roomData", "saveRoom", "_this6", "Table_selectChange", "Form_isSale_changeHandler", "Download_roomExcel", "downloadRooms", "download"], "sourceRoot": "src/views/admin/rq", "sources": ["rq_room.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card title=\"社区房间管理\">\n      <div class=\"head\">\n        <span>楼宇</span>\n        <a-select size=\"large\" v-model=\"building_query_name\" style=\"width: 200px;\">\n          <a-select-option\n            v-for=\"item in room_building_name_list\"\n            :key=\"item.name\"\n            :value=\"item.name\"\n          >{{item.name}}</a-select-option>\n        </a-select>\n        <span style=\"margin-left: 10px;\">单元</span>\n        <a-select size=\"large\" v-model=\"unit_query_name\" style=\"width: 200px;\">\n          <a-select-option\n            v-for=\"item in room_unit_name_list\"\n            :key=\"item.unitName\"\n            :value=\"item.unitName\"\n          >{{item.unitName}}</a-select-option>\n        </a-select>\n        <a-button\n          type=\"primary\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"room_save_modalVisible = true\"\n        >添加楼宇</a-button>\n        <a-button\n          type=\"primary\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Download_roomExcel\"\n        >导出Excel</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「楼宇」</a-button>\n      </div>\n      <a-table\n        :loading=\"loading\"\n        :data-source=\"room_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"id\" title=\"房间编号\" data-index=\"id\" />\n        <a-table-column key=\"buildingName\" title=\"楼宇名称\" data-index=\"buildingName\" />\n        <a-table-column key=\"unitName\" title=\"单元名称\" data-index=\"unitName\" />\n        <a-table-column key=\"direction\" title=\"朝向\" data-index=\"direction\" />\n        <a-table-column key=\"purpose\" title=\"用途\" data-index=\"purpose\" />\n        <a-table-column key=\"specifications\" title=\"规格\" data-index=\"specifications\" />\n        <a-table-column key=\"grade\" title=\"标准\" data-index=\"grade\" />\n        <a-table-column key=\"builtupArea\" title=\"建筑面积\" data-index=\"builtupArea\" />\n        <a-table-column key=\"useArea\" title=\"使用面积\" data-index=\"useArea\" />\n        <a-table-column key=\"isSale\" title=\"是否出售\" data-index=\"isSale\">\n          <template slot-scope=\"text, record\">\n            <a-tag :color=\"record.isSale == '已有住户' ? 'red' : 'blue'\">{{record.isSale}}</a-tag>\n          </template>\n        </a-table-column>\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_roomData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_roomData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"room_save_modalVisible\"\n      :title=\"room_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_roomData\"\n    >\n      <a-form-model :model=\"room_form_data\" :label-col=\"labelCol\" :wrapper-col=\"wrapperCol\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"楼宇名称\">\n              <a-select v-model=\"room_form_data.buildingName\" style=\"width: 131px;\">\n                <a-select-option\n                  v-for=\"item in room_building_name_list\"\n                  :key=\"item.name\"\n                  :value=\"item.name\"\n                >{{item.name}}</a-select-option>\n              </a-select>\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"单元名称\">\n              <a-input v-model=\"room_form_data.unitName\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"房间朝向\">\n              <a-select\n                default-value=\"坐北朝南\"\n                v-model=\"room_form_data.direction\"\n                style=\"width: 131px;\"\n              >\n                <a-select-option value=\"坐北朝南\">坐北朝南</a-select-option>\n                <a-select-option value=\"坐南朝北\">坐南朝北</a-select-option>\n                <a-select-option value=\"坐东朝西\">坐东朝西</a-select-option>\n                <a-select-option value=\"坐西朝东\">坐西朝东</a-select-option>\n              </a-select>\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"房间用途\">\n              <a-select default-value=\"住宅\" v-model=\"room_form_data.purpose\" style=\"width: 131px;\">\n                <a-select-option value=\"住宅\">住宅</a-select-option>\n                <a-select-option value=\"商铺\">商铺</a-select-option>\n              </a-select>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"房间规格\">\n              <a-select\n                default-value=\"普通\"\n                v-model=\"room_form_data.specifications\"\n                style=\"width: 131px;\"\n              >\n                <a-select-option value=\"普通\">普通</a-select-option>\n                <a-select-option value=\"高级\">高级</a-select-option>\n                <a-select-option value=\"豪华\">豪华</a-select-option>\n              </a-select>\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"房间标准\">\n              <a-select default-value=\"二室一厅\" v-model=\"room_form_data.grade\" style=\"width: 131px;\">\n                <a-select-option value=\"二室一厅\">二室一厅</a-select-option>\n                <a-select-option value=\"三室一厅\">三室一厅</a-select-option>\n                <a-select-option value=\"三室二厅\">三室二厅</a-select-option>\n              </a-select>\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"建筑面积\">\n              <a-input v-model=\"room_form_data.builtupArea\" />\n            </a-form-model-item>\n          </el-col>\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"使用面积\">\n              <a-input v-model=\"room_form_data.useArea\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\" :offset=\"0\">\n            <a-form-model-item label=\"是否出售\">\n              <a-switch @change=\"Form_isSale_changeHandler\" />\n            </a-form-model-item>\n          </el-col>\n        </el-row>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getBuildingNames, getUnitNameList, getRoom, saveRoom, deleteRoom, downloadRooms } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { download } from '@/util/download.js'\nimport { rTime } from '@/util/time.js'\nexport default {\n  data () {\n    return {\n      rTime,\n      loading: false,\n      init: true,\n      isEdit: false,\n      labelCol: { span: 8 },\n      wrapperCol: { span: 14 },\n      table_selectedRowKeys: [],\n      building_query_name: '',\n      unit_query_name: '',\n      room_query_buttonTitle: '搜索',\n      room_query_text: '',\n      room_save_title: '新增房间',\n      room_save_modalVisible: false,\n      room_form_data: {},\n      room_data_list: [],\n      room_building_name_list: [],\n      room_unit_name_list: [],\n    }\n  },\n  created () {\n    this.Get_buildingNameList()\n    this.Get_roomDataList()\n  },\n  watch: {\n    room_save_modalVisible (val) {\n      if (!val) {\n        this.room_form_data = {}\n      }\n    },\n    building_query_name (val) {\n      this.Get_unitNameList(this.building_query_name)\n      this.Get_roomDataList()\n    },\n    unit_query_name (val) {\n      this.Get_roomDataList()\n    },\n    isEdit (val) {\n      if (val) {\n        this.room_save_title = '新增房间'\n      } else {\n        this.room_save_title = '编辑房间'\n\n      }\n    }\n\n  },\n  methods: {\n    Get_buildingNameList () {\n      getBuildingNames().then(res => {\n        this.room_building_name_list = res.data\n        this.building_query_name = res.data[0].name\n        this.Get_unitNameList(res.data[0].name)\n        this.init = false\n      })\n\n    },\n    Get_unitNameList (buildingName) {\n      getUnitNameList(buildingName).then(res => {\n        this.room_unit_name_list = res.data\n        this.unit_query_name = res.data[0].unitName\n      })\n    },\n    Get_roomDataList () {\n      this.loading = true\n      if (this.init) {\n        this.Get_buildingNameList()\n      }\n      getRoom().then(res => {\n        this.room_query_buttonTitle = '搜索'\n        let temp_list = []\n        res.data.forEach(item => {\n          if (item['buildingName'] == this.building_query_name && item['unitName'] == this.unit_query_name) {\n            temp_list.push(item)\n          }\n        })\n        this.room_data_list = temp_list\n        this.loading = false\n      })\n    },\n    Edit_roomData (form) {\n      this.room_save_title = '编辑房间'\n      this.room_form_data = JSON.parse(JSON.stringify(form))\n      this.room_save_modalVisible = true\n      this.isEdit = true\n    },\n    Del_roomData (id) {\n      deleteRoom(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_roomDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_roomData(this.room_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    Save_roomData () {\n      if (this.room_form_data.isSale == undefined) {\n        this.room_form_data.isSale = '空'\n      }\n      saveRoom(this.room_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.room_save_modalVisible = false\n        this.isEdit = false\n        this.Get_roomDataList()\n      })\n\n\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Form_isSale_changeHandler (val) {\n      this.room_form_data.isSale = val ? '已有住户' : '空'\n    },\n    Download_roomExcel () {\n      downloadRooms().then(res => {\n        download('room.xlsx', res.data)\n      })\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n</style>\n\n<style lang=\"scss\">\n.ant-table-body {\n    height: 42vh;\n    overflow: auto;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}