{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  beforeRouteLeave: function beforeRouteLeave(to, from, next) {\n    clearInterval(this.inter);\n    next();\n  },\n  data: function data() {\n    return {\n      inter: null,\n      countdown: 5\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.inter = setInterval(function () {\n      _this.countdown--;\n      if (_this.countdown == 0) {\n        clearInterval(_this.inter);\n        _this.goBack();\n      }\n    }, 1000);\n  },\n  methods: {\n    goBack: function goBack() {\n      this.$router.push('/');\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAYA;EACAA;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACAC;MACA;QACAN;QACAM;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA", "names": ["beforeRouteLeave", "clearInterval", "next", "data", "inter", "countdown", "mounted", "_this", "methods", "goBack"], "sourceRoot": "src/views", "sources": ["404.vue"], "sourcesContent": ["<template>\n    <div class=\"notfound\">\n        <png-icon name=\"404\" size=\"200\" />\n        <div class=\"content\">\n            <h1>404</h1>\n            <div class=\"desc\">抱歉，你访问的页面不存在</div>\n            <el-button type=\"primary\" @click=\"goBack\">{{ countdown }}秒后，返回首页</el-button>\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    beforeRouteLeave(to, from, next) {\n        clearInterval(this.inter)\n        next()\n    },\n    data() {\n        return {\n            inter: null,\n            countdown: 5\n        }\n    },\n    mounted() {\n        this.inter = setInterval(() => {\n            this.countdown--\n            if (this.countdown == 0) {\n                clearInterval(this.inter)\n                this.goBack()\n            }\n        }, 1000)\n    },\n    methods: {\n        goBack() {\n            this.$router.push('/')\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.notfound {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 700px;\n    @include position-center(xy);\n    .svg-icon {\n        width: 400px;\n        height: 400px;\n    }\n    .content {\n        h1 {\n            margin: 0;\n            font-size: 72px;\n            color: #303133;\n        }\n        .desc {\n            margin: 20px 0 30px;\n            font-size: 20px;\n            color: #606266;\n        }\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}