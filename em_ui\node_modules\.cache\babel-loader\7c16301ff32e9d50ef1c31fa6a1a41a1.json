{"ast": null, "code": "import { auth, authAll } from '@/util';\nexport default {\n  install: function install(Vue) {\n    Vue.prototype.$auth = auth;\n    Vue.prototype.$authAll = authAll;\n    // 注册 v-auth 和 v-auth-all 指令\n    Vue.directive('auth', {\n      inserted: function inserted(el, binding) {\n        if (!auth(binding.value)) {\n          el.remove();\n        }\n      }\n    });\n    Vue.directive('auth-all', {\n      inserted: function inserted(el, binding) {\n        if (!authAll(binding.value)) {\n          el.remove();\n        }\n      }\n    });\n  }\n};", "map": {"version": 3, "names": ["auth", "authAll", "install", "<PERSON><PERSON>", "prototype", "$auth", "$authAll", "directive", "inserted", "el", "binding", "value", "remove"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/util/auth.js"], "sourcesContent": ["import { auth, authAll } from '@/util'\n\nexport default {\n    install(Vue) {\n        Vue.prototype.$auth = auth\n        Vue.prototype.$authAll = authAll\n        // 注册 v-auth 和 v-auth-all 指令\n        Vue.directive('auth', {\n            inserted: (el, binding) => {\n                if (!auth(binding.value)) {\n                    el.remove()\n                }\n            }\n        })\n        Vue.directive('auth-all', {\n            inserted: (el, binding) => {\n                if (!authAll(binding.value)) {\n                    el.remove()\n                }\n            }\n        })\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,QAAQ;AAEtC,eAAe;EACXC,OAAO,WAAPA,OAAOA,CAACC,GAAG,EAAE;IACTA,GAAG,CAACC,SAAS,CAACC,KAAK,GAAGL,IAAI;IAC1BG,GAAG,CAACC,SAAS,CAACE,QAAQ,GAAGL,OAAO;IAChC;IACAE,GAAG,CAACI,SAAS,CAAC,MAAM,EAAE;MAClBC,QAAQ,EAAE,SAAVA,QAAQA,CAAGC,EAAE,EAAEC,OAAO,EAAK;QACvB,IAAI,CAACV,IAAI,CAACU,OAAO,CAACC,KAAK,CAAC,EAAE;UACtBF,EAAE,CAACG,MAAM,CAAC,CAAC;QACf;MACJ;IACJ,CAAC,CAAC;IACFT,GAAG,CAACI,SAAS,CAAC,UAAU,EAAE;MACtBC,QAAQ,EAAE,SAAVA,QAAQA,CAAGC,EAAE,EAAEC,OAAO,EAAK;QACvB,IAAI,CAACT,OAAO,CAACS,OAAO,CAACC,KAAK,CAAC,EAAE;UACzBF,EAAE,CAACG,MAAM,CAAC,CAAC;QACf;MACJ;IACJ,CAAC,CAAC;EACN;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}