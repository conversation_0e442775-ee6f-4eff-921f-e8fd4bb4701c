{"ast": null, "code": "import _interopRequireWildcard from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/interopRequireWildcard.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport Layout from '@/layout';\nexport default [\n// {\n//   path: '/rq-manager',\n//   component: Layout,\n//   redirect: '/rq-manager/info',\n//   meta: {\n//     title: '社区管理'\n//   },\n//   children: [\n//     {\n//       path: 'info',\n//       name: 'rq_info',\n//       component: () => import('@/views/admin/rq/rq_info.vue'),\n//       meta: {\n//         title: '基本信息管理',\n//       }\n//     },\n//     {\n//       path: 'facilities',\n//       name: 'rq_facilities',\n//       component: () => import('@/views/admin/rq/rq_facilities.vue'),\n//       meta: {\n//         title: '周边设施管理',\n//       }\n//     },\n//     {\n//       path: 'notices',\n//       name: 'rq_notices',\n//       component: () => import('@/views/admin/rq/rq_notices.vue'),\n//       meta: {\n//         title: '物业公告管理',\n//       }\n//     },\n//   ]\n// },\n// {\n//   path: '/building-manager',\n//   component: Layout,\n//   redirect: '/building-manager/building',\n//   meta: {\n//     title: '楼盘管理'\n//   },\n//   children: [\n//     {\n//       path: 'building',\n//       name: 'rq_building',\n//       component: () => import('@/views/admin/rq/rq_building.vue'),\n//       meta: {\n//         title: '楼宇管理',\n//       }\n//     },\n//     {\n//       path: 'room',\n//       name: 'rq_room',\n//       component: () => import('@/views/admin/rq/rq_room.vue'),\n//       meta: {\n//         title: '房间管理',\n//       }\n//     }\n//   ]\n// },\n{\n  path: '/user/base',\n  component: Layout,\n  redirect: '/user/base/info',\n  meta: {\n    title: '基本信息'\n  },\n  children: [{\n    path: 'info',\n    name: 'user_info',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/user/user_info.vue'));\n      });\n    },\n    meta: {\n      title: '个人信息'\n    }\n  }, {\n    path: 'reset_pwd',\n    name: 'user_reset_pwd',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/user/user_reset_pwd.vue'));\n      });\n    },\n    meta: {\n      title: '密码修改'\n    }\n  }]\n}, {\n  path: '/user/',\n  component: Layout,\n  redirect: '/user/pay',\n  meta: {\n    title: '物业收费'\n  },\n  children: [{\n    path: 'pay',\n    name: 'user_pay',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/user/user_pay.vue'));\n      });\n    },\n    meta: {\n      title: '缴纳费用'\n    }\n  }, {\n    path: 'pay_record',\n    name: 'user_pay',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/user/user_pay.vue'));\n      });\n    },\n    meta: {\n      title: '缴纳费用'\n    }\n  }]\n}, {\n  path: '/user/repair',\n  component: Layout,\n  redirect: '/user/repair/add',\n  meta: {\n    title: '报修管理'\n  },\n  children: [{\n    path: 'add',\n    name: 'rq_repair',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/guarantee/rq_repair_add.vue'));\n      });\n    },\n    meta: {\n      title: '申请报修'\n    }\n  }, {\n    path: 'manager',\n    name: 'rq_repair',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/guarantee/rq_repair_manager.vue'));\n      });\n    },\n    meta: {\n      title: '报修管理'\n    }\n  }]\n}, {\n  path: '/user/complaint',\n  component: Layout,\n  redirect: '/user/complaint/add',\n  meta: {\n    title: '投诉管理'\n  },\n  children: [{\n    path: 'add',\n    name: 'rq_complaint',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/guarantee/rq_complaint_add.vue'));\n      });\n    },\n    meta: {\n      title: '发起投诉'\n    }\n  }, {\n    path: 'manager',\n    name: 'rq_complaint',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/admin/rq/guarantee/rq_complaint_manager.vue'));\n      });\n    },\n    meta: {\n      title: '投诉管理'\n    }\n  }]\n}\n\n// {\n//   path: '/videos_example/1',\n//   component: Layout,\n//   redirect: '/videos_example/aaas',\n//   meta: {\n//     title: '收费管理'\n//   },\n//   children: [\n//     {\n//       path: 'aaas',\n//       name: 'videosExampleVideo1',\n//       component: () => import('@/views/videos_example/1'),\n//       meta: {\n//         title: '收费类型管理',\n//       }\n//     },\n//     {\n//       path: 'aaas',\n//       name: 'videosExampleVideo1',\n//       component: () => import('@/views/videos_example/1'),\n//       meta: {\n//         title: '住户费用管理',\n//       }\n//     },\n//   ]\n// },\n// {\n//   path: '/videos_example/1',\n//   component: Layout,\n//   redirect: '/videos_example/aaas',\n//   meta: {\n//     title: '用户管理'\n//   },\n//   children: [\n//     {\n//       path: 'aaas',\n//       name: 'videosExampleVideo1',\n//       component: () => import('@/views/videos_example/1'),\n//       meta: {\n//         title: '住户信息管理',\n//       }\n//     },\n//     {\n//       path: 'aaas',\n//       name: 'videosExampleVideo1',\n//       component: () => import('@/views/videos_example/1'),\n//       meta: {\n//         title: '物业管理人员管理',\n//       }\n//     },\n//   ]\n// },\n// {\n//   path: '/videos_example/1',\n//   component: Layout,\n//   redirect: '/videos_example/aaas',\n//   meta: {\n//     title: '系统管理'\n//   },\n//   children: [\n//     {\n//       path: 'aaas',\n//       name: 'videosExampleVideo1',\n//       component: () => import('@/views/videos_example/1'),\n//       meta: {\n//         title: '定时任务管理',\n//       }\n//     },\n//     {\n//       path: 'aaas',\n//       name: 'videosExampleVideo1',\n//       component: () => import('@/views/videos_example/1'),\n//       meta: {\n//         title: '编辑管理员',\n//       }\n//     },\n//   ]\n// },\n];", "map": {"version": 3, "names": ["Layout", "path", "component", "redirect", "meta", "title", "children", "name", "Promise", "resolve", "then", "_interopRequireWildcard", "require"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/src/router/modules/user.js"], "sourcesContent": ["import Layout from '@/layout'\n\nexport default [\n  // {\n  //   path: '/rq-manager',\n  //   component: Layout,\n  //   redirect: '/rq-manager/info',\n  //   meta: {\n  //     title: '社区管理'\n  //   },\n  //   children: [\n  //     {\n  //       path: 'info',\n  //       name: 'rq_info',\n  //       component: () => import('@/views/admin/rq/rq_info.vue'),\n  //       meta: {\n  //         title: '基本信息管理',\n  //       }\n  //     },\n  //     {\n  //       path: 'facilities',\n  //       name: 'rq_facilities',\n  //       component: () => import('@/views/admin/rq/rq_facilities.vue'),\n  //       meta: {\n  //         title: '周边设施管理',\n  //       }\n  //     },\n  //     {\n  //       path: 'notices',\n  //       name: 'rq_notices',\n  //       component: () => import('@/views/admin/rq/rq_notices.vue'),\n  //       meta: {\n  //         title: '物业公告管理',\n  //       }\n  //     },\n  //   ]\n  // },\n  // {\n  //   path: '/building-manager',\n  //   component: Layout,\n  //   redirect: '/building-manager/building',\n  //   meta: {\n  //     title: '楼盘管理'\n  //   },\n  //   children: [\n  //     {\n  //       path: 'building',\n  //       name: 'rq_building',\n  //       component: () => import('@/views/admin/rq/rq_building.vue'),\n  //       meta: {\n  //         title: '楼宇管理',\n  //       }\n  //     },\n  //     {\n  //       path: 'room',\n  //       name: 'rq_room',\n  //       component: () => import('@/views/admin/rq/rq_room.vue'),\n  //       meta: {\n  //         title: '房间管理',\n  //       }\n  //     }\n  //   ]\n  // },\n  {\n    path: '/user/base',\n    component: Layout,\n    redirect: '/user/base/info',\n    meta: {\n      title: '基本信息'\n    },\n    children: [\n      {\n        path: 'info',\n        name: 'user_info',\n        component: () => import('@/views/admin/user/user_info.vue'),\n        meta: {\n          title: '个人信息',\n        }\n      },\n      {\n        path: 'reset_pwd',\n        name: 'user_reset_pwd',\n        component: () => import('@/views/admin/user/user_reset_pwd.vue'),\n        meta: {\n          title: '密码修改',\n        }\n      },\n\n    ]\n  },\n  {\n    path: '/user/',\n    component: Layout,\n    redirect: '/user/pay',\n    meta: {\n      title: '物业收费'\n    },\n    children: [\n      {\n        path: 'pay',\n        name: 'user_pay',\n        component: () => import('@/views/admin/user/user_pay.vue'),\n        meta: {\n          title: '缴纳费用',\n        }\n      },\n      {\n        path: 'pay_record',\n        name: 'user_pay',\n        component: () => import('@/views/admin/user/user_pay.vue'),\n        meta: {\n          title: '缴纳费用',\n        }\n      },\n\n    ]\n  },\n  {\n    path: '/user/repair',\n    component: Layout,\n    redirect: '/user/repair/add',\n    meta: {\n      title: '报修管理'\n    },\n    children: [\n      {\n        path: 'add',\n        name: 'rq_repair',\n        component: () => import('@/views/admin/rq/guarantee/rq_repair_add.vue'),\n        meta: {\n          title: '申请报修',\n        }\n      },\n      {\n        path: 'manager',\n        name: 'rq_repair',\n        component: () => import('@/views/admin/rq/guarantee/rq_repair_manager.vue'),\n        meta: {\n          title: '报修管理',\n        }\n      },\n\n    ]\n  },\n  {\n    path: '/user/complaint',\n    component: Layout,\n    redirect: '/user/complaint/add',\n    meta: {\n      title: '投诉管理'\n    },\n    children: [\n      {\n        path: 'add',\n        name: 'rq_complaint',\n        component: () => import('@/views/admin/rq/guarantee/rq_complaint_add.vue'),\n        meta: {\n          title: '发起投诉',\n        }\n      },\n      {\n        path: 'manager',\n        name: 'rq_complaint',\n        component: () => import('@/views/admin/rq/guarantee/rq_complaint_manager.vue'),\n        meta: {\n          title: '投诉管理',\n        }\n      },\n\n    ]\n  },\n\n  // {\n  //   path: '/videos_example/1',\n  //   component: Layout,\n  //   redirect: '/videos_example/aaas',\n  //   meta: {\n  //     title: '收费管理'\n  //   },\n  //   children: [\n  //     {\n  //       path: 'aaas',\n  //       name: 'videosExampleVideo1',\n  //       component: () => import('@/views/videos_example/1'),\n  //       meta: {\n  //         title: '收费类型管理',\n  //       }\n  //     },\n  //     {\n  //       path: 'aaas',\n  //       name: 'videosExampleVideo1',\n  //       component: () => import('@/views/videos_example/1'),\n  //       meta: {\n  //         title: '住户费用管理',\n  //       }\n  //     },\n  //   ]\n  // },\n  // {\n  //   path: '/videos_example/1',\n  //   component: Layout,\n  //   redirect: '/videos_example/aaas',\n  //   meta: {\n  //     title: '用户管理'\n  //   },\n  //   children: [\n  //     {\n  //       path: 'aaas',\n  //       name: 'videosExampleVideo1',\n  //       component: () => import('@/views/videos_example/1'),\n  //       meta: {\n  //         title: '住户信息管理',\n  //       }\n  //     },\n  //     {\n  //       path: 'aaas',\n  //       name: 'videosExampleVideo1',\n  //       component: () => import('@/views/videos_example/1'),\n  //       meta: {\n  //         title: '物业管理人员管理',\n  //       }\n  //     },\n  //   ]\n  // },\n  // {\n  //   path: '/videos_example/1',\n  //   component: Layout,\n  //   redirect: '/videos_example/aaas',\n  //   meta: {\n  //     title: '系统管理'\n  //   },\n  //   children: [\n  //     {\n  //       path: 'aaas',\n  //       name: 'videosExampleVideo1',\n  //       component: () => import('@/views/videos_example/1'),\n  //       meta: {\n  //         title: '定时任务管理',\n  //       }\n  //     },\n  //     {\n  //       path: 'aaas',\n  //       name: 'videosExampleVideo1',\n  //       component: () => import('@/views/videos_example/1'),\n  //       meta: {\n  //         title: '编辑管理员',\n  //       }\n  //     },\n  //   ]\n  // },\n]\n"], "mappings": ";;;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,eAAe;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,MAAM;IACZM,IAAI,EAAE,WAAW;IACjBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kCAAkC;MAAA;IAAA,CAAC;IAC3DR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,WAAW;IACjBM,IAAI,EAAE,gBAAgB;IACtBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uCAAuC;MAAA;IAAA,CAAC;IAChER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAGL,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,WAAW;EACrBC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,KAAK;IACXM,IAAI,EAAE,UAAU;IAChBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,YAAY;IAClBM,IAAI,EAAE,UAAU;IAChBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DR,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAGL,CAAC,EACD;EACEJ,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,kBAAkB;EAC5BC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,KAAK;IACXM,IAAI,EAAE,WAAW;IACjBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,8CAA8C;MAAA;IAAA,CAAC;IACvER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,SAAS;IACfM,IAAI,EAAE,WAAW;IACjBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,kDAAkD;MAAA;IAAA,CAAC;IAC3ER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAGL,CAAC,EACD;EACEJ,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEF,MAAM;EACjBG,QAAQ,EAAE,qBAAqB;EAC/BC,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,KAAK;IACXM,IAAI,EAAE,cAAc;IACpBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,iDAAiD;MAAA;IAAA,CAAC;IAC1ER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC,EACD;IACEJ,IAAI,EAAE,SAAS;IACfM,IAAI,EAAE,cAAc;IACpBL,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAM,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,qDAAqD;MAAA;IAAA,CAAC;IAC9ER,IAAI,EAAE;MACJC,KAAK,EAAE;IACT;EACF,CAAC;AAGL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}