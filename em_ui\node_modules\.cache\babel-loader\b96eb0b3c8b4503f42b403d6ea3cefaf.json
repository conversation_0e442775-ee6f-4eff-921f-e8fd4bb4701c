{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u57FA\\u4E8ESpringBoot+Vue\\u5B9E\\u73B0\\u7684\\u5C0F\\u533A\\u7269\\u4E1A\\u7BA1\\u7406\\u7CFB\\u7EDF/em_ui/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport { getHousehold, saveHousehold, deleteHousehold, downloadHouseholds, getUsers, registerUser } from '@/api/requests/rq-manage.js';\nimport { Success, Warning } from '@/util/message.js';\nimport { download } from '@/util/download.js';\nimport { rTime } from '@/util/time.js';\nexport default {\n  data: function data() {\n    return {\n      rTime: rTime,\n      loading: false,\n      labelCol: {\n        span: 7\n      },\n      wrapperCol: {\n        span: 7\n      },\n      table_selectedRowKeys: [],\n      household_query_type: 'userName',\n      household_query_buttonTitle: '搜索',\n      household_query_text: '',\n      household_save_title: '新增住户信息',\n      household_save_modalVisible: false,\n      household_form_data: {},\n      household_data_list: [],\n      rules: {\n        userName: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        fullName: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }],\n        roomId: [{\n          required: true,\n          message: '此项为必填项',\n          trigger: 'blur'\n        }]\n      },\n      user_data_list: [],\n      household_form_search_userNames: [],\n      household_form_userName_isChoice: false,\n      household_user_register_from: {}\n    };\n  },\n  created: function created() {\n    this.Get_householdDataList();\n    this.Get_users();\n  },\n  watch: {\n    household_save_modalVisible: function household_save_modalVisible(val) {\n      if (!val) {\n        this.household_form_data = {};\n      }\n    }\n  },\n  methods: {\n    Get_householdDataList: function Get_householdDataList() {\n      var _this = this;\n      getHousehold().then(function (res) {\n        _this.household_query_buttonTitle = '搜索';\n        _this.household_data_list = res.data;\n        _this.household_save_title = '新增住户信息';\n      });\n    },\n    Get_users: function Get_users() {\n      var _this2 = this;\n      getUsers().then(function (res) {\n        _this2.user_data_list = res.data;\n      });\n    },\n    Query_householdDataList: function Query_householdDataList() {\n      var _this3 = this;\n      var text = this.household_query_text;\n      var temp_list = [];\n      this.household_data_list.forEach(function (item) {\n        if (item[_this3.household_query_type].indexOf(text) != -1) {\n          temp_list.push(item);\n        }\n      });\n      this.household_query_buttonTitle = '返回';\n      this.household_data_list = temp_list;\n    },\n    Edit_householdData: function Edit_householdData(form) {\n      this.household_save_title = '编辑住户信息';\n      this.household_form_data = JSON.parse(JSON.stringify(form));\n      this.household_save_modalVisible = true;\n    },\n    Del_householdData: function Del_householdData(id) {\n      var _this4 = this;\n      deleteHousehold(id).then(function (res) {\n        if (res.code == 200) {\n          Success(_this4, '操作成功');\n        } else {\n          Warning(_this4, '操作失败');\n        }\n        _this4.Get_householdDataList();\n      });\n    },\n    Del_batchData: function Del_batchData() {\n      var _this5 = this;\n      this.table_selectedRowKeys.forEach(function (i) {\n        _this5.Del_householdData(_this5.household_data_list[i].id);\n      });\n      this.table_selectedRowKeys = [];\n    },\n    Save_householdData: function Save_householdData() {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var userId, b;\n        return _regeneratorRuntime().wrap(function (_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              userId = null;\n              if (_this6.household_form_userName_isChoice) {\n                _context.next = 2;\n                break;\n              }\n              _this6.household_user_register_from.fullName = _this6.household_form_data.fullName;\n              _this6.household_user_register_from.phone = _this6.household_form_data.phone;\n              _this6.household_user_register_from.password = '123456';\n              _context.next = 1;\n              return _this6.Register_User(_this6.household_user_register_from);\n            case 1:\n              b = _context.sent;\n              console.log(b);\n              if (b != false) {\n                userId = b.userId;\n              }\n            case 2:\n              if (userId != null) {\n                _this6.household_form_data.userId = userId;\n              }\n              saveHousehold(_this6.household_form_data).then(function (res) {\n                if (res.code == 200) {\n                  Success(_this6, '操作成功');\n                } else {\n                  Warning(_this6, '操作失败');\n                }\n                _this6.household_save_modalVisible = false;\n                _this6.Get_householdDataList();\n              });\n            case 3:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))();\n    },\n    Download_householdsExcel: function Download_householdsExcel() {\n      downloadHouseholds().then(function (res) {\n        download('社区住户信息.xlsx', res.data);\n      });\n    },\n    Table_selectChange: function Table_selectChange(selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    Register_User: function Register_User(from) {\n      var _this7 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        return _regeneratorRuntime().wrap(function (_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _this7.$message.info('系统自动为此账户注册，默认密码为「123456」');\n              _context2.next = 1;\n              return registerUser(from).then(function (res) {\n                return res.data;\n              });\n            case 1:\n              return _context2.abrupt(\"return\", _context2.sent);\n            case 2:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }))();\n    },\n    Users_choice_onSelect: function Users_choice_onSelect(value) {\n      var _this8 = this;\n      var flag = false;\n      this.user_data_list.forEach(function (item) {\n        if (item.userName.indexOf(value) != -1) {\n          flag = true;\n          _this8.household_form_data.userId = item.id;\n        }\n      });\n    },\n    Users_choice_handleSearch: function Users_choice_handleSearch(value) {\n      var _this9 = this;\n      var flag = false;\n      this.household_form_search_userNames = [];\n      this.user_data_list.forEach(function (item) {\n        if (item.userName.indexOf(value) != -1) {\n          flag = true;\n          _this9.household_form_userName_isChoice = true;\n          _this9.household_form_search_userNames.push(item.userName);\n        }\n      });\n      if (!flag) {\n        this.household_user_register_from.userName = value;\n        this.household_form_userName_isChoice = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "mappings": ";;;;;;;;;AAqHA;AACA;AACA;AACA;AAEA;EACAA;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;QAAAD;MAAA;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;UAAAC;UAAAC;UAAAC;QAAA;QACAC;UAAAH;UAAAC;UAAAC;QAAA;QACAE;UAAAJ;UAAAC;UAAAC;QAAA;QACAG;UAAAL;UAAAC;UAAAC;QAAA;MACA;MACAI;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAhB;MACA;QACA;MACA;IACA;EACA;EACAiB;IACAC;MAAA;MACAC;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MAAA;MACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;UACAC;QACA;UACAC;QACA;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cACAC;cAAA,IACAC;gBAAAC;gBAAA;cAAA;cACAD;cACAA;cACAA;cAAAC;cAAA,OACAD;YAAA;cAAAE;cACAC;cAEA;gBACAJ;cACA;YAAA;cAEA;gBACAC;cACA;cACAI;gBACA;kBACAX;gBACA;kBACAC;gBACA;gBACAM;gBACAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAGA;IACAK;MACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cAAAC;cAAA,OACAC;gBACA;cACA;YAAA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MACA;MACA;QACA;UACAC;UACAC;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;MACA;QACA;UACAF;UACAG;UACAA;QACA;MACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA", "names": ["data", "rTime", "loading", "labelCol", "span", "wrapperCol", "table_selected<PERSON><PERSON><PERSON><PERSON>s", "household_query_type", "household_query_buttonTitle", "household_query_text", "household_save_title", "household_save_modalVisible", "household_form_data", "household_data_list", "rules", "userName", "required", "message", "trigger", "fullName", "phone", "roomId", "user_data_list", "household_form_search_userNames", "household_form_userName_isChoice", "household_user_register_from", "created", "watch", "methods", "Get_householdDataList", "getHousehold", "_this", "Get_users", "getUsers", "_this2", "Query_householdDataList", "temp_list", "Edit_householdData", "Del_householdData", "deleteHousehold", "Success", "Warning", "_this4", "Del_batchData", "_this5", "Save_householdData", "userId", "_this6", "_context", "b", "console", "saveHousehold", "Download_householdsExcel", "downloadHouseholds", "download", "Table_selectChange", "Register_User", "_this7", "_context2", "registerUser", "Users_choice_onSelect", "flag", "_this8", "Users_choice_handleSearch", "_this9"], "sourceRoot": "src/views/admin/user", "sources": ["user_household.vue"], "sourcesContent": ["<template>\n  <page-main>\n    <a-card :loading=\"loading\" title=\"社区住户信息管理\">\n      <div class=\"head\">\n        <span>搜索类型</span>\n        <a-select\n          size=\"large\"\n          default-value=\"住户信息名称\"\n          v-model=\"household_query_type\"\n          style=\"width: 200px;\"\n        >\n          <a-select-option value=\"userName\">用户名</a-select-option>\n          <a-select-option value=\"fullName\">真实姓名</a-select-option>\n          <a-select-option value=\"buildingName\">所在楼宇</a-select-option>\n          <a-select-option value=\"unitName\">所在单元</a-select-option>\n          <a-select-option value=\"roomId\">房间编号</a-select-option>\n        </a-select>\n        <a-input-search\n          v-model=\"household_query_text\"\n          placeholder=\"输入要搜索的文本\"\n          :enter-button=\"household_query_buttonTitle\"\n          size=\"large\"\n          @search=\"household_query_buttonTitle == '搜索' ? Query_householdDataList() : Get_householdDataList()\"\n        />\n        <a-button\n          type=\"primary\"\n          style=\"height: 40px;\"\n          @click=\"household_save_modalVisible = true\"\n        >添加住户信息</a-button>\n        <a-button\n          type=\"primary\"\n          style=\"height: 40px; margin-left: 10px;\"\n          @click=\"Download_householdsExcel()\"\n        >导出Excel</a-button>\n        <a-button\n          type=\"danger\"\n          v-if=\"table_selectedRowKeys.length > 0\"\n          style=\"height: 38px; margin-left: 10px;\"\n          @click=\"Del_batchData\"\n        >删除被选择的「住户信息」</a-button>\n      </div>\n      <a-table\n        :data-source=\"household_data_list\"\n        :row-selection=\"{ selectedRowKeys: table_selectedRowKeys, onChange: Table_selectChange }\"\n      >\n        <a-table-column key=\"userName\" title=\"用户名\" data-index=\"userName\" />\n        <a-table-column key=\"fullName\" title=\"真实姓名\" data-index=\"fullName\" />\n        <a-table-column key=\"phone\" title=\"联系电话\" data-index=\"phone\" />\n        <a-table-column key=\"buildingName\" title=\"所在楼宇\" data-index=\"buildingName\" />\n        <a-table-column key=\"unitName\" title=\"所在单元\" data-index=\"unitName\" />\n        <a-table-column key=\"roomId\" title=\"房间编号\" data-index=\"roomId\" />\n\n        <a-table-column key=\"action\" title=\"操作\">\n          <template slot-scope=\"text, record\">\n            <a-button-group>\n              <a-button type=\"primary\" @click=\"Edit_householdData(record)\">编辑</a-button>\n              <a-button type=\"danger\" @click=\"Del_householdData(record.id)\">删除</a-button>\n            </a-button-group>\n          </template>\n        </a-table-column>\n      </a-table>\n    </a-card>\n    <!-- 新增或保存设施提示框 -->\n    <a-modal\n      v-model=\"household_save_modalVisible\"\n      :title=\"household_save_title\"\n      ok-text=\"确认\"\n      cancel-text=\"取消\"\n      :maskClosable=\"false\"\n      :destroyOnClose=\"false\"\n      @ok=\"Save_householdData\"\n    >\n      <a-form-model\n        :model=\"household_form_data\"\n        :rules=\"rules\"\n        :label-col=\"labelCol\"\n        :wrapper-col=\"wrapperCol\"\n      >\n        <a-form-model-item label=\"选择用户\" prop=\"userName\">\n          <div class=\"global-search-wrapper\">\n            <a-auto-complete\n              class=\"global-search\"\n              size=\"large\"\n              style=\"width: 100%;\"\n              placeholder=\"选择目标用户\"\n              option-label-prop=\"title\"\n              @select=\"Users_choice_onSelect\"\n              @search=\"Users_choice_handleSearch\"\n            >\n              <template slot=\"dataSource\">\n                <a-select-option\n                  v-for=\"item in household_form_search_userNames\"\n                  :key=\"item\"\n                  :title=\"item\"\n                >\n                  <span class=\"global-search-item-count\">{{ item }}</span>\n                </a-select-option>\n              </template>\n              <a-input v-model=\"household_form_data.userName\"></a-input>\n            </a-auto-complete>\n          </div>\n        </a-form-model-item>\n        <a-form-model-item label=\"选择房间\" prop=\"roomId\">\n          <a-input v-model=\"household_form_data.roomId\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"真实姓名\" prop=\"fullName\">\n          <a-input v-model=\"household_form_data.fullName\" />\n        </a-form-model-item>\n        <a-form-model-item label=\"住户电话\" prop=\"phone\">\n          <a-input v-model=\"household_form_data.phone\" />\n        </a-form-model-item>\n      </a-form-model>\n    </a-modal>\n  </page-main>\n</template>\n\n<script>\nimport { getHousehold, saveHousehold, deleteHousehold, downloadHouseholds, getUsers, registerUser } from '@/api/requests/rq-manage.js'\nimport { Success, Warning } from '@/util/message.js'\nimport { download } from '@/util/download.js'\nimport { rTime } from '@/util/time.js'\n\nexport default {\n  data () {\n    return {\n      rTime,\n      loading: false,\n      labelCol: { span: 7 },\n      wrapperCol: { span: 7 },\n      table_selectedRowKeys: [],\n      household_query_type: 'userName',\n      household_query_buttonTitle: '搜索',\n      household_query_text: '',\n      household_save_title: '新增住户信息',\n      household_save_modalVisible: false,\n      household_form_data: {},\n      household_data_list: [],\n      rules: {\n        userName: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        fullName: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        phone: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n        roomId: [{ required: true, message: '此项为必填项', trigger: 'blur' }],\n      },\n      user_data_list: [],\n      household_form_search_userNames: [],\n      household_form_userName_isChoice: false,\n      household_user_register_from: {},\n    }\n  },\n  created () {\n    this.Get_householdDataList()\n    this.Get_users()\n  },\n  watch: {\n    household_save_modalVisible (val) {\n      if (!val) {\n        this.household_form_data = {}\n      }\n    }\n  },\n  methods: {\n    Get_householdDataList () {\n      getHousehold().then(res => {\n        this.household_query_buttonTitle = '搜索'\n        this.household_data_list = res.data\n        this.household_save_title = '新增住户信息'\n      })\n    },\n    Get_users () {\n      getUsers().then(res => {\n        this.user_data_list = res.data\n      })\n    },\n    Query_householdDataList () {\n      let text = this.household_query_text\n      let temp_list = []\n      this.household_data_list.forEach(item => {\n        if (item[this.household_query_type].indexOf(text) != -1) {\n          temp_list.push(item)\n        }\n      })\n      this.household_query_buttonTitle = '返回'\n      this.household_data_list = temp_list\n    },\n    Edit_householdData (form) {\n      this.household_save_title = '编辑住户信息'\n      this.household_form_data = JSON.parse(JSON.stringify(form))\n      this.household_save_modalVisible = true\n    },\n    Del_householdData (id) {\n      deleteHousehold(id).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.Get_householdDataList()\n      })\n    },\n    Del_batchData () {\n      this.table_selectedRowKeys.forEach(i => {\n        this.Del_householdData(this.household_data_list[i].id)\n      })\n      this.table_selectedRowKeys = []\n    },\n    async Save_householdData () {\n      let userId = null\n      if (!this.household_form_userName_isChoice) {\n        this.household_user_register_from.fullName = this.household_form_data.fullName\n        this.household_user_register_from.phone = this.household_form_data.phone\n        this.household_user_register_from.password = '123456'\n        let b = await this.Register_User(this.household_user_register_from)\n        console.log(b)\n\n        if (b != false) {\n          userId = b.userId\n        }\n      }\n      if (userId != null) {\n        this.household_form_data.userId = userId\n      }\n      saveHousehold(this.household_form_data).then(res => {\n        if (res.code == 200) {\n          Success(this, '操作成功')\n        } else {\n          Warning(this, '操作失败')\n        }\n        this.household_save_modalVisible = false\n        this.Get_householdDataList()\n      })\n\n\n    },\n    Download_householdsExcel () {\n      downloadHouseholds().then(res => {\n        download('社区住户信息.xlsx', res.data)\n      })\n    },\n    Table_selectChange (selectedRowKeys) {\n      this.table_selectedRowKeys = selectedRowKeys;\n    },\n    async Register_User (from) {\n      this.$message.info('系统自动为此账户注册，默认密码为「123456」')\n      return await registerUser(from).then(res => {\n        return res.data\n      })\n\n    },\n    Users_choice_onSelect (value) {\n      let flag = false\n      this.user_data_list.forEach(item => {\n        if (item.userName.indexOf(value) != -1) {\n          flag = true\n          this.household_form_data.userId = item.id\n        }\n      })\n    },\n\n    Users_choice_handleSearch (value) {\n      let flag = false\n      this.household_form_search_userNames = []\n      this.user_data_list.forEach(item => {\n        if (item.userName.indexOf(value) != -1) {\n          flag = true\n          this.household_form_userName_isChoice = true\n          this.household_form_search_userNames.push(item.userName)\n        }\n      })\n      if (!flag) {\n        this.household_user_register_from.userName = value\n        this.household_form_userName_isChoice = false\n      }\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head {\n    display: flex;\n    justify-content: flex-start;\n    margin-bottom: 10px;\n    span {\n        line-height: 40px;\n        margin-right: 10px;\n    }\n    .ant-input-search {\n        width: 30%;\n        margin-left: 10px;\n    }\n}\n.ant-modal-content {\n    width: 24vw;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}