{"ast": null, "code": "import \"core-js/modules/es.array.from.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };", "map": {"version": 3, "names": ["arrayLikeToArray", "_unsupportedIterableToArray", "r", "a", "t", "toString", "call", "slice", "constructor", "name", "Array", "from", "test", "default"], "sources": ["F:/基于SpringBoot+Vue实现的小区物业管理系统/em_ui/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js"], "sourcesContent": ["import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };"], "mappings": ";;;;;;;;AAAA,OAAOA,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,2BAA2BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAID,CAAC,EAAE;IACL,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOF,gBAAgB,CAACE,CAAC,EAAEC,CAAC,CAAC;IACvD,IAAIC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,OAAO,QAAQ,KAAKH,CAAC,IAAIF,CAAC,CAACM,WAAW,KAAKJ,CAAC,GAAGF,CAAC,CAACM,WAAW,CAACC,IAAI,CAAC,EAAE,KAAK,KAAKL,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGM,KAAK,CAACC,IAAI,CAACT,CAAC,CAAC,GAAG,WAAW,KAAKE,CAAC,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,GAAGJ,gBAAgB,CAACE,CAAC,EAAEC,CAAC,CAAC,GAAG,KAAK,CAAC;EAC5N;AACF;AACA,SAASF,2BAA2B,IAAIY,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}