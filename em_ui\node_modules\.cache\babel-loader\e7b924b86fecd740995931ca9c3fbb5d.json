{"ast": null, "code": "import \"core-js/modules/es.array.includes.js\";\nexport default {\n  name: 'Result',\n  props: {\n    type: {\n      type: String,\n      validator: function validator(val) {\n        return ['success', 'warning', 'error'].includes(val);\n      },\n      required: true\n    },\n    title: {\n      type: String,\n      required: true\n    },\n    desc: {\n      type: String,\n      default: ''\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAuBA;EACAA;EACAC;IACAC;MACAA;MACAC;QAAA;MAAA;MACAC;IACA;IACAC;MACAH;MACAE;IACA;IACAE;MACAJ;MACAK;IACA;EACA;AACA", "names": ["name", "props", "type", "validator", "required", "title", "desc", "default"], "sourceRoot": "src/components/Result", "sources": ["index.vue"], "sourcesContent": ["<template>\n    <div class=\"result\">\n        <div v-if=\"type === 'success'\" class=\"icon icon-success\">\n            <i class=\"el-icon-success\" />\n        </div>\n        <div v-else-if=\"type === 'warning'\" class=\"icon icon-warning\">\n            <i class=\"el-icon-warning\" />\n        </div>\n        <div v-else class=\"icon icon-error\">\n            <i class=\"el-icon-error\" />\n        </div>\n        <h1>{{ title }}</h1>\n        <div v-if=\"desc\" class=\"desc\">{{ desc }}</div>\n        <div v-if=\"$slots.extra\" class=\"extra\">\n            <slot name=\"extra\" />\n        </div>\n        <div v-if=\"$slots.default\" class=\"actions\">\n            <slot />\n        </div>\n    </div>\n</template>\n\n<script>\nexport default {\n    name: 'Result',\n    props: {\n        type: {\n            type: String,\n            validator: val => ['success', 'warning', 'error'].includes(val),\n            required: true\n        },\n        title: {\n            type: String,\n            required: true\n        },\n        desc: {\n            type: String,\n            default: ''\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n$success: #67c23a;\n$warning: #e6a23c;\n$error: #f56c6c;\n.result {\n    width: 72%;\n    margin: 20px auto 0;\n    text-align: center;\n    .icon {\n        i {\n            font-size: 80px;\n        }\n        &-success i {\n            color: $success;\n        }\n        &-warning i {\n            color: $warning;\n        }\n        &-error i {\n            color: $error;\n        }\n    }\n    h1 {\n        margin: 20px 0;\n        font-size: 24px;\n        font-weight: normal;\n    }\n    .desc {\n        color: #909399;\n        margin-bottom: 20px;\n    }\n    .extra {\n        margin: 50px 0;\n        padding: 24px 40px;\n        text-align: left;\n        color: #606266;\n        background: #f8f8f9;\n        border-radius: 4px;\n    }\n    .actions {\n        margin-bottom: 20px;\n    }\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}